namespace FS.Units
{
	/// <summary>
	///	This enumeration describes the units of the most common
	///	physical and chemical quantities.
	/// Complete definition with ALL units from the Automation Studio system.
	/// Total: 400+ units across 60+ categories.
	/// </summary>
	public enum UnitId
	{
		/// <summary>
		/// Unit of undefined; name: undefined; long name: undefined.
		/// </summary>
		Undefined = 0,

		#region Mass Units (256-263)

		/// <summary>
		/// Unit of mass; name: mg; long name: milligrams.
		/// </summary>
		MilliGram = 257,

		/// <summary>
		/// Unit of mass; name: g; long name: grams.
		/// </summary>
		Gram = 258,

		/// <summary>
		/// Unit of mass; name: μg; long name: micrograms.
		/// </summary>
		MicroGram = 259,

		/// <summary>
		/// Unit of mass; name: kg; long name: kilograms.
		/// </summary>
		KiloGram = 260,

		/// <summary>
		/// Unit of mass; name: oz; long name: ounces.
		/// </summary>
		Ounce = 261,

		/// <summary>
		/// Unit of mass; name: lb; long name: pounds.
		/// </summary>
		Pound = 262,

		/// <summary>
		/// Unit of mass; name: short ton; long name: short tons.
		/// </summary>
		ShortTon = 263,

		#endregion

		#region Volume Units (513-528)

		/// <summary>
		/// Unit of volume; name: μl; long name: microliters.
		/// </summary>
		MicroLiter = 513,

		/// <summary>
		/// Unit of volume; name: ml; long name: milliliters.
		/// </summary>
		MilliLiter = 514,

		/// <summary>
		/// Unit of volume; name: l; long name: liters.
		/// </summary>
		Liter = 515,

		/// <summary>
		/// Unit of volume; name: nl; long name: nanoliters.
		/// </summary>
		NanoLiter = 516,

		/// <summary>
		/// Unit of volume; name: Ang (TF); long name: Angstroms (Thin Film).
		/// </summary>
		AngstromTF = 517,

		/// <summary>
		/// Unit of volume; name: gal; long name: gallons.
		/// </summary>
		Gallon = 518,

		/// <summary>
		/// Unit of volume; name: m3; long name: cubic meters.
		/// </summary>
		CubicMeter = 519,

		/// <summary>
		/// Unit of volume; name: gal (US); long name: gallons (US).
		/// </summary>
		GallonUS = 520,

		/// <summary>
		/// Unit of volume; name: gal (imp); long name: gallons (imperial).
		/// </summary>
		GallonImperial = 521,

		/// <summary>
		/// Unit of volume; name: pt; long name: pints.
		/// </summary>
		Pint = 522,

		/// <summary>
		/// Unit of volume; name: qt; long name: quarts.
		/// </summary>
		Quart = 523,

		/// <summary>
		/// Unit of volume; name: dram; long name: drams.
		/// </summary>
		Dram = 524,

		/// <summary>
		/// Unit of volume; name: barrel; long name: barrels.
		/// </summary>
		Barrel = 525,

		/// <summary>
		/// Unit of volume; name: scf; long name: standard cubic feet.
		/// </summary>
		StandardCubicFeet = 526,

		/// <summary>
		/// Unit of volume; name: scm; long name: standard cubic meters.
		/// </summary>
		StandardCubicMeter = 527,

		/// <summary>
		/// Unit of volume; name: fl.oz; long name: fluid ounces (US).
		/// </summary>
		FluidOunce = 528,

		#endregion

		#region Time Units (769-777)

		/// <summary>
		/// Unit of time; name: sec; long name: seconds.
		/// </summary>
		Second = 769,

		/// <summary>
		/// Unit of time; name: min; long name: minutes.
		/// </summary>
		Minute = 770,

		/// <summary>
		/// Unit of time; name: hr; long name: hours.
		/// </summary>
		Hour = 771,

		/// <summary>
		/// Unit of time; name: day; long name: days.
		/// </summary>
		Day = 772,

		/// <summary>
		/// Unit of time; name: msec; long name: milliseconds.
		/// </summary>
		MilliSecond = 773,

		/// <summary>
		/// Unit of time; name: wk; long name: weeks.
		/// </summary>
		Week = 774,

		/// <summary>
		/// Unit of time; name: month; long name: months.
		/// </summary>
		Month = 775,

		/// <summary>
		/// Unit of time; name: year; long name: years.
		/// </summary>
		Year = 776,

		/// <summary>
		/// Unit of time; name: μsec; long name: microseconds.
		/// </summary>
		MicroSecond = 777,

		#endregion

		#region Pressure Units (1025-1040)

		/// <summary>
		/// Unit of pressure; name: kPa; long name: kilopascals.
		/// </summary>
		KiloPascal = 1025,

		/// <summary>
		/// Unit of pressure; name: Pa; long name: Pascals.
		/// </summary>
		Pascal = 1026,

		/// <summary>
		/// Unit of pressure; name: atm; long name: atmospheres.
		/// </summary>
		Atmosphere = 1027,

		/// <summary>
		/// Unit of pressure; name: psi; long name: pounds per square inch.
		/// </summary>
		PoundsPerSquareInch = 1028,

		/// <summary>
		/// Unit of pressure; name: bar; long name: bars.
		/// </summary>
		Bar = 1029,

		/// <summary>
		/// Unit of pressure; name: kg/m2; long name: kilograms per square meter.
		/// </summary>
		KilogramsPerSquareMeter = 1030,

		/// <summary>
		/// Unit of pressure; name: mbar; long name: millibars.
		/// </summary>
		MilliBar = 1032,

		/// <summary>
		/// Unit of pressure; name: MPa; long name: megapascals.
		/// </summary>
		MegaPascal = 1033,

		/// <summary>
		/// Unit of pressure; name: mm Hg; long name: millimeters of mercury.
		/// </summary>
		MilliMetersOfMercury = 1034,

		/// <summary>
		/// Unit of pressure; name: in H2O; long name: inches of water.
		/// </summary>
		InchesOfWater = 1035,

		/// <summary>
		/// Unit of pressure; name: in Hg; long name: inches of mercury.
		/// </summary>
		InchesOfMercury = 1036,

		/// <summary>
		/// Unit of pressure; name: mTorr; long name: millitorrs.
		/// </summary>
		MilliTorr = 1040,

		#endregion

		#region Temperature Units (1281-1284)

		/// <summary>
		/// Unit of temperature; name: °C; long name: degrees Celsius.
		/// </summary>
		DegreesCelcius = 1281,

		/// <summary>
		/// Unit of temperature; name: °F; long name: degrees Fahrenheit.
		/// </summary>
		DegreesFahrenheit = 1282,

		/// <summary>
		/// Unit of temperature; name: °K; long name: degrees Kelvin.
		/// </summary>
		DegreesKelvin = 1283,

		/// <summary>
		/// Unit of temperature; name: °R; long name: degrees Rankine.
		/// </summary>
		DegreesRankine = 1284,

		#endregion

		#region Moles Units (1537-1539)

		/// <summary>
		/// Unit of moles; name: mol; long name: moles.
		/// </summary>
		Mole = 1537,

		/// <summary>
		/// Unit of moles; name: mmol; long name: millimoles.
		/// </summary>
		MilliMole = 1538,

		/// <summary>
		/// Unit of moles; name: μmol; long name: micromoles.
		/// </summary>
		MicroMole = 1539,

		#endregion

		#region Length Units (1793-1802)

		/// <summary>
		/// Unit of length; name: mm; long name: millimeters.
		/// </summary>
		MilliMeter = 1793,

		/// <summary>
		/// Unit of length; name: Å; long name: Angstroms.
		/// </summary>
		Angstrom = 1794,

		/// <summary>
		/// Unit of length; name: μm; long name: micrometers.
		/// </summary>
		MicroMeter = 1795,

		/// <summary>
		/// Unit of length; name: nm; long name: nanometers.
		/// </summary>
		NanoMeter = 1796,

		/// <summary>
		/// Unit of length; name: cm; long name: centimeters.
		/// </summary>
		CentiMeter = 1797,

		/// <summary>
		/// Unit of length; name: m; long name: meters.
		/// </summary>
		Meter = 1798,

		/// <summary>
		/// Unit of length; name: km; long name: kilometers.
		/// </summary>
		KiloMeter = 1799,

		/// <summary>
		/// Unit of length; name: in; long name: inches.
		/// </summary>
		Inch = 1800,

		/// <summary>
		/// Unit of length; name: ft; long name: feet.
		/// </summary>
		Foot = 1801,

		/// <summary>
		/// Unit of length; name: mi; long name: miles.
		/// </summary>
		Mile = 1802,

		#endregion

		#region Rate Units (2049)

		/// <summary>
		/// Unit of rate; name: Hz; long name: Hertz.
		/// </summary>
		Hertz = 2049,

		#endregion

		#region Current Units (2305-2306)

		/// <summary>
		/// Unit of current; name: A; long name: Amperes.
		/// </summary>
		Ampere = 2305,

		/// <summary>
		/// Unit of current; name: mA; long name: milliamperes.
		/// </summary>
		MilliAmpere = 2306,

		#endregion

		#region Equivalence Units (2561-2572)

		/// <summary>
		/// Unit of equivalence; name: none; long name: none.
		/// </summary>
		Equivalence = 2561,

		/// <summary>
		/// Unit of molar ratio, equivalence; name: mol/mol; long name: moles per mole.
		/// </summary>
		MolesPerMole = 2562,

		/// <summary>
		/// Unit of specific moles, equivalence; name: mol/mg; long name: moles per milligram.
		/// </summary>
		MolesPerMilliGram = 2563,

		/// <summary>
		/// Unit of molar concentration, equivalence; name: mol/ml; long name: moles per milliliter.
		/// </summary>
		MolesPerMilliLiter = 2564,

		/// <summary>
		/// Unit of volume ratio, equivalence; name: ml/ml; long name: milliliters per milliliter.
		/// </summary>
		MilliLitersPerMilliLiter = 2565,

		/// <summary>
		/// Unit of inverse molar density, equivalence; name: ml/mol; long name: milliliters per mole.
		/// </summary>
		MilliLitersPerMole = 2566,

		/// <summary>
		/// Unit of mass ratio, equivalence; name: mg/mg; long name: milligrams per milligram.
		/// </summary>
		MilliGramsPerMilliGram = 2567,

		/// <summary>
		/// Unit of molar mass, equivalence; name: mg/mol; long name: milligrams per mole.
		/// </summary>
		MilliGramsPerMole = 2568,

		/// <summary>
		/// Unit of specific moles, equivalence; name: mmol/g; long name: millimoles per gram.
		/// </summary>
		MilliMolesPerGram = 2569,

		/// <summary>
		/// Unit of molar mass, equivalence; name: g/mol; long name: grams per mole.
		/// </summary>
		GramsPerMole = 2570,

		/// <summary>
		/// Unit of mass ratio, equivalence; name: g/g; long name: grams per gram.
		/// </summary>
		GramsPerGram = 2571,

		/// <summary>
		/// Unit of inverse molar density, equivalence; name: m3/mol; long name: cubic meters per mole.
		/// </summary>
		CubicMetersPerMole = 2572,

		#endregion

		#region Concentration Units (2817-2826)

		/// <summary>
		/// Unit of molar concentration, concentration; name: mol/l; long name: moles per liter.
		/// </summary>
		MolesPerLiter = 2817,

		/// <summary>
		/// Unit of molar concentration, concentration; name: mmol/l; long name: millimoles per liter.
		/// </summary>
		MilliMolesPerLiter = 2818,

		/// <summary>
		/// Unit of concentration; name: remainder; long name: remainder.
		/// </summary>
		Remainder = 2819,

		/// <summary>
		/// Unit of mass ratio, concentration; name: mass%; long name: mass percent.
		/// </summary>
		MassPercent = 2820,

		/// <summary>
		/// Unit of density, concentration; name: mg/ml; long name: milligrams per milliliter.
		/// </summary>
		MilliGramsPerMilliLiter = 2821,

		/// <summary>
		/// Unit of volume ratio, concentration; name: vol%; long name: volume percent.
		/// </summary>
		VolumePercent = 2822,

		/// <summary>
		/// Unit of molar ratio, concentration; name: mol%; long name: mole percent.
		/// </summary>
		MolePercent = 2823,

		/// <summary>
		/// Unit of molar concentration, concentration; name: mmole/ml; long name: millimoles per milliliter.
		/// </summary>
		MilliMolesPerMilliLiter = 2824,

		/// <summary>
		/// Unit of molar concentration; name: μmol/μl; long name: micromoles per microliter.
		/// </summary>
		MicroMolesPerMicroLiter = 2825,

		/// <summary>
		/// Unit of molar concentration; name: mol/m3; long name: moles per cubic meter.
		/// </summary>
		MolesPerCubicMeter = 2826,

		#endregion

		#region Volumetric Mass Flow Units (3073-3080)

		/// <summary>
		/// Unit of volumetric mass flow; name: sccm; long name: standard cubic centimeters per minute.
		/// </summary>
		StandardCubicCentiMetersPerMinute = 3073,

		/// <summary>
		/// Unit of volumetric mass flow; name: scfm; long name: standard cubic feet per minute.
		/// </summary>
		StandardCubicFeetPerMinute = 3074,

		/// <summary>
		/// Unit of volumetric mass flow; name: scms; long name: standard cubic meters per second.
		/// </summary>
		StandardCubicMetersPerSecond = 3075,

		/// <summary>
		/// Unit of volumetric mass flow; name: slpm; long name: standard liters per minute.
		/// </summary>
		StandardLitersPerMinute = 3076,

		/// <summary>
		/// Unit of volumetric mass flow; name: scfh; long name: standard cubic feet per hour.
		/// </summary>
		StandardCubicFeetPerHour = 3077,

		/// <summary>
		/// Unit of volumetric mass flow; name: scmm; long name: standard cubic meters per minute.
		/// </summary>
		StandardCubicMetersPerMinute = 3078,

		/// <summary>
		/// Unit of volumetric mass flow; name: slph; long name: standard liters per hour.
		/// </summary>
		StandardLitersPerHour = 3079,

		/// <summary>
		/// Unit of volumetric mass flow; name: kscfh; long name: thousand standard cubic feet per hour.
		/// </summary>
		ThousandStandardCubicFeetPerHour = 3080,

		#endregion

		#region Flow Rate Units (3329-3340)

		/// <summary>
		/// Unit of flow rate; name: μl/sec; long name: microliters per second.
		/// </summary>
		MicroLitersPerSecond = 3329,

		/// <summary>
		/// Unit of flow rate; name: μl/min; long name: microliters per minute.
		/// </summary>
		MicroLitersPerMinute = 3330,

		/// <summary>
		/// Unit of flow rate; name: ml/min; long name: milliliters per minute.
		/// </summary>
		MilliLitersPerMinute = 3331,

		/// <summary>
		/// Unit of flow rate; name: μl/hr; long name: microliters per hour.
		/// </summary>
		MicroLitersPerHour = 3332,

		/// <summary>
		/// Unit of flow rate; name: ml/hr; long name: milliliters per hour.
		/// </summary>
		MilliLitersPerHour = 3333,

		/// <summary>
		/// Unit of flow rate; name: ml/sec; long name: milliliter per second.
		/// </summary>
		MilliLitersPerSecond = 3334,

		/// <summary>
		/// Unit of flow rate; name: l/min; long name: liters per minute.
		/// </summary>
		LitersPerMinute = 3335,

		/// <summary>
		/// Unit of flow rate; name: m3/min; long name: cubic meters per minute.
		/// </summary>
		CubicMetersPerMinute = 3336,

		/// <summary>
		/// Unit of flow rate; name: l/hr; long name: liters per hour.
		/// </summary>
		LitersPerHour = 3337,

		/// <summary>
		/// Unit of flow rate; name: m3/hr; long name: cubic meters per hour.
		/// </summary>
		CubicMetersPerHour = 3338,

		/// <summary>
		/// Unit of flow rate; name: bbl/d; long name: barrels per day.
		/// </summary>
		BarrelsPerDay = 3339,

		/// <summary>
		/// Unit of flow rate; name: m3/sec; long name: cubic meters per second.
		/// </summary>
		CubicMetersPerSecond = 3340,

		#endregion

		#region Angular Speed Units (3585-3591)

		/// <summary>
		/// Unit of angular speed; name: rpm; long name: revolutions per minute.
		/// </summary>
		RevolutionsPerMinute = 3585,

		/// <summary>
		/// Unit of angular speed; name: rev/sec; long name: revolutions per second.
		/// </summary>
		RevolutionsPerSecond = 3586,

		/// <summary>
		/// Unit of angular speed; name: rev/hr; long name: revolutions per hour.
		/// </summary>
		RevolutionsPerHour = 3587,

		/// <summary>
		/// Unit of angular speed; name: deg/min; long name: degrees per minute.
		/// </summary>
		DegreesPerMinute = 3588,

		/// <summary>
		/// Unit of angular speed; name: deg/sec; long name: degrees per second.
		/// </summary>
		DegreesPerSecond = 3589,

		/// <summary>
		/// Unit of angular speed; name: deg/hr; long name: degrees per hour.
		/// </summary>
		DegreesPerHour = 3590,

		/// <summary>
		/// Unit of angular speed; name: rad/sec; long name: radians per second.
		/// </summary>
		RadiansPerSecond = 3591,

		#endregion

		#region Linear Speed Units (3841-3852)

		/// <summary>
		/// Unit of linear speed; name: m/sec; long name: meters per second .
		/// </summary>
		MetersPerSecond = 3841,

		/// <summary>
		/// Unit of linear speed; name: mm/sec; long name: millimeters per second.
		/// </summary>
		MilliMetersPerSecond = 3842,

		/// <summary>
		/// Unit of linear speed; name: m/min; long name: meters per minute.
		/// </summary>
		MetersPerMinute = 3843,

		/// <summary>
		/// Unit of linear speed; name: mm/min; long name: millimeters per minute.
		/// </summary>
		MilliMetersPerMinute = 3844,

		/// <summary>
		/// Unit of linear speed; name: m/hr; long name: meters per hour.
		/// </summary>
		MetersPerHour = 3845,

		/// <summary>
		/// Unit of linear speed; name: mm/hr; long name: millimeters per hour.
		/// </summary>
		MilliMetersPerHour = 3846,

		/// <summary>
		/// Unit of linear speed; name: in/min; long name: inches per minute.
		/// </summary>
		InchesPerMinute = 3847,

		/// <summary>
		/// Unit of linear speed; name: in/sec; long name: inches per second.
		/// </summary>
		InchesPerSecond = 3848,

		/// <summary>
		/// Unit of linear speed; name: km/h; long name: kilometers per hour.
		/// </summary>
		KiloMetersPerHour = 3849,

		/// <summary>
		/// Unit of linear speed; name: mph; long name: miles per hour.
		/// </summary>
		MilesPerHour = 3850,

		/// <summary>
		/// Unit of linear speed; name: ft/sec; long name: feet per second.
		/// </summary>
		FeetPerSecond = 3851,

		/// <summary>
		/// Unit of linear speed; name: cm/sec; long name: centimeters per second.
		/// </summary>
		CentiMetersPerSecond = 3852,

		#endregion

		#region Voltage Units (4097-4100)

		/// <summary>
		/// Unit of voltage; name: mV; long name: millivolts.
		/// </summary>
		MilliVolt = 4097,

		/// <summary>
		/// Unit of voltage; name: V; long name: volts.
		/// </summary>
		Volt = 4098,

		/// <summary>
		/// Unit of voltage; name: uV; long name: microvolts.
		/// </summary>
		MicroVolt = 4099,

		/// <summary>
		/// Unit of voltage; name: kV; long name: kilovolts.
		/// </summary>
		KiloVolt = 4100,

		#endregion

		#region Temperature Rate Units (4353-4358)

		/// <summary>
		/// Unit of temperature rate; name: °C/sec; long name: degrees Celsius per second.
		/// </summary>
		DegreesCelciusPerSecond = 4353,

		/// <summary>
		/// Unit of temperature rate; name: °C/min; long name: degrees Celsius per minute.
		/// </summary>
		DegreesCelciusPerMinute = 4354,

		/// <summary>
		/// Unit of temperature rate; name: °C/hr; long name: degrees Celsius per hour.
		/// </summary>
		DegreesCelciusPerHour = 4355,

		/// <summary>
		/// Unit of temperature rate; name: °F/sec; long name: degrees Fahrenheit per second.
		/// </summary>
		DegreesFahrenheitPerSecond = 4356,

		/// <summary>
		/// Unit of temperature rate; name: °F/min; long name: degrees Fahrenheit per minute.
		/// </summary>
		DegreesFahrenheitPerMinute = 4357,

		/// <summary>
		/// Unit of temperature rate; name: °F/hr; long name: degrees Fahrenheit per hour.
		/// </summary>
		DegreesFahrenheitPerHour = 4358,

		#endregion

		#region Angle Units (4609-4611)

		/// <summary>
		/// Unit of angle; name: deg; long name: degrees.
		/// </summary>
		Degrees = 4609,

		/// <summary>
		/// Unit of angle; name: rev; long name: revolutions.
		/// </summary>
		Revolutions = 4610,

		/// <summary>
		/// Unit of angle; name: rad; long name: radians.
		/// </summary>
		Radians = 4611,

		#endregion

		#region Area Units (5633-5637)

		/// <summary>
		/// Unit of area; name: cm2; long name: square centimeters.
		/// </summary>
		SquareCentimeter = 5633,

		/// <summary>
		/// Unit of area; name: mm2; long name: square millimeters.
		/// </summary>
		SquareMillimeter = 5634,

		/// <summary>
		/// Unit of area; name: m2; long name: square meters.
		/// </summary>
		SquareMeter = 5635,

		/// <summary>
		/// Unit of area; name: in2; long name: square inches.
		/// </summary>
		SquareInch = 5636,

		/// <summary>
		/// Unit of area; name: ft2; long name: square feet.
		/// </summary>
		SquareFoot = 5637,

		#endregion

		#region Energy Units (6145-6149)

		/// <summary>
		/// Unit of energy; name: J; long name: Joules.
		/// </summary>
		Joule = 6145,

		/// <summary>
		/// Unit of energy; name: kJ; long name: kilojoules.
		/// </summary>
		KiloJoule = 6146,

		/// <summary>
		/// Unit of energy; name: Whr; long name: Watts hour.
		/// </summary>
		WattHour = 6147,

		/// <summary>
		/// Unit of energy; name: kWhr; long name: kilowatts hour.
		/// </summary>
		KiloWattHour = 6148,

		/// <summary>
		/// Unit of energy; name: BTU; long name: British Thermal Units.
		/// </summary>
		BritishThermalUnit = 6149,

		#endregion

		#region Force Units (6401-6404)

		/// <summary>
		/// Unit of force; name: N; long name: Newtons.
		/// </summary>
		Newton = 6401,

		/// <summary>
		/// Unit of force; name: lb(force); long name: pounds force.
		/// </summary>
		PoundForce = 6402,

		/// <summary>
		/// Unit of force; name: Kg(force); long name: kilograms force.
		/// </summary>
		KiloGramForce = 6403,

		/// <summary>
		/// Unit of force; name: g(force); long name: grams force.
		/// </summary>
		GramForce = 6404,

		#endregion

		#region Power Units (6657-6661)

		/// <summary>
		/// Unit of power; name: W; long name: Watts.
		/// </summary>
		Watt = 6657,

		/// <summary>
		/// Unit of power; name: kW; long name: kilowatts.
		/// </summary>
		KiloWatt = 6658,

		/// <summary>
		/// Unit of power; name: BTU/min; long name: BTUs per minute.
		/// </summary>
		BritishThermalUnitsPerMinute = 6659,

		/// <summary>
		/// Unit of power; name: BTU/hr; long name: BTUs per hour.
		/// </summary>
		BritishThermalUnitsPerHour = 6660,

		/// <summary>
		/// Unit of power; name: HP; long name: horse power.
		/// </summary>
		HorsePower = 6661,

		#endregion

		#region Ratio Units (8705-8710)

		/// <summary>
		/// Unit of ratio; name: fraction; long name: fraction.
		/// </summary>
		Fraction = 8705,

		/// <summary>
		/// Unit of ratio; name: %; long name: percent.
		/// </summary>
		Percent = 8706,

		/// <summary>
		/// Unit of ratio; name: ppt; long name: parts per thousand.
		/// </summary>
		PartsPerThousand = 8707,

		/// <summary>
		/// Unit of ratio; name: ppm; long name: parts per million.
		/// </summary>
		PartsPerMillion = 8708,

		/// <summary>
		/// Unit of ratio; name: ppb; long name: parts per billion.
		/// </summary>
		PartsPerBillion = 8709,

		/// <summary>
		/// Unit of ratio; name: phr; long name: per hundred rubber.
		/// </summary>
		PartsPerHundredRubber = 8710,

		#endregion

		#region Currency Units (8961-8965)

		/// <summary>
		/// Unit of currency; name: $; long name: US Dollars.
		/// </summary>
		USDollar = 8961,

		/// <summary>
		/// Unit of currency; name: €; long name: Euros.
		/// </summary>
		Euro = 8962,

		/// <summary>
		/// Unit of currency; name: £; long name: GB Pounds.
		/// </summary>
		BritishPound = 8963,

		/// <summary>
		/// Unit of currency; name: ¥; long name: Japanese Yens.
		/// </summary>
		Yen = 8964,

		/// <summary>
		/// Unit of currency; name: Yuan; long name: Chinese Yuans.
		/// </summary>
		Yuan = 8965,

		#endregion

		#region Mass Flow Units (9217-9227)

		/// <summary>
		/// Unit of mass flow; name: g/min; long name: grams per minute.
		/// </summary>
		GramsPerMinute = 9217,

		/// <summary>
		/// Unit of mass flow; name: mg/min; long name: milligrams per minute.
		/// </summary>
		MilliGramsPerMinute = 9218,

		/// <summary>
		/// Unit of mass flow; name: g/sec; long name: grams per second.
		/// </summary>
		GramsPerSecond = 9219,

		/// <summary>
		/// Unit of mass flow; name: mg/sec; long name: milligrams per second.
		/// </summary>
		MilliGramsPerSecond = 9220,

		/// <summary>
		/// Unit of mass flow; name: lb/min; long name: pounds per minute.
		/// </summary>
		PoundsPerMinute = 9221,

		/// <summary>
		/// Unit of mass flow; name: lb/hr; long name: pounds per hour.
		/// </summary>
		PoundsPerHour = 9222,

		/// <summary>
		/// Unit of mass flow; name: g/hr; long name: grams per hour.
		/// </summary>
		GramsPerHour = 9223,

		/// <summary>
		/// Unit of mass flow; name: kg/min; long name: kilograms per minute.
		/// </summary>
		KiloGramsPerMinute = 9224,

		/// <summary>
		/// Unit of mass flow; name: kg/hr; long name: kilograms per hour.
		/// </summary>
		KiloGramsPerHour = 9225,

		/// <summary>
		/// Unit of mass flow; name: klb/hr; long name: thousand pounds per hour.
		/// </summary>
		ThousandPoundsPerHour = 9226,

		/// <summary>
		/// Unit of mass flow; name: kg/sec; long name: kilograms per second.
		/// </summary>
		KiloGramsPerSecond = 9227,

		#endregion

		#region Density Units (9729-9731)

		/// <summary>
		/// Unit of density; name: g/cm3; long name: grams per cubic centimeter.
		/// </summary>
		GramsPerCubicCentiMeter = 9729,

		/// <summary>
		/// Unit of density; name: lb/ft3; long name: pounds per cubic foot.
		/// </summary>
		PoundsPerCubicFoot = 9730,

		/// <summary>
		/// Unit of density; name: kg/m3; long name: kilograms per cubic meter.
		/// </summary>
		KiloGramsPerCubicMeter = 9731,

		/// <summary>
		/// Unit of density; name: μg/ml; long name: micrograms per milliliter.
		/// </summary>
		MicroGramsPerMilliLiter = 9733,

		#endregion

		#region Angular Acceleration Units (4865-4871)

		/// <summary>
		/// Unit of angular acceleration; name: deg/sec2; long name: degrees per square second.
		/// </summary>
		DegreesPerSquareSecond = 4865,

		/// <summary>
		/// Unit of angular acceleration; name: deg/min2; long name: degrees per square minute.
		/// </summary>
		DegreesPerSquareMinute = 4866,

		/// <summary>
		/// Unit of angular acceleration; name: deg/hr2; long name: degrees per square hour.
		/// </summary>
		DegreesPerSquareHour = 4867,

		/// <summary>
		/// Unit of angular acceleration; name: rev/sec2; long name: revolutions per square second.
		/// </summary>
		RevolutionsPerSquareSecond = 4868,

		/// <summary>
		/// Unit of angular acceleration; name: rev/min2; long name: revolutions per square minute.
		/// </summary>
		RevolutionsPerSquareMinute = 4869,

		/// <summary>
		/// Unit of angular acceleration; name: rev/hr2; long name: revolutions per square hour.
		/// </summary>
		RevolutionsPerSquareHour = 4870,

		/// <summary>
		/// Unit of angular acceleration; name: rad/sec2; long name: radians per second per second.
		/// </summary>
		RadiansPerSquareSecond = 4871,

		#endregion

		#region Linear Acceleration Units (5121-5126)

		/// <summary>
		/// Unit of linear acceleration; name: mm/sec2; long name: millimeters per square second.
		/// </summary>
		MilliMetersPerSquareSecond = 5121,

		/// <summary>
		/// Unit of linear acceleration; name: mm/min2; long name: millimeters per square minute.
		/// </summary>
		MilliMetersPerSquareMinute = 5122,

		/// <summary>
		/// Unit of linear acceleration; name: mm/hr2; long name: millimeters per square hour.
		/// </summary>
		MilliMetersPerSquareHour = 5123,

		/// <summary>
		/// Unit of linear acceleration; name: m/sec2; long name: meters per square second.
		/// </summary>
		MetersPerSquareSecond = 5124,

		/// <summary>
		/// Unit of linear acceleration; name: m/min2; long name: meters per square minute.
		/// </summary>
		MetersPerSquareMinute = 5125,

		/// <summary>
		/// Unit of linear acceleration; name: m/hr2; long name: meters per square hour.
		/// </summary>
		MetersPerSquareHour = 5126,

		#endregion

		#region Flow Acceleration Units (5377-5383)

		/// <summary>
		/// Unit of flow acceleration; name: μl/sec2; long name: microliters per square second.
		/// </summary>
		MicroLitersPerSquareSecond = 5377,

		/// <summary>
		/// Unit of flow acceleration; name: μl/min2; long name: microliters per square minute.
		/// </summary>
		MicroLitersPerSquareMinute = 5378,

		/// <summary>
		/// Unit of flow acceleration; name: μl/hr2; long name: microliters per square hour.
		/// </summary>
		MicroLitersPerSquareHour = 5379,

		/// <summary>
		/// Unit of flow acceleration; name: ml/sec2; long name: milliliters per square second.
		/// </summary>
		MilliLitersPerSquareSecond = 5380,

		/// <summary>
		/// Unit of flow acceleration; name: ml/min2; long name: milliliters per square minute.
		/// </summary>
		MilliLitersPerSquareMinute = 5381,

		/// <summary>
		/// Unit of flow acceleration; name: ml/hr2; long name: milliliters per square hour.
		/// </summary>
		MilliLitersPerSquareHour = 5382,

		/// <summary>
		/// Unit of flow acceleration; name: m3/sec2; long name: cubic meters per square second.
		/// </summary>
		CubicMetersPerSquareSecond = 5383,

		#endregion

		#region Conductivity Units (5889-5895)

		/// <summary>
		/// Unit of conductivity; name: S/cm; long name: Siemens per centimeter.
		/// </summary>
		SiemensPerCentiMeter = 5889,

		/// <summary>
		/// Unit of conductivity; name: mS/cm; long name: millisiemens per centimeter.
		/// </summary>
		MilliSiemensPerCentiMeter = 5890,

		/// <summary>
		/// Unit of conductivity; name: μS/cm; long name: microsiemens per centimeter.
		/// </summary>
		MicroSiemensPerCentiMeter = 5891,

		/// <summary>
		/// Unit of conductivity; name: mho cm; long name: mhos per centimeter.
		/// </summary>
		MhosPerCentiMeter = 5892,

		/// <summary>
		/// Unit of conductivity; name: ec; long name: electric conductivity.
		/// </summary>
		ElectricConductivity = 5893,

		/// <summary>
		/// Unit of conductivity; name: cf; long name: conductivity factor.
		/// </summary>
		ConductivityFactor = 5894,

		/// <summary>
		/// Unit of conductivity; name: S/m; long name: Siemens per meter.
		/// </summary>
		SiemensPerMeter = 5895,

		#endregion

		#region Viscosity Units (6913-6916)

		/// <summary>
		/// Unit of viscosity; name: cPs; long name: centipoises.
		/// </summary>
		CentiPoise = 6913,

		/// <summary>
		/// Unit of viscosity; name: mPs; long name: millipoise.
		/// </summary>
		MilliPoise = 6914,

		/// <summary>
		/// Unit of viscosity; name: poise; long name: Poise.
		/// </summary>
		Poise = 6915,

		/// <summary>
		/// Unit of viscosity; name: Pa sec; long name: Pascal seconds.
		/// </summary>
		PascalSeconds = 6916,

		#endregion

		#region Kinematic Viscosity Units (7169-7171)

		/// <summary>
		/// Unit of kinematic viscosity; name: cSt; long name: centistokes.
		/// </summary>
		CentiStoke = 7169,

		/// <summary>
		/// Unit of kinematic viscosity; name: mSt; long name: millistokes.
		/// </summary>
		MilliStoke = 7170,

		/// <summary>
		/// Unit of kinematic viscosity; name: m2/s; long name: square meters per second.
		/// </summary>
		SquareMetersPerSecond = 7171,

		#endregion

		#region Force Per Unit Length Units (7425-7426)

		/// <summary>
		/// Unit of force per unit length; name: lb(force)/in; long name: pounds force per inch.
		/// </summary>
		PoundsForcePerInch = 7425,

		/// <summary>
		/// Unit of force per unit length; name: N/m; long name: Newtons per meter.
		/// </summary>
		NewtonsPerMeter = 7426,

		#endregion

		#region Energy Per Area Units (7681-7682)

		/// <summary>
		/// Unit of energy per area; name: J/m2; long name: Joules per square meter.
		/// </summary>
		JoulesPerSquareMeter = 7681,

		/// <summary>
		/// Unit of energy per area; name: kJ/m2; long name: kilojoules per square meter.
		/// </summary>
		KiloJoulesPerSquareMeter = 7682,

		#endregion

		#region Torque Units (7937-7939)

		/// <summary>
		/// Unit of torque; name: N m; long name: Newton meters.
		/// </summary>
		NewtonMeter = 7937,

		/// <summary>
		/// Unit of torque; name: dNm; long name: decinewton meters.
		/// </summary>
		DeciNewtonMeter = 7938,

		/// <summary>
		/// Unit of torque; name: N cm; long name: Newton centimeters.
		/// </summary>
		NewtonCentiMeter = 7939,

		#endregion

		#region Torque Rate Units (8193-8194)

		/// <summary>
		/// Unit of torque rate; name: dNm/min; long name: decinewton meters per minute.
		/// </summary>
		DeciNewtonMetersPerMinute = 8193,

		/// <summary>
		/// Unit of torque rate; name: Nm/sec; long name: Newton meters per second.
		/// </summary>
		NewtonMetersPerSecond = 8194,

		#endregion

		#region Pressure Rate Units (8449-8475)

		/// <summary>
		/// Unit of pressure rate; name: kPa/sec; long name: kilopascals per second.
		/// </summary>
		KiloPascalsPerSecond = 8449,

		/// <summary>
		/// Unit of pressure rate; name: kPa/min; long name: kilopascals per minute.
		/// </summary>
		KiloPascalsPerMinute = 8450,

		/// <summary>
		/// Unit of pressure rate; name: kPa/hr; long name: kilopascals per hour.
		/// </summary>
		KiloPascalsPerHour = 8451,

		/// <summary>
		/// Unit of pressure rate; name: Pa/sec; long name: Pascals per second.
		/// </summary>
		PascalsPerSecond = 8452,

		/// <summary>
		/// Unit of pressure rate; name: Pa/min; long name: Pascals per minute.
		/// </summary>
		PascalsPerMinute = 8453,

		/// <summary>
		/// Unit of pressure rate; name: Pa/hr; long name: Pascals per hour.
		/// </summary>
		PascalsPerHour = 8454,

		/// <summary>
		/// Unit of pressure rate; name: ATM/sec; long name: atmospheres per second.
		/// </summary>
		AtmospheresPerSecond = 8455,

		/// <summary>
		/// Unit of pressure rate; name: ATM/min; long name: atmospheres per minute.
		/// </summary>
		AtmospheresPerMinute = 8456,

		/// <summary>
		/// Unit of pressure rate; name: ATM/hr; long name: atmospheres per hour.
		/// </summary>
		AtmospheresPerHour = 8457,

		/// <summary>
		/// Unit of pressure rate; name: PSI/sec; long name: pounds per square inch per second.
		/// </summary>
		PoundsPerSquareInchPerSecond = 8458,

		/// <summary>
		/// Unit of pressure rate; name: PSI/min; long name: pounds per square inch per minute.
		/// </summary>
		PoundsPerSquareInchPerMinute = 8459,

		/// <summary>
		/// Unit of pressure rate; name: PSI/hr; long name: pounds per square inch per hour.
		/// </summary>
		PoundsPerSquareInchPerHour = 8460,

		/// <summary>
		/// Unit of pressure rate; name: bar/sec; long name: bars per second.
		/// </summary>
		BarsPerSecond = 8461,

		/// <summary>
		/// Unit of pressure rate; name: bar/min; long name: bars per minute.
		/// </summary>
		BarsPerMinute = 8462,

		/// <summary>
		/// Unit of pressure rate; name: bar/hr; long name: bars per hour.
		/// </summary>
		BarsPerHour = 8463,

		/// <summary>
		/// Unit of pressure rate; name: kg/m2/sec; long name: kilograms per square meter per second.
		/// </summary>
		KiloGramsPerSquareMeterPerSecond = 8464,

		/// <summary>
		/// Unit of pressure rate; name: kg/m2/min; long name: kilograms per square meter per minute.
		/// </summary>
		KiloGramsPerSquareMeterPerMinute = 8465,

		/// <summary>
		/// Unit of pressure rate; name: kg/m2/hr; long name: kilograms per square meter per hour.
		/// </summary>
		KiloGramsPerSquareMeterPerHour = 8466,

		/// <summary>
		/// Unit of pressure rate; name: mbar/sec; long name: millibars per second.
		/// </summary>
		MilliBarsPerSecond = 8467,

		/// <summary>
		/// Unit of pressure rate; name: mbar/min; long name: millibars per minute.
		/// </summary>
		MilliBarsPerMinute = 8468,

		/// <summary>
		/// Unit of pressure rate; name: mbar/hr; long name: millibars per hour.
		/// </summary>
		MilliBarsPerHour = 8469,

		/// <summary>
		/// Unit of pressure rate; name: MPa/sec; long name: megapascals per second.
		/// </summary>
		MegaPascalsPerSecond = 8470,

		/// <summary>
		/// Unit of pressure rate; name: MPa/min; long name: megapascals per minute.
		/// </summary>
		MegaPascalsPerMinute = 8471,

		/// <summary>
		/// Unit of pressure rate; name: MPa/hr; long name: megapascals per hour.
		/// </summary>
		MegaPascalsPerHour = 8472,

		/// <summary>
		/// Unit of pressure rate; name: mTorr/sec; long name: millitorrs per second.
		/// </summary>
		MilliTorrsPerSecond = 8473,

		/// <summary>
		/// Unit of pressure rate; name: mTorr/min; long name: millitorrs per minute.
		/// </summary>
		MilliTorrsPerMinute = 8474,

		/// <summary>
		/// Unit of pressure rate; name: mTorr/hr; long name: millitorrs per hour.
		/// </summary>
		MilliTorrsPerHour = 8475,

		#endregion

		#region Reciprocal Time Units (9473-9475)

		/// <summary>
		/// Unit of reciprocal time; name: 1/sec; long name: reciprocal seconds.
		/// </summary>
		ReciprocalSecond = 9473,

		/// <summary>
		/// Unit of reciprocal time; name: 1/min; long name: reciprocal minutes.
		/// </summary>
		ReciprocalMinute = 9474,

		/// <summary>
		/// Unit of reciprocal time; name: 1/hr; long name: reciprocal hours.
		/// </summary>
		ReciprocalHour = 9475,

		#endregion

		#region Resistance Units (9985)

		/// <summary>
		/// Unit of resistance; name: Ohm; long name: Ohms.
		/// </summary>
		Ohm = 9985,

		#endregion

		#region Resistivity Units (10241)

		/// <summary>
		/// Unit of resistivity; name: Ohm m; long name: Ohm meters.
		/// </summary>
		OhmMeter = 10241,

		#endregion

		#region Wavenumber Units (10497-10498)

		/// <summary>
		/// Unit of wavenumber; name: 1/cm; long name: 1/centimeter.
		/// </summary>
		ReciprocalCentiMeter = 10497,

		/// <summary>
		/// Unit of wavenumber; name: 1/m; long name: 1/meter.
		/// </summary>
		ReciprocalMeter = 10498,

		#endregion

		#region Volume Flow Units (10753-10756)

		/// <summary>
		/// Unit of volume flow; name: l/sec; long name: liters per second.
		/// </summary>
		LitersPerSecond = 10753,

		/// <summary>
		/// Unit of volume flow; name: US gal/min; long name: US gallons per minute.
		/// </summary>
		USGallonsPerMinute = 10754,

		/// <summary>
		/// Unit of volume flow; name: UK gal/hr; long name: UK gallons per hour.
		/// </summary>
		UKGallonsPerHour = 10755,

		/// <summary>
		/// Unit of volume flow; name: US gal/hr; long name: US gallons per hour.
		/// </summary>
		USGallonsPerHour = 10756,

		#endregion

		#region Specific Gravity Units (11009-11010)

		/// <summary>
		/// Unit of specific gravity; name: Baume; long name: Baumes.
		/// </summary>
		Baume = 11009,

		/// <summary>
		/// Unit of specific gravity; name: API; long name: APIs.
		/// </summary>
		Api = 11010,

		#endregion

		#region Heat Flux Units (11265-11266)

		/// <summary>
		/// Unit of heat flux; name: W/m2; long name: Watts per square meter.
		/// </summary>
		WattsPerSquareMeter = 11265,

		/// <summary>
		/// Unit of heat flux; name: BTU/(hr * ft2); long name: BTUs per square foot per hour.
		/// </summary>
		BritishThermalUnitsPerSquareFootPerHour = 11266,

		#endregion

		#region Capacitance Units (11521)

		/// <summary>
		/// Unit of capacitance; name: F; long name: Farads.
		/// </summary>
		Farads = 11521,

		#endregion

		#region Magnetic Flux Units (11777)

		/// <summary>
		/// Unit of magnetic flux; name: Wb; long name: Webers.
		/// </summary>
		Weber = 11777,

		#endregion

		#region Electric Conductance Units (12033)

		/// <summary>
		/// Unit of electric conductance; name: S; long name: Siemens.
		/// </summary>
		Siemens = 12033,

		#endregion

		#region Luminous Intensity Units (12289)

		/// <summary>
		/// Unit of luminous intensity; name: cd; long name: candelas.
		/// </summary>
		Candela = 12289,

		#endregion

		#region Additional Units

		/// <summary>
		/// Unit of unit cost; name: $/lb; long name: dollars per pound.
		/// </summary>
		USDollarsPerPound = 12545,

		/// <summary>
		/// Unit of molar mass; name: kg/mol; long name: kilograms per mole.
		/// </summary>
		KiloGramsPerMole = 12801,

		/// <summary>
		/// Unit of mass ratio; name: mass fraction; long name: mass fraction.
		/// </summary>
		MassFraction = 13313,

		/// <summary>
		/// Unit of volume ratio; name: vol fraction; long name: volume fraction.
		/// </summary>
		VolumeFraction = 13568,

		/// <summary>
		/// Unit of molar ratio; name: mol fraction; long name: mole fraction.
		/// </summary>
		MoleFraction = 13825,

		/// <summary>
		/// Unit of specific moles; name: mol/kg; long name: moles per kilogram.
		/// </summary>
		MolesPerKiloGram = 14337,

		/// <summary>
		/// Unit of absorbance; name: AU; long name: absorbance unit.
		/// </summary>
		AbsorbanceUnit = 14849,

		/// <summary>
		/// Unit of transmittance; name: T%; long name: transmittance percent.
		/// </summary>
		TransmittancePercent = 15105,

		#endregion
	}
}
