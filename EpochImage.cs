using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// Complete implementation of the IEpochImage interface for .NET
    /// Provides comprehensive image management capabilities without COM dependencies
    /// </summary>
    public class EpochImage : IEpochImage
    {
        #region Private Fields

        private readonly Guid _id;
        private readonly DateTime _timestamp;
        private Image _image;
        private byte[] _imageData;
        private string _filePath;
        private Dictionary<string, object> _metadata;
        private List<string> _tags;
        private bool _isLoaded;
        private bool _isModified;
        private bool _disposed;
        private readonly DateTime _creationTime;
        private DateTime _lastModifiedTime;
        private readonly object _lockObject = new object();

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the EpochImage class
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        public EpochImage(DateTime timestamp)
        {
            _id = Guid.NewGuid();
            _timestamp = timestamp;
            _creationTime = DateTime.Now;
            _lastModifiedTime = _creationTime;
            _metadata = new Dictionary<string, object>();
            _tags = new List<string>();
            _isLoaded = false;
            _isModified = false;
            Quality = 95;
        }

        /// <summary>
        /// Initializes a new instance of the EpochImage class with image data
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        /// <param name="imageData">The image data as byte array</param>
        public EpochImage(DateTime timestamp, byte[] imageData) : this(timestamp)
        {
            if (imageData != null && imageData.Length > 0)
            {
                _imageData = new byte[imageData.Length];
                Array.Copy(imageData, _imageData, imageData.Length);
                LoadFromBytes(_imageData);
            }
        }

        /// <summary>
        /// Initializes a new instance of the EpochImage class with an image
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        /// <param name="image">The image object</param>
        public EpochImage(DateTime timestamp, Image image) : this(timestamp)
        {
            if (image != null)
            {
                _image = new Bitmap(image);
                _isLoaded = true;
                _imageData = ImageToByteArray(_image);
            }
        }

        /// <summary>
        /// Initializes a new instance of the EpochImage class with a file path
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        /// <param name="filePath">The file path to the image</param>
        public EpochImage(DateTime timestamp, string filePath) : this(timestamp)
        {
            _filePath = filePath;
        }

        #endregion

        #region Properties

        /// <summary>
        /// Gets the unique identifier of the epoch image
        /// </summary>
        public Guid Id => _id;

        /// <summary>
        /// Gets the timestamp of the epoch
        /// </summary>
        public DateTime Timestamp => _timestamp;

        /// <summary>
        /// Gets the image data
        /// </summary>
        public Image Image
        {
            get
            {
                lock (_lockObject)
                {
                    return _image;
                }
            }
        }

        /// <summary>
        /// Gets the raw image data as bytes
        /// </summary>
        public byte[] ImageData
        {
            get
            {
                lock (_lockObject)
                {
                    return _imageData;
                }
            }
        }

        /// <summary>
        /// Gets the image format
        /// </summary>
        public string Format
        {
            get
            {
                lock (_lockObject)
                {
                    if (_image?.RawFormat != null)
                    {
                        if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Jpeg))
                            return "JPEG";
                        if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Png))
                            return "PNG";
                        if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Bmp))
                            return "BMP";
                        if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Gif))
                            return "GIF";
                        if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Tiff))
                            return "TIFF";
                        if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Icon))
                            return "ICO";
                        if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Wmf))
                            return "WMF";
                        if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Emf))
                            return "EMF";

                        return _image.RawFormat.ToString();
                    }
                    return "Unknown";
                }
            }
        }

        /// <summary>
        /// Gets the image width
        /// </summary>
        public int Width
        {
            get
            {
                lock (_lockObject)
                {
                    return _image?.Width ?? 0;
                }
            }
        }

        /// <summary>
        /// Gets the image height
        /// </summary>
        public int Height
        {
            get
            {
                lock (_lockObject)
                {
                    return _image?.Height ?? 0;
                }
            }
        }

        /// <summary>
        /// Gets the image size in bytes
        /// </summary>
        public long Size
        {
            get
            {
                lock (_lockObject)
                {
                    return _imageData?.Length ?? 0;
                }
            }
        }

        /// <summary>
        /// Gets or sets the metadata associated with the epoch image
        /// </summary>
        public Dictionary<string, object> Metadata
        {
            get
            {
                lock (_lockObject)
                {
                    return _metadata;
                }
            }
            set
            {
                lock (_lockObject)
                {
                    _metadata = value ?? new Dictionary<string, object>();
                    MarkAsModified();
                }
            }
        }

        /// <summary>
        /// Gets or sets the file path of the epoch image
        /// </summary>
        public string FilePath
        {
            get
            {
                lock (_lockObject)
                {
                    return _filePath;
                }
            }
            set
            {
                lock (_lockObject)
                {
                    _filePath = value;
                    MarkAsModified();
                }
            }
        }

        /// <summary>
        /// Gets a value indicating whether the image is loaded in memory
        /// </summary>
        public bool IsLoaded
        {
            get
            {
                lock (_lockObject)
                {
                    return _isLoaded;
                }
            }
        }

        /// <summary>
        /// Gets or sets the name of the epoch image
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the epoch image
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the quality of the epoch image (0-100)
        /// </summary>
        public int Quality { get; set; }

        /// <summary>
        /// Gets the creation time of the epoch image
        /// </summary>
        public DateTime CreationTime => _creationTime;

        /// <summary>
        /// Gets the last modified time of the epoch image
        /// </summary>
        public DateTime LastModifiedTime
        {
            get
            {
                lock (_lockObject)
                {
                    return _lastModifiedTime;
                }
            }
        }

        /// <summary>
        /// Gets or sets the compression type used for the image
        /// </summary>
        public string CompressionType { get; set; }

        /// <summary>
        /// Gets the pixel format of the image
        /// </summary>
        public string PixelFormat
        {
            get
            {
                lock (_lockObject)
                {
                    return _image?.PixelFormat.ToString() ?? "Unknown";
                }
            }
        }

        /// <summary>
        /// Gets the horizontal resolution (DPI) of the image
        /// </summary>
        public float HorizontalResolution
        {
            get
            {
                lock (_lockObject)
                {
                    return _image?.HorizontalResolution ?? 0f;
                }
            }
        }

        /// <summary>
        /// Gets the vertical resolution (DPI) of the image
        /// </summary>
        public float VerticalResolution
        {
            get
            {
                lock (_lockObject)
                {
                    return _image?.VerticalResolution ?? 0f;
                }
            }
        }

        /// <summary>
        /// Gets or sets the tags associated with the epoch image
        /// </summary>
        public List<string> Tags
        {
            get
            {
                lock (_lockObject)
                {
                    return _tags;
                }
            }
            set
            {
                lock (_lockObject)
                {
                    _tags = value ?? new List<string>();
                    MarkAsModified();
                }
            }
        }

        /// <summary>
        /// Gets a value indicating whether the image has been modified
        /// </summary>
        public bool IsModified
        {
            get
            {
                lock (_lockObject)
                {
                    return _isModified;
                }
            }
        }

        #endregion

        #region Loading and Saving Methods

        /// <summary>
        /// Loads the image into memory asynchronously
        /// </summary>
        /// <returns>True if the image was loaded successfully; otherwise, false</returns>
        public async Task<bool> LoadAsync()
        {
            if (!string.IsNullOrEmpty(_filePath))
            {
                return await LoadAsync(_filePath);
            }
            else if (_imageData != null && _imageData.Length > 0)
            {
                return await Task.Run(() => LoadFromBytes(_imageData));
            }
            return false;
        }

        /// <summary>
        /// Loads the image into memory from the specified file path asynchronously
        /// </summary>
        /// <param name="filePath">The file path to load from</param>
        /// <returns>True if the image was loaded successfully; otherwise, false</returns>
        public async Task<bool> LoadAsync(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                    return false;

                return await Task.Run(() =>
                {
                    try
                    {
                        lock (_lockObject)
                        {
                            // Dispose existing image
                            _image?.Dispose();

                            // Load new image using file stream to avoid file locking
                            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
                            {
                                _image = Image.FromStream(fileStream);
                            }

                            _imageData = ImageToByteArray(_image);
                            _filePath = filePath;
                            _isLoaded = true;
                            _lastModifiedTime = File.GetLastWriteTime(filePath);
                            _isModified = false;

                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log error if logging is available
                        System.Diagnostics.Debug.WriteLine($"Failed to load image from {filePath}: {ex.Message}");
                        return false;
                    }
                });
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Unloads the image from memory
        /// </summary>
        public void Unload()
        {
            lock (_lockObject)
            {
                _image?.Dispose();
                _image = null;
                _isLoaded = false;
            }
        }

        /// <summary>
        /// Saves the epoch image to the specified file path asynchronously
        /// </summary>
        /// <param name="filePath">The file path to save to</param>
        /// <returns>True if the image was saved successfully; otherwise, false</returns>
        public async Task<bool> SaveAsync(string filePath)
        {
            try
            {
                if (_image == null || string.IsNullOrEmpty(filePath))
                    return false;

                return await Task.Run(() =>
                {
                    try
                    {
                        lock (_lockObject)
                        {
                            var directory = Path.GetDirectoryName(filePath);
                            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                            {
                                Directory.CreateDirectory(directory);
                            }

                            // Determine format from file extension
                            var extension = Path.GetExtension(filePath).ToLower();
                            System.Drawing.Imaging.ImageFormat format;

                            switch (extension)
                            {
                                case ".jpg":
                                case ".jpeg":
                                    format = System.Drawing.Imaging.ImageFormat.Jpeg;
                                    SaveWithQuality(filePath, format, Quality);
                                    break;
                                case ".png":
                                    format = System.Drawing.Imaging.ImageFormat.Png;
                                    _image.Save(filePath, format);
                                    break;
                                case ".bmp":
                                    format = System.Drawing.Imaging.ImageFormat.Bmp;
                                    _image.Save(filePath, format);
                                    break;
                                case ".gif":
                                    format = System.Drawing.Imaging.ImageFormat.Gif;
                                    _image.Save(filePath, format);
                                    break;
                                case ".tiff":
                                case ".tif":
                                    format = System.Drawing.Imaging.ImageFormat.Tiff;
                                    _image.Save(filePath, format);
                                    break;
                                case ".ico":
                                    format = System.Drawing.Imaging.ImageFormat.Icon;
                                    _image.Save(filePath, format);
                                    break;
                                default:
                                    format = System.Drawing.Imaging.ImageFormat.Png;
                                    _image.Save(filePath, format);
                                    break;
                            }

                            _filePath = filePath;
                            _isModified = false;
                            _lastModifiedTime = DateTime.Now;

                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Failed to save image to {filePath}: {ex.Message}");
                        return false;
                    }
                });
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Saves the epoch image to its current file path asynchronously
        /// </summary>
        /// <returns>True if the image was saved successfully; otherwise, false</returns>
        public async Task<bool> SaveAsync()
        {
            if (string.IsNullOrEmpty(_filePath))
                return false;

            return await SaveAsync(_filePath);
        }

        #endregion

        #region Transformation Methods

        /// <summary>
        /// Creates a shallow clone of the epoch image
        /// </summary>
        /// <returns>A new EpochImage instance that is a clone of this instance</returns>
        public IEpochImage Clone()
        {
            lock (_lockObject)
            {
                var clone = new EpochImage(_timestamp);

                if (_image != null)
                {
                    clone._image = new Bitmap(_image);
                    clone._isLoaded = true;
                }

                if (_imageData != null)
                {
                    clone._imageData = new byte[_imageData.Length];
                    Array.Copy(_imageData, clone._imageData, _imageData.Length);
                }

                clone._filePath = _filePath;
                clone.Name = Name;
                clone.Description = Description;
                clone.Quality = Quality;
                clone.CompressionType = CompressionType;
                clone._metadata = new Dictionary<string, object>(_metadata);
                clone._tags = new List<string>(_tags);

                return clone;
            }
        }

        /// <summary>
        /// Creates a deep clone of the epoch image
        /// </summary>
        /// <returns>A new EpochImage instance that is a deep clone of this instance</returns>
        public IEpochImage DeepClone()
        {
            return Clone(); // For images, shallow and deep clone are effectively the same
        }

        /// <summary>
        /// Resizes the image to the specified dimensions
        /// </summary>
        /// <param name="width">The new width</param>
        /// <param name="height">The new height</param>
        /// <returns>A new EpochImage with the resized image, or null if the operation failed</returns>
        public IEpochImage Resize(int width, int height)
        {
            if (width <= 0 || height <= 0)
                return null;

            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    var resizedBitmap = new Bitmap(width, height);
                    using (var graphics = Graphics.FromImage(resizedBitmap))
                    {
                        graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                        graphics.SmoothingMode = SmoothingMode.HighQuality;
                        graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                        graphics.CompositingQuality = CompositingQuality.HighQuality;

                        graphics.DrawImage(_image, 0, 0, width, height);
                    }

                    var result = new EpochImage(_timestamp, resizedBitmap);
                    result.Name = Name + " (Resized)";
                    result.Description = $"Resized from {Width}x{Height} to {width}x{height}";
                    return result;
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Crops the image to the specified rectangle
        /// </summary>
        /// <param name="cropRectangle">The rectangle to crop to</param>
        /// <returns>A new EpochImage with the cropped image, or null if the operation failed</returns>
        public IEpochImage Crop(Rectangle cropRectangle)
        {
            if (cropRectangle.IsEmpty)
                return null;

            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    // Ensure crop rectangle is within image bounds
                    cropRectangle = Rectangle.Intersect(cropRectangle, new Rectangle(0, 0, Width, Height));

                    if (cropRectangle.IsEmpty)
                        return null;

                    var croppedBitmap = new Bitmap(cropRectangle.Width, cropRectangle.Height);
                    using (var graphics = Graphics.FromImage(croppedBitmap))
                    {
                        graphics.DrawImage(_image,
                            new Rectangle(0, 0, cropRectangle.Width, cropRectangle.Height),
                            cropRectangle,
                            GraphicsUnit.Pixel);
                    }

                    var result = new EpochImage(_timestamp, croppedBitmap);
                    result.Name = Name + " (Cropped)";
                    result.Description = $"Cropped to {cropRectangle.Width}x{cropRectangle.Height}";
                    return result;
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Rotates the image by the specified angle
        /// </summary>
        /// <param name="angle">The angle to rotate in degrees</param>
        /// <returns>A new EpochImage with the rotated image, or null if the operation failed</returns>
        public IEpochImage Rotate(float angle)
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    // Calculate new dimensions after rotation
                    var radians = angle * Math.PI / 180;
                    var cos = Math.Abs(Math.Cos(radians));
                    var sin = Math.Abs(Math.Sin(radians));
                    var newWidth = (int)(Width * cos + Height * sin);
                    var newHeight = (int)(Width * sin + Height * cos);

                    var rotatedBitmap = new Bitmap(newWidth, newHeight);
                    using (var graphics = Graphics.FromImage(rotatedBitmap))
                    {
                        graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                        graphics.SmoothingMode = SmoothingMode.HighQuality;

                        // Move to center, rotate, then move back
                        graphics.TranslateTransform(newWidth / 2f, newHeight / 2f);
                        graphics.RotateTransform(angle);
                        graphics.TranslateTransform(-Width / 2f, -Height / 2f);
                        graphics.DrawImage(_image, 0, 0);
                    }

                    var result = new EpochImage(_timestamp, rotatedBitmap);
                    result.Name = Name + " (Rotated)";
                    result.Description = $"Rotated by {angle} degrees";
                    return result;
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Flips the image horizontally
        /// </summary>
        /// <returns>A new EpochImage with the horizontally flipped image, or null if the operation failed</returns>
        public IEpochImage FlipHorizontal()
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    var flippedBitmap = new Bitmap(_image);
                    flippedBitmap.RotateFlip(RotateFlipType.RotateNoneFlipX);

                    var result = new EpochImage(_timestamp, flippedBitmap);
                    result.Name = Name + " (Flipped H)";
                    result.Description = "Flipped horizontally";
                    return result;
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Flips the image vertically
        /// </summary>
        /// <returns>A new EpochImage with the vertically flipped image, or null if the operation failed</returns>
        public IEpochImage FlipVertical()
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    var flippedBitmap = new Bitmap(_image);
                    flippedBitmap.RotateFlip(RotateFlipType.RotateNoneFlipY);

                    var result = new EpochImage(_timestamp, flippedBitmap);
                    result.Name = Name + " (Flipped V)";
                    result.Description = "Flipped vertically";
                    return result;
                }
                catch
                {
                    return null;
                }
            }
        }

        #endregion

        #region Color Adjustment Methods

        /// <summary>
        /// Converts the image to grayscale
        /// </summary>
        /// <returns>A new EpochImage with the grayscale image, or null if the operation failed</returns>
        public IEpochImage ToGrayscale()
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    var grayscaleBitmap = new Bitmap(Width, Height);
                    var bitmap = _image as Bitmap ?? new Bitmap(_image);

                    for (int x = 0; x < Width; x++)
                    {
                        for (int y = 0; y < Height; y++)
                        {
                            var pixel = bitmap.GetPixel(x, y);
                            var grayValue = (int)(pixel.R * 0.299 + pixel.G * 0.587 + pixel.B * 0.114);
                            var grayColor = Color.FromArgb(pixel.A, grayValue, grayValue, grayValue);
                            grayscaleBitmap.SetPixel(x, y, grayColor);
                        }
                    }

                    var result = new EpochImage(_timestamp, grayscaleBitmap);
                    result.Name = Name + " (Grayscale)";
                    result.Description = "Converted to grayscale";
                    return result;
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Adjusts the brightness of the image
        /// </summary>
        /// <param name="brightness">The brightness adjustment (-100 to 100)</param>
        /// <returns>A new EpochImage with adjusted brightness, or null if the operation failed</returns>
        public IEpochImage AdjustBrightness(int brightness)
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    brightness = Math.Max(-100, Math.Min(100, brightness));
                    var factor = brightness / 100f;

                    var adjustedBitmap = new Bitmap(Width, Height);
                    var bitmap = _image as Bitmap ?? new Bitmap(_image);

                    for (int x = 0; x < Width; x++)
                    {
                        for (int y = 0; y < Height; y++)
                        {
                            var pixel = bitmap.GetPixel(x, y);

                            var r = Math.Max(0, Math.Min(255, pixel.R + (int)(255 * factor)));
                            var g = Math.Max(0, Math.Min(255, pixel.G + (int)(255 * factor)));
                            var b = Math.Max(0, Math.Min(255, pixel.B + (int)(255 * factor)));

                            var adjustedColor = Color.FromArgb(pixel.A, r, g, b);
                            adjustedBitmap.SetPixel(x, y, adjustedColor);
                        }
                    }

                    var result = new EpochImage(_timestamp, adjustedBitmap);
                    result.Name = Name + " (Brightness Adjusted)";
                    result.Description = $"Brightness adjusted by {brightness}";
                    return result;
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Adjusts the contrast of the image
        /// </summary>
        /// <param name="contrast">The contrast adjustment (-100 to 100)</param>
        /// <returns>A new EpochImage with adjusted contrast, or null if the operation failed</returns>
        public IEpochImage AdjustContrast(int contrast)
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    contrast = Math.Max(-100, Math.Min(100, contrast));
                    var factor = (259f * (contrast + 255f)) / (255f * (259f - contrast));

                    var adjustedBitmap = new Bitmap(Width, Height);
                    var bitmap = _image as Bitmap ?? new Bitmap(_image);

                    for (int x = 0; x < Width; x++)
                    {
                        for (int y = 0; y < Height; y++)
                        {
                            var pixel = bitmap.GetPixel(x, y);

                            var r = Math.Max(0, Math.Min(255, (int)(factor * (pixel.R - 128) + 128)));
                            var g = Math.Max(0, Math.Min(255, (int)(factor * (pixel.G - 128) + 128)));
                            var b = Math.Max(0, Math.Min(255, (int)(factor * (pixel.B - 128) + 128)));

                            var adjustedColor = Color.FromArgb(pixel.A, r, g, b);
                            adjustedBitmap.SetPixel(x, y, adjustedColor);
                        }
                    }

                    var result = new EpochImage(_timestamp, adjustedBitmap);
                    result.Name = Name + " (Contrast Adjusted)";
                    result.Description = $"Contrast adjusted by {contrast}";
                    return result;
                }
                catch
                {
                    return null;
                }
            }
        }

        #endregion

        #region Analysis Methods

        /// <summary>
        /// Gets a thumbnail of the image
        /// </summary>
        /// <param name="width">The thumbnail width</param>
        /// <param name="height">The thumbnail height</param>
        /// <returns>A thumbnail image, or null if the operation failed</returns>
        public Image GetThumbnail(int width, int height)
        {
            if (width <= 0 || height <= 0)
                return null;

            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    return _image.GetThumbnailImage(width, height, null, IntPtr.Zero);
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Gets the histogram of the image
        /// </summary>
        /// <returns>An array of 256 integers representing the luminance histogram, or null if the operation failed</returns>
        public int[] GetHistogram()
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    var histogram = new int[256];
                    var bitmap = _image as Bitmap ?? new Bitmap(_image);

                    for (int x = 0; x < Width; x++)
                    {
                        for (int y = 0; y < Height; y++)
                        {
                            var pixel = bitmap.GetPixel(x, y);
                            var luminance = (int)(pixel.R * 0.299 + pixel.G * 0.587 + pixel.B * 0.114);
                            histogram[luminance]++;
                        }
                    }

                    return histogram;
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Gets statistical information about the image
        /// </summary>
        /// <returns>ImageStatistics object containing statistical data, or null if the operation failed</returns>
        public ImageStatistics GetStatistics()
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    var bitmap = _image as Bitmap ?? new Bitmap(_image);
                    var stats = new ImageStatistics();
                    long totalR = 0, totalG = 0, totalB = 0;
                    int minR = 255, minG = 255, minB = 255;
                    int maxR = 0, maxG = 0, maxB = 0;
                    long pixelCount = Width * Height;

                    for (int x = 0; x < Width; x++)
                    {
                        for (int y = 0; y < Height; y++)
                        {
                            var pixel = bitmap.GetPixel(x, y);

                            totalR += pixel.R;
                            totalG += pixel.G;
                            totalB += pixel.B;

                            minR = Math.Min(minR, pixel.R);
                            minG = Math.Min(minG, pixel.G);
                            minB = Math.Min(minB, pixel.B);

                            maxR = Math.Max(maxR, pixel.R);
                            maxG = Math.Max(maxG, pixel.G);
                            maxB = Math.Max(maxB, pixel.B);
                        }
                    }

                    stats.Mean = Color.FromArgb((int)(totalR / pixelCount), (int)(totalG / pixelCount), (int)(totalB / pixelCount));
                    stats.Minimum = Color.FromArgb(minR, minG, minB);
                    stats.Maximum = Color.FromArgb(maxR, maxG, maxB);
                    stats.PixelCount = pixelCount;
                    stats.BrightnessAverage = (stats.Mean.R + stats.Mean.G + stats.Mean.B) / 3.0;

                    return stats;
                }
                catch
                {
                    return null;
                }
            }
        }

        #endregion

        #region Validation and Comparison Methods

        /// <summary>
        /// Validates the epoch image
        /// </summary>
        /// <returns>True if the image is valid; otherwise, false</returns>
        public bool Validate()
        {
            try
            {
                lock (_lockObject)
                {
                    return _image != null && Width > 0 && Height > 0;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Compares this epoch image with another
        /// </summary>
        /// <param name="other">The other epoch image to compare with</param>
        /// <returns>ImageComparisonResult containing comparison data, or null if comparison failed</returns>
        public ImageComparisonResult Compare(IEpochImage other)
        {
            var result = new ImageComparisonResult();

            if (other == null || _image == null || other.Image == null)
            {
                result.SimilarityPercentage = 0;
                return result;
            }

            if (Width != other.Width || Height != other.Height)
            {
                result.SimilarityPercentage = 0;
                return result;
            }

            lock (_lockObject)
            {
                try
                {
                    var bitmap1 = _image as Bitmap ?? new Bitmap(_image);
                    var bitmap2 = other.Image as Bitmap ?? new Bitmap(other.Image);

                    long totalPixels = Width * Height;
                    long identicalPixels = 0;
                    long totalDifference = 0;

                    for (int x = 0; x < Width; x++)
                    {
                        for (int y = 0; y < Height; y++)
                        {
                            var pixel1 = bitmap1.GetPixel(x, y);
                            var pixel2 = bitmap2.GetPixel(x, y);

                            if (pixel1.ToArgb() == pixel2.ToArgb())
                            {
                                identicalPixels++;
                            }
                            else
                            {
                                // Calculate pixel difference for MSE
                                var rDiff = pixel1.R - pixel2.R;
                                var gDiff = pixel1.G - pixel2.G;
                                var bDiff = pixel1.B - pixel2.B;
                                totalDifference += (rDiff * rDiff + gDiff * gDiff + bDiff * bDiff);
                            }
                        }
                    }

                    result.SimilarityPercentage = (double)identicalPixels / totalPixels * 100;
                    result.AreIdentical = result.SimilarityPercentage == 100;
                    result.MeanSquaredError = (double)totalDifference / (totalPixels * 3); // 3 for RGB

                    // Calculate PSNR
                    if (result.MeanSquaredError > 0)
                    {
                        result.PeakSignalToNoiseRatio = 20 * Math.Log10(255 / Math.Sqrt(result.MeanSquaredError));
                    }
                    else
                    {
                        result.PeakSignalToNoiseRatio = double.PositiveInfinity;
                    }

                    return result;
                }
                catch
                {
                    result.SimilarityPercentage = 0;
                    return result;
                }
            }
        }

        /// <summary>
        /// Exports the image to the specified format
        /// </summary>
        /// <param name="format">The format to export to</param>
        /// <returns>The exported image data as byte array, or null if export failed</returns>
        public byte[] Export(ImageFormat format)
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    using (var stream = new MemoryStream())
                    {
                        System.Drawing.Imaging.ImageFormat drawingFormat;

                        switch (format)
                        {
                            case ImageFormat.Jpeg:
                                drawingFormat = System.Drawing.Imaging.ImageFormat.Jpeg;
                                break;
                            case ImageFormat.Png:
                                drawingFormat = System.Drawing.Imaging.ImageFormat.Png;
                                break;
                            case ImageFormat.Bmp:
                                drawingFormat = System.Drawing.Imaging.ImageFormat.Bmp;
                                break;
                            case ImageFormat.Gif:
                                drawingFormat = System.Drawing.Imaging.ImageFormat.Gif;
                                break;
                            case ImageFormat.Tiff:
                                drawingFormat = System.Drawing.Imaging.ImageFormat.Tiff;
                                break;
                            case ImageFormat.Ico:
                                drawingFormat = System.Drawing.Imaging.ImageFormat.Icon;
                                break;
                            default:
                                drawingFormat = System.Drawing.Imaging.ImageFormat.Png;
                                break;
                        }

                        if (format == ImageFormat.Jpeg)
                        {
                            SaveWithQualityToStream(stream, drawingFormat, Quality);
                        }
                        else
                        {
                            _image.Save(stream, drawingFormat);
                        }

                        return stream.ToArray();
                    }
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Refreshes the image from its file source
        /// </summary>
        /// <returns>True if the refresh was successful; otherwise, false</returns>
        public async Task<bool> RefreshAsync()
        {
            if (string.IsNullOrEmpty(_filePath) || !File.Exists(_filePath))
                return false;

            return await LoadAsync(_filePath);
        }

        /// <summary>
        /// Exports the image to the specified format
        /// </summary>
        /// <param name="format">The format to export to</param>
        /// <returns>The exported image data as byte array, or null if export failed</returns>
        public byte[] Export(ImageFormat format)
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    using (var stream = new MemoryStream())
                    {
                        System.Drawing.Imaging.ImageFormat drawingFormat = GetDrawingImageFormat(format);

                        if (format == ImageFormat.Jpeg)
                        {
                            SaveWithQualityToStream(stream, drawingFormat, Quality);
                        }
                        else
                        {
                            _image.Save(stream, drawingFormat);
                        }

                        return stream.ToArray();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Export failed: {ex.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// Exports with custom quality settings
        /// </summary>
        /// <param name="format">The format to export to</param>
        /// <param name="quality">Quality setting (0-100, only applies to formats that support quality)</param>
        /// <returns>The exported image data as byte array, or null if export failed</returns>
        public byte[] ExportWithQuality(ImageFormat format, int quality)
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                // Validate quality parameter
                quality = Math.Max(0, Math.Min(100, quality));

                try
                {
                    using (var stream = new MemoryStream())
                    {
                        var drawingFormat = GetDrawingImageFormat(format);

                        if (format.SupportsQuality())
                        {
                            SaveWithQualityToStream(stream, drawingFormat, quality);
                        }
                        else
                        {
                            _image.Save(stream, drawingFormat);
                        }

                        return stream.ToArray();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Export with quality failed: {ex.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// Gets the recommended format based on image characteristics
        /// </summary>
        /// <returns>Recommended ImageFormat for this image</returns>
        public ImageFormat GetRecommendedExportFormat()
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return ImageFormat.Png; // Safe default

                try
                {
                    // Check if image has transparency
                    var hasTransparency = HasTransparency();

                    // Check if image is photographic (many colors)
                    var isPhotographic = IsPhotographicImage();

                    // Check if image is simple (few colors)
                    var isSimple = IsSimpleImage();

                    // Decision logic
                    if (hasTransparency)
                    {
                        return ImageFormat.Png; // PNG supports transparency
                    }
                    else if (isPhotographic)
                    {
                        return ImageFormat.Jpeg; // JPEG is good for photos
                    }
                    else if (isSimple)
                    {
                        return ImageFormat.Gif; // GIF is good for simple images
                    }
                    else
                    {
                        return ImageFormat.Png; // PNG is a good general-purpose format
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error determining recommended format: {ex.Message}");
                    return ImageFormat.Png; // Safe fallback
                }
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Loads image data from a byte array
        /// </summary>
        /// <param name="data">The image data</param>
        /// <returns>True if loading was successful; otherwise, false</returns>
        private bool LoadFromBytes(byte[] data)
        {
            try
            {
                using (var stream = new MemoryStream(data))
                {
                    lock (_lockObject)
                    {
                        _image?.Dispose();
                        _image = Image.FromStream(stream);
                        _isLoaded = true;
                        return true;
                    }
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Converts an image to a byte array
        /// </summary>
        /// <param name="image">The image to convert</param>
        /// <returns>The image as byte array, or null if conversion failed</returns>
        private byte[] ImageToByteArray(Image image)
        {
            if (image == null)
                return null;

            try
            {
                using (var stream = new MemoryStream())
                {
                    image.Save(stream, System.Drawing.Imaging.ImageFormat.Png);
                    return stream.ToArray();
                }
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Saves an image with quality settings
        /// </summary>
        /// <param name="filePath">The file path to save to</param>
        /// <param name="format">The image format</param>
        /// <param name="quality">The quality setting (0-100)</param>
        private void SaveWithQuality(string filePath, System.Drawing.Imaging.ImageFormat format, int quality)
        {
            var encoderParameters = new EncoderParameters(1);
            encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, quality);

            var codec = ImageCodecInfo.GetImageEncoders()
                .FirstOrDefault(c => c.FormatID == format.Guid);

            if (codec != null)
            {
                _image.Save(filePath, codec, encoderParameters);
            }
            else
            {
                _image.Save(filePath, format);
            }
        }

        /// <summary>
        /// Saves an image with quality settings to a stream
        /// </summary>
        /// <param name="stream">The stream to save to</param>
        /// <param name="format">The image format</param>
        /// <param name="quality">The quality setting (0-100)</param>
        private void SaveWithQualityToStream(Stream stream, System.Drawing.Imaging.ImageFormat format, int quality)
        {
            var encoderParameters = new EncoderParameters(1);
            encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, quality);

            var codec = ImageCodecInfo.GetImageEncoders()
                .FirstOrDefault(c => c.FormatID == format.Guid);

            if (codec != null)
            {
                _image.Save(stream, codec, encoderParameters);
            }
            else
            {
                _image.Save(stream, format);
            }
        }

        /// <summary>
        /// Marks the image as modified
        /// </summary>
        private void MarkAsModified()
        {
            _isModified = true;
            _lastModifiedTime = DateTime.Now;
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the epoch image and releases all resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes of the epoch image resources
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                lock (_lockObject)
                {
                    _image?.Dispose();
                    _disposed = true;
                }
            }
        }

        #endregion
    }
}