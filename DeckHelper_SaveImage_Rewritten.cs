using System;
using System.IO;
using System.Threading.Tasks;
using System.Drawing;
using System.Drawing.Imaging;
using FS.AS.Core.Imaging;
using FS.AS.Core.LSDesignHelper.Net.HelperClasses;

namespace FS.AS.Core.LSDesignHelper.Net.HelperClasses
{
    /// <summary>
    /// Rewritten DeckHelper with modern .NET implementation
    /// Replaces COM-based EpochImages with modern EpochImage implementation
    /// </summary>
    internal static partial class DeckHelper
    {
        #region Modern SaveImage Implementation

        /// <summary>
        /// Saves an image file to the database using modern EpochImage implementation
        /// Replaces the legacy COM-based approach with modern .NET practices
        /// </summary>
        /// <param name="filename">The full path to the image file to save</param>
        /// <returns>The database ID of the saved image</returns>
        /// <exception cref="ArgumentException">Thrown when filename is null or empty</exception>
        /// <exception cref="FileNotFoundException">Thrown when the specified file does not exist</exception>
        /// <exception cref="InvalidOperationException">Thrown when unable to save to database</exception>
        private static async Task<int> SaveImageAsync(string filename)
        {
            // Input validation
            if (string.IsNullOrWhiteSpace(filename))
                throw new ArgumentException("Filename cannot be null or empty", nameof(filename));

            if (!File.Exists(filename))
                throw new FileNotFoundException($"Image file not found: {filename}", filename);

            try
            {
                // Create EpochImage from file
                var epochImage = new EpochImage(DateTime.Now, filename);
                
                // Load the image data
                if (!await epochImage.LoadAsync())
                {
                    throw new InvalidOperationException($"Failed to load image from file: {filename}");
                }

                // Set image properties
                epochImage.Name = Path.GetFileNameWithoutExtension(filename);
                epochImage.Description = $"Deck image saved from {filename}";
                epochImage.Quality = 85; // High quality for deck images
                epochImage.CompressionType = "JPEG";

                // Add metadata
                epochImage.Metadata["OriginalFilePath"] = filename;
                epochImage.Metadata["SavedDateTime"] = DateTime.Now;
                epochImage.Metadata["ImageType"] = "DeckImage";
                epochImage.Metadata["FileSize"] = new FileInfo(filename).Length;

                // Add tags
                epochImage.Tags.Add("deck");
                epochImage.Tags.Add("automation");
                epochImage.Tags.Add("layout");

                // Save to database (this would need to be implemented based on your database layer)
                var databaseId = await SaveEpochImageToDatabaseAsync(epochImage);

                return databaseId;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    $"Unable to save deck image {filename} to the database. {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Synchronous version of SaveImage for backward compatibility
        /// </summary>
        /// <param name="filename">The full path to the image file to save</param>
        /// <returns>The database ID of the saved image</returns>
        private static int SaveImage(string filename)
        {
            try
            {
                return SaveImageAsync(filename).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                throw new Exception(
                    $"Unable to save deck image {filename} to the database. There may be a problem with the connection.",
                    ex);
            }
        }

        /// <summary>
        /// Enhanced version that accepts an EpochImage directly
        /// </summary>
        /// <param name="epochImage">The EpochImage to save</param>
        /// <returns>The database ID of the saved image</returns>
        private static async Task<int> SaveImageAsync(IEpochImage epochImage)
        {
            if (epochImage == null)
                throw new ArgumentNullException(nameof(epochImage));

            try
            {
                // Ensure image is loaded
                if (!epochImage.IsLoaded)
                {
                    if (!await epochImage.LoadAsync())
                    {
                        throw new InvalidOperationException("Failed to load epoch image data");
                    }
                }

                // Validate image data
                if (epochImage.Image == null)
                {
                    throw new InvalidOperationException("Epoch image contains no image data");
                }

                // Set default properties if not already set
                if (string.IsNullOrEmpty(epochImage.Name))
                {
                    epochImage.Name = $"DeckImage_{epochImage.Id}";
                }

                if (string.IsNullOrEmpty(epochImage.Description))
                {
                    epochImage.Description = "Deck layout image";
                }

                // Ensure quality is set for JPEG compression
                if (epochImage.Quality <= 0)
                {
                    epochImage.Quality = 85;
                }

                // Add standard deck image metadata
                epochImage.Metadata["ImageType"] = "DeckImage";
                epochImage.Metadata["SavedDateTime"] = DateTime.Now;
                epochImage.Metadata["ImageDimensions"] = $"{epochImage.Width}x{epochImage.Height}";
                epochImage.Metadata["ImageFormat"] = epochImage.Format;

                // Add standard tags
                if (!epochImage.Tags.Contains("deck"))
                    epochImage.Tags.Add("deck");
                if (!epochImage.Tags.Contains("automation"))
                    epochImage.Tags.Add("automation");

                // Save to database
                var databaseId = await SaveEpochImageToDatabaseAsync(epochImage);

                return databaseId;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    $"Unable to save epoch image to the database. {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Saves an image from a Bitmap object
        /// </summary>
        /// <param name="bitmap">The bitmap to save</param>
        /// <param name="name">Optional name for the image</param>
        /// <returns>The database ID of the saved image</returns>
        private static async Task<int> SaveImageAsync(Bitmap bitmap, string name = null)
        {
            if (bitmap == null)
                throw new ArgumentNullException(nameof(bitmap));

            try
            {
                // Create EpochImage from bitmap
                var epochImage = new EpochImage(DateTime.Now, bitmap);
                
                // Set properties
                epochImage.Name = name ?? $"DeckBitmap_{DateTime.Now:yyyyMMdd_HHmmss}";
                epochImage.Description = "Deck image created from bitmap";
                epochImage.Quality = 85;
                epochImage.CompressionType = "JPEG";

                // Add metadata
                epochImage.Metadata["Source"] = "Bitmap";
                epochImage.Metadata["CreatedDateTime"] = DateTime.Now;
                epochImage.Metadata["ImageType"] = "DeckImage";

                // Save using the EpochImage overload
                return await SaveImageAsync(epochImage);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    $"Unable to save bitmap to database. {ex.Message}", ex);
            }
        }

        #endregion

        #region Database Integration Methods

        /// <summary>
        /// Saves an EpochImage to the database
        /// This method would need to be implemented based on your specific database layer
        /// </summary>
        /// <param name="epochImage">The EpochImage to save</param>
        /// <returns>The database ID of the saved image</returns>
        private static async Task<int> SaveEpochImageToDatabaseAsync(IEpochImage epochImage)
        {
            try
            {
                // TODO: Replace this with your actual database implementation
                // This is a placeholder that demonstrates the interface

                // Example implementation using a hypothetical database service
                /*
                var imageService = new ImageDatabaseService();
                
                // Convert EpochImage to database format
                var imageData = epochImage.Export(ImageFormat.Jpeg);
                var thumbnailData = CreateThumbnail(epochImage);
                
                // Save to database
                var imageRecord = new ImageRecord
                {
                    Name = epochImage.Name,
                    Description = epochImage.Description,
                    ImageData = imageData,
                    ThumbnailData = thumbnailData,
                    Metadata = epochImage.Metadata,
                    Tags = epochImage.Tags,
                    CreatedDate = epochImage.CreationTime,
                    ModifiedDate = epochImage.LastModifiedTime,
                    Width = epochImage.Width,
                    Height = epochImage.Height,
                    Format = epochImage.Format,
                    Quality = epochImage.Quality
                };
                
                var databaseId = await imageService.SaveImageAsync(imageRecord);
                return databaseId;
                */

                // For now, return a mock ID
                // In a real implementation, this would interact with your database
                await Task.Delay(10); // Simulate async database operation
                return new Random().Next(1000, 9999); // Mock database ID
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    "Failed to save image to database", ex);
            }
        }

        /// <summary>
        /// Creates a thumbnail from an EpochImage
        /// </summary>
        /// <param name="epochImage">The source image</param>
        /// <returns>Thumbnail image data</returns>
        private static byte[] CreateThumbnail(IEpochImage epochImage)
        {
            try
            {
                using (var thumbnail = epochImage.GetThumbnail(100, 100))
                {
                    if (thumbnail == null)
                        return null;

                    using (var stream = new MemoryStream())
                    {
                        thumbnail.Save(stream, System.Drawing.Imaging.ImageFormat.Jpeg);
                        return stream.ToArray();
                    }
                }
            }
            catch
            {
                return null;
            }
        }

        #endregion

        #region Enhanced Public Methods

        /// <summary>
        /// Enhanced version of SaveDeckImage with better error handling and async support
        /// </summary>
        /// <param name="filename">The filename (without path) to save</param>
        /// <returns>The database ID of the saved image</returns>
        public static async Task<int> SaveDeckImageAsync(string filename)
        {
            if (string.IsNullOrWhiteSpace(filename))
                throw new ArgumentException("Filename cannot be null or empty", nameof(filename));

            var fullPath = SettingsHelper.GetDeckImagesDirectory().Combine(filename);
            return await SaveImageAsync(fullPath);
        }

        /// <summary>
        /// Synchronous version for backward compatibility
        /// </summary>
        /// <param name="filename">The filename to save</param>
        /// <returns>The database ID of the saved image</returns>
        public static int SaveDeckImage(string filename)
        {
            return SaveDeckImageAsync(filename).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Enhanced version of SaveDeckLayout with better error handling and async support
        /// </summary>
        /// <returns>The database ID of the saved deck layout image</returns>
        public static async Task<int> SaveDeckLayoutAsync()
        {
            string tempFileName = null;
            
            try
            {
                tempFileName = Path.GetTempFileName();
                
                using (var bitmap = TrainingHelper.GetDeckImage(0.625))
                {
                    if (bitmap == null)
                        throw new InvalidOperationException("Failed to generate deck image");

                    // Save to temporary file first
                    bitmap.Save(tempFileName, System.Drawing.Imaging.ImageFormat.Jpeg);
                    
                    // Create EpochImage and save to database
                    var epochImage = new EpochImage(DateTime.Now, tempFileName);
                    epochImage.Name = $"DeckLayout_{DateTime.Now:yyyyMMdd_HHmmss}";
                    epochImage.Description = "Automated deck layout capture";
                    epochImage.Quality = 85;
                    
                    // Add specific metadata for deck layouts
                    epochImage.Metadata["ImageType"] = "DeckLayout";
                    epochImage.Metadata["Scale"] = 0.625;
                    epochImage.Metadata["GeneratedDateTime"] = DateTime.Now;
                    
                    epochImage.Tags.Add("deck");
                    epochImage.Tags.Add("layout");
                    epochImage.Tags.Add("automated");
                    
                    return await SaveImageAsync(epochImage);
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException("Unable to load sub element reference deck image", ex);
            }
            finally
            {
                // Clean up temporary file
                if (!string.IsNullOrEmpty(tempFileName) && File.Exists(tempFileName))
                {
                    try
                    {
                        File.Delete(tempFileName);
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }
                }
            }
        }

        /// <summary>
        /// Synchronous version for backward compatibility
        /// </summary>
        /// <returns>The database ID of the saved deck layout image</returns>
        public static int SaveDeckLayout()
        {
            return SaveDeckLayoutAsync().GetAwaiter().GetResult();
        }

        #endregion
    }
}
