using System;
using System.IO;
using System.Threading.Tasks;
using FS.AS.Core.Imaging;

namespace FS.AS.Core.Imaging.Examples
{
    /// <summary>
    /// Example usage of EpochImageExportFormat enumeration and its extension methods
    /// </summary>
    public class EpochImageExportFormatExample
    {
        public static void RunExamples()
        {
            Console.WriteLine("EpochImageExportFormat Examples");
            Console.WriteLine("===============================");

            // Example 1: Basic format information
            Example1_BasicFormatInfo();

            // Example 2: Format capabilities
            Example2_FormatCapabilities();

            // Example 3: Format selection based on requirements
            Example3_FormatSelection();

            // Example 4: File naming with extensions
            Example4_FileNaming();

            // Example 5: Format validation
            Example5_FormatValidation();
        }

        private static void Example1_BasicFormatInfo()
        {
            Console.WriteLine("\n1. Basic Format Information");
            Console.WriteLine("---------------------------");

            // Display information for all formats
            foreach (EpochImageExportFormat format in Enum.GetValues<EpochImageExportFormat>())
            {
                Console.WriteLine($"{format}:");
                Console.WriteLine($"  Extension: {format.GetFileExtension()}");
                Console.WriteLine($"  MIME Type: {format.GetMimeType()}");
                Console.WriteLine($"  Description: {format.GetDescription()}");
                Console.WriteLine();
            }
        }

        private static void Example2_FormatCapabilities()
        {
            Console.WriteLine("\n2. Format Capabilities");
            Console.WriteLine("----------------------");

            Console.WriteLine("Format Capabilities Matrix:");
            Console.WriteLine("Format".PadRight(15) + "Binary Data".PadRight(12) + "Human Readable".PadRight(16) + "Compression");
            Console.WriteLine(new string('-', 60));

            foreach (EpochImageExportFormat format in Enum.GetValues<EpochImageExportFormat>())
            {
                var binarySupport = format.SupportsBinaryData() ? "Yes" : "No";
                var humanReadable = format.IsHumanReadable() ? "Yes" : "No";
                var compression = new string('*', format.GetCompressionEfficiency());

                Console.WriteLine($"{format.ToString().PadRight(15)}{binarySupport.PadRight(12)}{humanReadable.PadRight(16)}{compression}");
            }
        }

        private static void Example3_FormatSelection()
        {
            Console.WriteLine("\n3. Format Selection Based on Requirements");
            Console.WriteLine("-----------------------------------------");

            // Scenario 1: Need human-readable format with binary data support
            Console.WriteLine("Scenario 1: Human-readable format with binary data support");
            var humanReadableWithBinary = GetFormatsForRequirements(humanReadable: true, binaryData: true);
            Console.WriteLine($"Recommended formats: {string.Join(", ", humanReadableWithBinary)}");

            // Scenario 2: Need maximum compression efficiency
            Console.WriteLine("\nScenario 2: Maximum compression efficiency");
            var highCompression = GetFormatsWithHighCompression(4);
            Console.WriteLine($"High compression formats: {string.Join(", ", highCompression)}");

            // Scenario 3: Need database-like capabilities
            Console.WriteLine("\nScenario 3: Database-like capabilities");
            var databaseFormats = new[] { EpochImageExportFormat.Sqlite, EpochImageExportFormat.Hdf5 };
            Console.WriteLine($"Database formats: {string.Join(", ", databaseFormats)}");

            // Scenario 4: Need web-friendly formats
            Console.WriteLine("\nScenario 4: Web-friendly formats");
            var webFormats = new[] { EpochImageExportFormat.Json, EpochImageExportFormat.Xml };
            Console.WriteLine($"Web-friendly formats: {string.Join(", ", webFormats)}");
        }

        private static void Example4_FileNaming()
        {
            Console.WriteLine("\n4. File Naming with Extensions");
            Console.WriteLine("------------------------------");

            var baseFileName = "epoch_images_export";
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            Console.WriteLine("Generated file names:");
            foreach (EpochImageExportFormat format in Enum.GetValues<EpochImageExportFormat>())
            {
                var fileName = $"{baseFileName}_{timestamp}{format.GetFileExtension()}";
                Console.WriteLine($"{format}: {fileName}");
            }
        }

        private static void Example5_FormatValidation()
        {
            Console.WriteLine("\n5. Format Validation and Recommendations");
            Console.WriteLine("----------------------------------------");

            // Validate format for different use cases
            ValidateFormatForUseCase(EpochImageExportFormat.Json, "Web API export");
            ValidateFormatForUseCase(EpochImageExportFormat.Csv, "Spreadsheet analysis");
            ValidateFormatForUseCase(EpochImageExportFormat.Binary, "High-performance storage");
            ValidateFormatForUseCase(EpochImageExportFormat.Hdf5, "Scientific data analysis");
            ValidateFormatForUseCase(EpochImageExportFormat.Excel, "Business reporting");
        }

        private static void ValidateFormatForUseCase(EpochImageExportFormat format, string useCase)
        {
            Console.WriteLine($"\nUse Case: {useCase}");
            Console.WriteLine($"Selected Format: {format}");
            Console.WriteLine($"Validation Results:");

            // Check various aspects
            var binarySupport = format.SupportsBinaryData();
            var humanReadable = format.IsHumanReadable();
            var compression = format.GetCompressionEfficiency();

            Console.WriteLine($"  ✓ Binary data support: {(binarySupport ? "Available" : "Not available")}");
            Console.WriteLine($"  ✓ Human readable: {(humanReadable ? "Yes" : "No")}");
            Console.WriteLine($"  ✓ Compression efficiency: {compression}/5");

            // Provide recommendations
            if (useCase.Contains("Web") && !humanReadable)
            {
                Console.WriteLine($"  ⚠ Warning: Binary format may not be ideal for web use");
            }

            if (useCase.Contains("analysis") && compression < 3)
            {
                Console.WriteLine($"  ⚠ Warning: Low compression may impact storage efficiency");
            }

            if (useCase.Contains("Spreadsheet") && !humanReadable)
            {
                Console.WriteLine($"  ⚠ Warning: Binary format not suitable for spreadsheet import");
            }
        }

        private static string[] GetFormatsForRequirements(bool humanReadable = false, bool binaryData = false)
        {
            var matchingFormats = new List<string>();

            foreach (EpochImageExportFormat format in Enum.GetValues<EpochImageExportFormat>())
            {
                bool meetsRequirements = true;

                if (humanReadable && !format.IsHumanReadable())
                    meetsRequirements = false;

                if (binaryData && !format.SupportsBinaryData())
                    meetsRequirements = false;

                if (meetsRequirements)
                    matchingFormats.Add(format.ToString());
            }

            return matchingFormats.ToArray();
        }

        private static string[] GetFormatsWithHighCompression(int minCompressionLevel)
        {
            var highCompressionFormats = new List<string>();

            foreach (EpochImageExportFormat format in Enum.GetValues<EpochImageExportFormat>())
            {
                if (format.GetCompressionEfficiency() >= minCompressionLevel)
                {
                    highCompressionFormats.Add(format.ToString());
                }
            }

            return highCompressionFormats.ToArray();
        }
    }

    /// <summary>
    /// Example implementation showing how to use export formats in practice
    /// </summary>
    public class EpochImageExporter
    {
        /// <summary>
        /// Exports epoch images using the specified format
        /// </summary>
        /// <param name="images">The images to export</param>
        /// <param name="format">The export format</param>
        /// <param name="outputPath">The output file path (without extension)</param>
        /// <returns>True if export was successful</returns>
        public static async Task<bool> ExportAsync(IEpochImages images, EpochImageExportFormat format, string outputPath)
        {
            try
            {
                // Add appropriate file extension
                var fullPath = outputPath + format.GetFileExtension();

                // Validate format capabilities
                if (!ValidateFormatForImages(format, images))
                {
                    Console.WriteLine($"Format {format} is not suitable for the provided images");
                    return false;
                }

                // Export based on format
                switch (format)
                {
                    case EpochImageExportFormat.Json:
                        return await ExportToJsonAsync(images, fullPath);

                    case EpochImageExportFormat.Xml:
                        return await ExportToXmlAsync(images, fullPath);

                    case EpochImageExportFormat.Csv:
                        return await ExportToCsvAsync(images, fullPath);

                    case EpochImageExportFormat.Binary:
                        return await ExportToBinaryAsync(images, fullPath);

                    case EpochImageExportFormat.Excel:
                        return await ExportToExcelAsync(images, fullPath);

                    default:
                        Console.WriteLine($"Export format {format} is not yet implemented");
                        return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Export failed: {ex.Message}");
                return false;
            }
        }

        private static bool ValidateFormatForImages(EpochImageExportFormat format, IEpochImages images)
        {
            // Check if format supports binary data if images contain large data
            if (images.Count > 100 && !format.SupportsBinaryData())
            {
                return false; // Large datasets need binary support
            }

            // Check compression efficiency for large collections
            if (images.Count > 1000 && format.GetCompressionEfficiency() < 3)
            {
                return false; // Large collections need good compression
            }

            return true;
        }

        private static async Task<bool> ExportToJsonAsync(IEpochImages images, string filePath)
        {
            // Implementation would serialize images to JSON format
            Console.WriteLine($"Exporting {images.Count} images to JSON: {filePath}");
            return await Task.FromResult(true);
        }

        private static async Task<bool> ExportToXmlAsync(IEpochImages images, string filePath)
        {
            // Implementation would serialize images to XML format
            Console.WriteLine($"Exporting {images.Count} images to XML: {filePath}");
            return await Task.FromResult(true);
        }

        private static async Task<bool> ExportToCsvAsync(IEpochImages images, string filePath)
        {
            // Implementation would export metadata to CSV format
            Console.WriteLine($"Exporting {images.Count} images metadata to CSV: {filePath}");
            return await Task.FromResult(true);
        }

        private static async Task<bool> ExportToBinaryAsync(IEpochImages images, string filePath)
        {
            // Implementation would serialize to custom binary format
            Console.WriteLine($"Exporting {images.Count} images to binary format: {filePath}");
            return await Task.FromResult(true);
        }

        private static async Task<bool> ExportToExcelAsync(IEpochImages images, string filePath)
        {
            // Implementation would export to Excel workbook
            Console.WriteLine($"Exporting {images.Count} images to Excel: {filePath}");
            return await Task.FromResult(true);
        }
    }
}
