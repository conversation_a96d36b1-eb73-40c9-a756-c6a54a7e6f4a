using System;
using System.ComponentModel;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// Enumeration of supported image formats for EpochImage export and import operations
    /// Provides a comprehensive list of image formats with descriptions and metadata
    /// </summary>
    public enum ImageFormat
    {
        /// <summary>
        /// Joint Photographic Experts Group format
        /// Lossy compression, good for photographs, supports quality settings
        /// File extensions: .jpg, .jpeg
        /// MIME type: image/jpeg
        /// </summary>
        [Description("JPEG - Joint Photographic Experts Group")]
        Jpeg = 0,

        /// <summary>
        /// Portable Network Graphics format
        /// Lossless compression, supports transparency, good for graphics and screenshots
        /// File extensions: .png
        /// MIME type: image/png
        /// </summary>
        [Description("PNG - Portable Network Graphics")]
        Png = 1,

        /// <summary>
        /// Bitmap format
        /// Uncompressed or lightly compressed, large file sizes, universal support
        /// File extensions: .bmp
        /// MIME type: image/bmp
        /// </summary>
        [Description("BMP - Bitmap")]
        Bmp = 2,

        /// <summary>
        /// Graphics Interchange Format
        /// Lossless compression, supports transparency and animation, limited to 256 colors
        /// File extensions: .gif
        /// MIME type: image/gif
        /// </summary>
        [Description("GIF - Graphics Interchange Format")]
        Gif = 3,

        /// <summary>
        /// Tagged Image File Format
        /// Lossless compression, supports multiple pages, good for archival
        /// File extensions: .tiff, .tif
        /// MIME type: image/tiff
        /// </summary>
        [Description("TIFF - Tagged Image File Format")]
        Tiff = 4,

        /// <summary>
        /// Icon format
        /// Multiple resolutions in single file, supports transparency
        /// File extensions: .ico
        /// MIME type: image/x-icon
        /// </summary>
        [Description("ICO - Icon")]
        Ico = 5,

        /// <summary>
        /// Enhanced Metafile format
        /// Vector graphics format, scalable, Windows-specific
        /// File extensions: .emf
        /// MIME type: image/emf
        /// </summary>
        [Description("EMF - Enhanced Metafile")]
        Emf = 6,

        /// <summary>
        /// Windows Metafile format
        /// Vector graphics format, legacy Windows format
        /// File extensions: .wmf
        /// MIME type: image/wmf
        /// </summary>
        [Description("WMF - Windows Metafile")]
        Wmf = 7,

        /// <summary>
        /// Exchangeable Image File format
        /// JPEG with metadata, used by digital cameras
        /// File extensions: .jpg, .jpeg
        /// MIME type: image/jpeg
        /// </summary>
        [Description("EXIF - Exchangeable Image File")]
        Exif = 8,

        /// <summary>
        /// Memory Bitmap format
        /// In-memory bitmap representation, not typically saved to disk
        /// File extensions: N/A
        /// MIME type: N/A
        /// </summary>
        [Description("Memory BMP - In-Memory Bitmap")]
        MemoryBmp = 9,

        /// <summary>
        /// WebP format
        /// Modern format with excellent compression, supports transparency and animation
        /// File extensions: .webp
        /// MIME type: image/webp
        /// </summary>
        [Description("WebP - Web Picture")]
        WebP = 10,

        /// <summary>
        /// JPEG 2000 format
        /// Advanced JPEG with better compression and features
        /// File extensions: .jp2, .j2k
        /// MIME type: image/jp2
        /// </summary>
        [Description("JPEG 2000 - Advanced JPEG")]
        Jpeg2000 = 11,

        /// <summary>
        /// High Efficiency Image Format
        /// Modern format with excellent compression, successor to JPEG
        /// File extensions: .heif, .heic
        /// MIME type: image/heif
        /// </summary>
        [Description("HEIF - High Efficiency Image Format")]
        Heif = 12,

        /// <summary>
        /// AV1 Image File Format
        /// Based on AV1 video codec, excellent compression
        /// File extensions: .avif
        /// MIME type: image/avif
        /// </summary>
        [Description("AVIF - AV1 Image File Format")]
        Avif = 13,

        /// <summary>
        /// Scalable Vector Graphics format
        /// XML-based vector graphics, scalable, web-friendly
        /// File extensions: .svg
        /// MIME type: image/svg+xml
        /// </summary>
        [Description("SVG - Scalable Vector Graphics")]
        Svg = 14,

        /// <summary>
        /// Portable Document Format (image)
        /// Document format that can contain images
        /// File extensions: .pdf
        /// MIME type: application/pdf
        /// </summary>
        [Description("PDF - Portable Document Format")]
        Pdf = 15,

        /// <summary>
        /// Raw image format
        /// Unprocessed sensor data from digital cameras
        /// File extensions: .raw, .cr2, .nef, .arw, etc.
        /// MIME type: image/raw
        /// </summary>
        [Description("RAW - Raw Image Data")]
        Raw = 16,

        /// <summary>
        /// Photoshop Document format
        /// Adobe Photoshop native format with layers
        /// File extensions: .psd
        /// MIME type: image/vnd.adobe.photoshop
        /// </summary>
        [Description("PSD - Photoshop Document")]
        Psd = 17,

        /// <summary>
        /// Digital Negative format
        /// Adobe's open standard for raw files
        /// File extensions: .dng
        /// MIME type: image/dng
        /// </summary>
        [Description("DNG - Digital Negative")]
        Dng = 18,

        /// <summary>
        /// Unknown or unsupported format
        /// Used as fallback when format cannot be determined
        /// </summary>
        [Description("Unknown Format")]
        Unknown = 99
    }

    /// <summary>
    /// Extension methods for ImageFormat enum to provide additional functionality
    /// </summary>
    public static class ImageFormatExtensions
    {
        /// <summary>
        /// Gets the description attribute value for an ImageFormat enum value
        /// </summary>
        /// <param name="format">The ImageFormat enum value</param>
        /// <returns>The description string</returns>
        public static string GetDescription(this ImageFormat format)
        {
            var field = format.GetType().GetField(format.ToString());
            if (field != null)
            {
                var attribute = (DescriptionAttribute)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));
                if (attribute != null)
                    return attribute.Description;
            }
            return format.ToString();
        }

        /// <summary>
        /// Gets the typical file extension for an ImageFormat
        /// </summary>
        /// <param name="format">The ImageFormat enum value</param>
        /// <returns>The file extension including the dot (e.g., ".jpg")</returns>
        public static string GetFileExtension(this ImageFormat format)
        {
            switch (format)
            {
                case ImageFormat.Jpeg:
                case ImageFormat.Exif:
                    return ".jpg";
                case ImageFormat.Png:
                    return ".png";
                case ImageFormat.Bmp:
                case ImageFormat.MemoryBmp:
                    return ".bmp";
                case ImageFormat.Gif:
                    return ".gif";
                case ImageFormat.Tiff:
                    return ".tiff";
                case ImageFormat.Ico:
                    return ".ico";
                case ImageFormat.Emf:
                    return ".emf";
                case ImageFormat.Wmf:
                    return ".wmf";
                case ImageFormat.WebP:
                    return ".webp";
                case ImageFormat.Jpeg2000:
                    return ".jp2";
                case ImageFormat.Heif:
                    return ".heif";
                case ImageFormat.Avif:
                    return ".avif";
                case ImageFormat.Svg:
                    return ".svg";
                case ImageFormat.Pdf:
                    return ".pdf";
                case ImageFormat.Raw:
                    return ".raw";
                case ImageFormat.Psd:
                    return ".psd";
                case ImageFormat.Dng:
                    return ".dng";
                default:
                    return ".bin";
            }
        }

        /// <summary>
        /// Gets the MIME type for an ImageFormat
        /// </summary>
        /// <param name="format">The ImageFormat enum value</param>
        /// <returns>The MIME type string</returns>
        public static string GetMimeType(this ImageFormat format)
        {
            switch (format)
            {
                case ImageFormat.Jpeg:
                case ImageFormat.Exif:
                    return "image/jpeg";
                case ImageFormat.Png:
                    return "image/png";
                case ImageFormat.Bmp:
                case ImageFormat.MemoryBmp:
                    return "image/bmp";
                case ImageFormat.Gif:
                    return "image/gif";
                case ImageFormat.Tiff:
                    return "image/tiff";
                case ImageFormat.Ico:
                    return "image/x-icon";
                case ImageFormat.Emf:
                    return "image/emf";
                case ImageFormat.Wmf:
                    return "image/wmf";
                case ImageFormat.WebP:
                    return "image/webp";
                case ImageFormat.Jpeg2000:
                    return "image/jp2";
                case ImageFormat.Heif:
                    return "image/heif";
                case ImageFormat.Avif:
                    return "image/avif";
                case ImageFormat.Svg:
                    return "image/svg+xml";
                case ImageFormat.Pdf:
                    return "application/pdf";
                case ImageFormat.Raw:
                    return "image/raw";
                case ImageFormat.Psd:
                    return "image/vnd.adobe.photoshop";
                case ImageFormat.Dng:
                    return "image/dng";
                default:
                    return "application/octet-stream";
            }
        }

        /// <summary>
        /// Determines if the format supports quality settings
        /// </summary>
        /// <param name="format">The ImageFormat enum value</param>
        /// <returns>True if the format supports quality settings</returns>
        public static bool SupportsQuality(this ImageFormat format)
        {
            switch (format)
            {
                case ImageFormat.Jpeg:
                case ImageFormat.Exif:
                case ImageFormat.WebP:
                case ImageFormat.Jpeg2000:
                case ImageFormat.Heif:
                case ImageFormat.Avif:
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// Determines if the format supports transparency
        /// </summary>
        /// <param name="format">The ImageFormat enum value</param>
        /// <returns>True if the format supports transparency</returns>
        public static bool SupportsTransparency(this ImageFormat format)
        {
            switch (format)
            {
                case ImageFormat.Png:
                case ImageFormat.Gif:
                case ImageFormat.Tiff:
                case ImageFormat.Ico:
                case ImageFormat.WebP:
                case ImageFormat.Heif:
                case ImageFormat.Avif:
                case ImageFormat.Svg:
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// Determines if the format uses lossy compression
        /// </summary>
        /// <param name="format">The ImageFormat enum value</param>
        /// <returns>True if the format uses lossy compression</returns>
        public static bool IsLossy(this ImageFormat format)
        {
            switch (format)
            {
                case ImageFormat.Jpeg:
                case ImageFormat.Exif:
                case ImageFormat.WebP: // Can be lossy or lossless
                case ImageFormat.Jpeg2000: // Can be lossy or lossless
                case ImageFormat.Heif:
                case ImageFormat.Avif:
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// Determines if the format is commonly supported by web browsers
        /// </summary>
        /// <param name="format">The ImageFormat enum value</param>
        /// <returns>True if the format is web-compatible</returns>
        public static bool IsWebCompatible(this ImageFormat format)
        {
            switch (format)
            {
                case ImageFormat.Jpeg:
                case ImageFormat.Png:
                case ImageFormat.Gif:
                case ImageFormat.WebP:
                case ImageFormat.Svg:
                case ImageFormat.Avif: // Modern browsers
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// Gets all file extensions associated with a format
        /// </summary>
        /// <param name="format">The ImageFormat enum value</param>
        /// <returns>Array of file extensions</returns>
        public static string[] GetAllFileExtensions(this ImageFormat format)
        {
            switch (format)
            {
                case ImageFormat.Jpeg:
                case ImageFormat.Exif:
                    return new[] { ".jpg", ".jpeg" };
                case ImageFormat.Tiff:
                    return new[] { ".tiff", ".tif" };
                case ImageFormat.Jpeg2000:
                    return new[] { ".jp2", ".j2k", ".jpf", ".jpx" };
                case ImageFormat.Heif:
                    return new[] { ".heif", ".heic" };
                case ImageFormat.Raw:
                    return new[] { ".raw", ".cr2", ".nef", ".arw", ".dng", ".orf", ".rw2" };
                default:
                    return new[] { GetFileExtension(format) };
            }
        }

        /// <summary>
        /// Parses a file extension to determine the ImageFormat
        /// </summary>
        /// <param name="extension">The file extension (with or without dot)</param>
        /// <returns>The corresponding ImageFormat, or Unknown if not recognized</returns>
        public static ImageFormat FromFileExtension(string extension)
        {
            if (string.IsNullOrEmpty(extension))
                return ImageFormat.Unknown;

            // Normalize extension
            extension = extension.ToLowerInvariant();
            if (!extension.StartsWith("."))
                extension = "." + extension;

            switch (extension)
            {
                case ".jpg":
                case ".jpeg":
                    return ImageFormat.Jpeg;
                case ".png":
                    return ImageFormat.Png;
                case ".bmp":
                    return ImageFormat.Bmp;
                case ".gif":
                    return ImageFormat.Gif;
                case ".tiff":
                case ".tif":
                    return ImageFormat.Tiff;
                case ".ico":
                    return ImageFormat.Ico;
                case ".emf":
                    return ImageFormat.Emf;
                case ".wmf":
                    return ImageFormat.Wmf;
                case ".webp":
                    return ImageFormat.WebP;
                case ".jp2":
                case ".j2k":
                case ".jpf":
                case ".jpx":
                    return ImageFormat.Jpeg2000;
                case ".heif":
                case ".heic":
                    return ImageFormat.Heif;
                case ".avif":
                    return ImageFormat.Avif;
                case ".svg":
                    return ImageFormat.Svg;
                case ".pdf":
                    return ImageFormat.Pdf;
                case ".raw":
                case ".cr2":
                case ".nef":
                case ".arw":
                case ".orf":
                case ".rw2":
                    return ImageFormat.Raw;
                case ".psd":
                    return ImageFormat.Psd;
                case ".dng":
                    return ImageFormat.Dng;
                default:
                    return ImageFormat.Unknown;
            }
        }
    }
}
