using System;
using System.Threading.Tasks;
using FS.AS.Core.Imaging;

namespace FS.AS.Core.Imaging.Examples
{
    /// <summary>
    /// Example usage of EpochImageEventArgs and related event argument classes
    /// </summary>
    public class EpochImageEventArgsExample
    {
        public static async Task RunExamples()
        {
            Console.WriteLine("EpochImageEventArgs Examples");
            Console.WriteLine("============================");

            // Example 1: Basic event handling
            await Example1_BasicEventHandling();

            // Example 2: Current image changed events
            await Example2_CurrentImageChangedEvents();

            // Example 3: Loading and saving events
            await Example3_LoadingAndSavingEvents();

            // Example 4: Transformation events
            await Example4_TransformationEvents();

            // Example 5: Event cancellation
            await Example5_EventCancellation();

            // Example 6: Custom event data
            await Example6_CustomEventData();
        }

        private static async Task Example1_BasicEventHandling()
        {
            Console.WriteLine("\n1. Basic Event Handling");
            Console.WriteLine("-----------------------");

            var epochImages = new EpochImages();

            // Subscribe to events
            epochImages.ImageAdded += OnImageAdded;
            epochImages.ImageRemoved += OnImageRemoved;

            // Create and add some images
            var image1 = new EpochImage(DateTime.Now);
            var image2 = new EpochImage(DateTime.Now.AddMinutes(1));

            epochImages.Add(image1);
            epochImages.Add(image2);

            // Remove an image
            epochImages.Remove(image1);

            // Clean up
            epochImages.ImageAdded -= OnImageAdded;
            epochImages.ImageRemoved -= OnImageRemoved;
            epochImages.Dispose();
        }

        private static async Task Example2_CurrentImageChangedEvents()
        {
            Console.WriteLine("\n2. Current Image Changed Events");
            Console.WriteLine("-------------------------------");

            var epochImages = new EpochImages();
            epochImages.CurrentImageChanged += OnCurrentImageChanged;

            // Add some images
            for (int i = 0; i < 5; i++)
            {
                var image = new EpochImage(DateTime.Now.AddMinutes(i));
                image.Name = $"Image {i + 1}";
                epochImages.Add(image);
            }

            // Navigate through images
            Console.WriteLine("Navigating through images...");
            epochImages.MoveFirst();
            await Task.Delay(100);

            epochImages.MoveNext();
            await Task.Delay(100);

            epochImages.MoveLast();
            await Task.Delay(100);

            epochImages.MovePrevious();
            await Task.Delay(100);

            // Direct navigation
            epochImages.SetCurrentImage(2);

            // Clean up
            epochImages.CurrentImageChanged -= OnCurrentImageChanged;
            epochImages.Dispose();
        }

        private static async Task Example3_LoadingAndSavingEvents()
        {
            Console.WriteLine("\n3. Loading and Saving Events");
            Console.WriteLine("-----------------------------");

            var image = new EpochImage(DateTime.Now);

            // Simulate loading events
            Console.WriteLine("Simulating image loading...");
            var loadingArgs = new ImageLoadingEventArgs(image, @"C:\Images\sample.jpg");
            
            // Simulate loading progress
            for (int progress = 0; progress <= 100; progress += 20)
            {
                loadingArgs.Progress = progress;
                loadingArgs.LoadedBytes = progress * 1024; // Simulate bytes loaded
                loadingArgs.TotalBytes = 100 * 1024;
                loadingArgs.LoadingState = progress < 100 ? ImageLoadingState.InProgress : ImageLoadingState.Completed;
                
                OnImageLoading(null, loadingArgs);
                await Task.Delay(50);
            }

            // Simulate saving events
            Console.WriteLine("\nSimulating image saving...");
            var savingArgs = new ImageSavingEventArgs(image, @"C:\Output\saved.jpg");
            savingArgs.Format = "JPEG";
            savingArgs.Quality = 85;
            savingArgs.OriginalSize = 150 * 1024;

            for (int progress = 0; progress <= 100; progress += 25)
            {
                savingArgs.Progress = progress;
                savingArgs.SavingState = progress < 100 ? ImageSavingState.InProgress : ImageSavingState.Completed;
                
                if (progress == 100)
                {
                    savingArgs.SavedSize = 120 * 1024; // Compressed size
                }
                
                OnImageSaving(null, savingArgs);
                await Task.Delay(50);
            }

            image.Dispose();
        }

        private static async Task Example4_TransformationEvents()
        {
            Console.WriteLine("\n4. Transformation Events");
            Console.WriteLine("------------------------");

            var originalImage = new EpochImage(DateTime.Now);

            // Simulate transformation events
            Console.WriteLine("Simulating image transformation...");
            var transformArgs = new ImageTransformationEventArgs(originalImage, "Resize");
            
            // Add transformation parameters
            transformArgs.Parameters["Width"] = 800;
            transformArgs.Parameters["Height"] = 600;
            transformArgs.Parameters["Quality"] = "High";
            transformArgs.PreservesOriginal = true;

            // Simulate transformation progress
            for (int progress = 0; progress <= 100; progress += 33)
            {
                transformArgs.Progress = progress;
                transformArgs.TransformationState = progress < 100 ? TransformationState.InProgress : TransformationState.Completed;
                
                if (progress == 100)
                {
                    // Create result image
                    transformArgs.ResultImage = new EpochImage(originalImage.Timestamp);
                    transformArgs.ResultImage.Name = "Resized Image";
                }
                
                OnImageTransformation(null, transformArgs);
                await Task.Delay(100);
            }

            originalImage.Dispose();
            transformArgs.ResultImage?.Dispose();
        }

        private static async Task Example5_EventCancellation()
        {
            Console.WriteLine("\n5. Event Cancellation");
            Console.WriteLine("---------------------");

            var epochImages = new EpochImages();
            
            // Subscribe to cancellable events
            epochImages.CurrentImageChanged += OnCurrentImageChangedCancellable;

            // Add images
            var image1 = new EpochImage(DateTime.Now);
            var image2 = new EpochImage(DateTime.Now.AddMinutes(1));
            epochImages.Add(image1);
            epochImages.Add(image2);

            // Try to navigate (will be cancelled)
            Console.WriteLine("Attempting navigation (will be cancelled)...");
            epochImages.SetCurrentImage(1);

            // Clean up
            epochImages.CurrentImageChanged -= OnCurrentImageChangedCancellable;
            epochImages.Dispose();
        }

        private static async Task Example6_CustomEventData()
        {
            Console.WriteLine("\n6. Custom Event Data");
            Console.WriteLine("--------------------");

            var image = new EpochImage(DateTime.Now);
            
            // Create event args with custom data
            var eventArgs = new EpochImageEventArgs(image, "Custom Processing");
            eventArgs.EventSource = "BatchProcessor";
            eventArgs.Priority = EventPriority.High;
            
            // Add custom event data
            eventArgs.EventData["ProcessingMode"] = "Batch";
            eventArgs.EventData["BatchId"] = Guid.NewGuid();
            eventArgs.EventData["UserName"] = "Administrator";
            eventArgs.EventData["ProcessingTime"] = DateTime.Now;

            Console.WriteLine($"Event: {eventArgs}");
            Console.WriteLine($"Source: {eventArgs.EventSource}");
            Console.WriteLine($"Priority: {eventArgs.Priority}");
            Console.WriteLine("Custom Data:");
            
            foreach (var kvp in eventArgs.EventData)
            {
                Console.WriteLine($"  {kvp.Key}: {kvp.Value}");
            }

            image.Dispose();
        }

        #region Event Handlers

        private static void OnImageAdded(object sender, EpochImageEventArgs e)
        {
            Console.WriteLine($"Image Added: {e.Image.Id} at {e.Timestamp:HH:mm:ss.fff}");
            Console.WriteLine($"  Event ID: {e.EventId}");
            Console.WriteLine($"  Context: {e.Context ?? "None"}");
        }

        private static void OnImageRemoved(object sender, EpochImageEventArgs e)
        {
            Console.WriteLine($"Image Removed: {e.Image.Id} at {e.Timestamp:HH:mm:ss.fff}");
        }

        private static void OnCurrentImageChanged(object sender, CurrentImageChangedEventArgs e)
        {
            Console.WriteLine($"Current Image Changed: {e}");
            Console.WriteLine($"  Direction: {e.Direction}");
            Console.WriteLine($"  Position Change: {e.PositionChange}");
            Console.WriteLine($"  Is First Selection: {e.IsFirstSelection}");
            Console.WriteLine($"  Navigation Method: {e.NavigationMethod}");
            Console.WriteLine($"  User Initiated: {e.IsUserInitiated}");
        }

        private static void OnCurrentImageChangedCancellable(object sender, CurrentImageChangedEventArgs e)
        {
            Console.WriteLine($"Navigation attempt: {e.CurrentIndex}");
            
            // Cancel navigation to index 1
            if (e.CurrentIndex == 1)
            {
                e.Cancel = true;
                e.ChangeReason = "Navigation to index 1 is not allowed";
                Console.WriteLine($"Navigation cancelled: {e.ChangeReason}");
            }
        }

        private static void OnImageLoading(object sender, ImageLoadingEventArgs e)
        {
            Console.WriteLine($"Loading Progress: {e.Progress}% - {e.LoadingState}");
            Console.WriteLine($"  File: {e.FilePath}");
            Console.WriteLine($"  Bytes: {e.LoadedBytes:N0} / {e.TotalBytes:N0}");
            Console.WriteLine($"  Speed: {e.LoadingSpeed:F0} bytes/sec");
            
            if (e.EstimatedTimeRemaining.HasValue)
            {
                Console.WriteLine($"  ETA: {e.EstimatedTimeRemaining.Value.TotalSeconds:F1} seconds");
            }
        }

        private static void OnImageSaving(object sender, ImageSavingEventArgs e)
        {
            Console.WriteLine($"Saving Progress: {e.Progress}% - {e.SavingState}");
            Console.WriteLine($"  File: {e.FilePath}");
            Console.WriteLine($"  Format: {e.Format}, Quality: {e.Quality}");
            
            if (e.SavingState == ImageSavingState.Completed)
            {
                Console.WriteLine($"  Original Size: {e.OriginalSize:N0} bytes");
                Console.WriteLine($"  Saved Size: {e.SavedSize:N0} bytes");
                Console.WriteLine($"  Compression Ratio: {e.CompressionRatio:F2}");
            }
        }

        private static void OnImageTransformation(object sender, ImageTransformationEventArgs e)
        {
            Console.WriteLine($"Transformation Progress: {e.Progress}% - {e.TransformationState}");
            Console.WriteLine($"  Type: {e.TransformationType}");
            Console.WriteLine($"  Elapsed Time: {e.ElapsedTime.TotalMilliseconds:F0}ms");
            Console.WriteLine($"  Preserves Original: {e.PreservesOriginal}");
            
            if (e.Parameters.Count > 0)
            {
                Console.WriteLine("  Parameters:");
                foreach (var param in e.Parameters)
                {
                    Console.WriteLine($"    {param.Key}: {param.Value}");
                }
            }
            
            if (e.TransformationState == TransformationState.Completed && e.ResultImage != null)
            {
                Console.WriteLine($"  Result Image: {e.ResultImage.Id}");
            }
        }

        #endregion
    }

    /// <summary>
    /// Example event publisher that demonstrates comprehensive event usage
    /// </summary>
    public class EpochImageEventPublisher
    {
        #region Events

        public event EventHandler<EpochImageEventArgs> ImageProcessed;
        public event EventHandler<CurrentImageChangedEventArgs> CurrentImageChanged;
        public event EventHandler<ImageLoadingEventArgs> ImageLoading;
        public event EventHandler<ImageSavingEventArgs> ImageSaving;
        public event EventHandler<ImageTransformationEventArgs> ImageTransformation;

        #endregion

        #region Methods

        public async Task ProcessImageAsync(IEpochImage image)
        {
            // Raise image processing event
            var eventArgs = new EpochImageEventArgs(image, "Processing");
            eventArgs.EventSource = "EpochImageProcessor";
            eventArgs.Priority = EventPriority.Normal;
            
            OnImageProcessed(eventArgs);

            // Simulate processing
            await Task.Delay(100);
        }

        public void ChangeCurrentImage(IEpochImage current, int currentIndex, IEpochImage previous, int previousIndex)
        {
            var eventArgs = new CurrentImageChangedEventArgs(current, currentIndex, previous, previousIndex);
            eventArgs.NavigationMethod = NavigationMethod.Programmatic;
            eventArgs.IsUserInitiated = false;
            eventArgs.ChangeReason = "Programmatic navigation";
            
            OnCurrentImageChanged(eventArgs);
            
            // Check if cancelled
            if (eventArgs.Cancel)
            {
                Console.WriteLine($"Navigation cancelled: {eventArgs.ChangeReason}");
                return;
            }
        }

        protected virtual void OnImageProcessed(EpochImageEventArgs e)
        {
            ImageProcessed?.Invoke(this, e);
        }

        protected virtual void OnCurrentImageChanged(CurrentImageChangedEventArgs e)
        {
            CurrentImageChanged?.Invoke(this, e);
        }

        protected virtual void OnImageLoading(ImageLoadingEventArgs e)
        {
            ImageLoading?.Invoke(this, e);
        }

        protected virtual void OnImageSaving(ImageSavingEventArgs e)
        {
            ImageSaving?.Invoke(this, e);
        }

        protected virtual void OnImageTransformation(ImageTransformationEventArgs e)
        {
            ImageTransformation?.Invoke(this, e);
        }

        #endregion
    }
}
