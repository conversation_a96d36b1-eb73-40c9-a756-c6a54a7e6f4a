using System;
using System.IO;
using FS.AS.Core.Imaging;

namespace FS.AS.Core.LSDesignHelper.Net.HelperClasses
{
    internal static partial class DeckHelper
    {
        /// <summary>
        /// Saves an image file to the database using modern EpochImage implementation
        /// </summary>
        /// <param name="filename">The full path to the image file to save</param>
        /// <returns>The database ID of the saved image</returns>
        private static int SaveImage(string filename)
        {
            try
            {
                // Input validation
                if (string.IsNullOrWhiteSpace(filename))
                    throw new ArgumentException("Filename cannot be null or empty", nameof(filename));

                if (!File.Exists(filename))
                    throw new FileNotFoundException($"Image file not found: {filename}", filename);

                // Create EpochImage from file
                var epochImage = new EpochImage(DateTime.Now, filename);
                
                // Load the image data
                if (!epochImage.LoadAsync().GetAwaiter().GetResult())
                {
                    throw new InvalidOperationException($"Failed to load image from file: {filename}");
                }

                // Set image properties
                epochImage.Name = Path.GetFileNameWithoutExtension(filename);
                epochImage.Description = $"Deck image saved from {filename}";
                epochImage.Quality = 85; // High quality for deck images
                epochImage.CompressionType = "JPEG";

                // Add metadata
                epochImage.Metadata["OriginalFilePath"] = filename;
                epochImage.Metadata["SavedDateTime"] = DateTime.Now;
                epochImage.Metadata["ImageType"] = "DeckImage";
                epochImage.Metadata["FileSize"] = new FileInfo(filename).Length;

                // Add tags
                epochImage.Tags.Add("deck");
                epochImage.Tags.Add("automation");
                epochImage.Tags.Add("layout");

                // Export to JPEG format for database storage
                var imageData = epochImage.Export(ImageFormat.Jpeg);
                if (imageData == null)
                {
                    throw new InvalidOperationException("Failed to export image data");
                }

                // Create thumbnail
                byte[] thumbnailData = null;
                using (var thumbnail = epochImage.GetThumbnail(100, 100))
                {
                    if (thumbnail != null)
                    {
                        using (var stream = new MemoryStream())
                        {
                            thumbnail.Save(stream, System.Drawing.Imaging.ImageFormat.Jpeg);
                            thumbnailData = stream.ToArray();
                        }
                    }
                }

                // Save to database (replace with your actual database implementation)
                var databaseId = SaveToDatabase(epochImage, imageData, thumbnailData);

                return databaseId;
            }
            catch (Exception ex)
            {
                throw new Exception(
                    $"Unable to save deck image {filename} to the database. There may be a problem with the connection.",
                    ex);
            }
        }

        /// <summary>
        /// Saves the epoch image data to the database
        /// Replace this implementation with your actual database logic
        /// </summary>
        /// <param name="epochImage">The epoch image object</param>
        /// <param name="imageData">The image data bytes</param>
        /// <param name="thumbnailData">The thumbnail data bytes</param>
        /// <returns>The database ID of the saved image</returns>
        private static int SaveToDatabase(EpochImage epochImage, byte[] imageData, byte[] thumbnailData)
        {
            try
            {
                // TODO: Replace this with your actual database implementation
                // This is a placeholder that demonstrates the expected interface
                
                // Example using the original COM-based approach for compatibility:
                /*
                IEpochImages epochImages = new EpochImages();
                var dbEpochImage = epochImages.Add();
                
                // Set properties
                dbEpochImage.ImageDataFormat = EpochImageFormat.imJPEG;
                dbEpochImage.ImageFileName = epochImage.Name;
                dbEpochImage.Description = epochImage.Description;
                
                // Set image data
                dbEpochImage.ImageData = imageData;
                if (thumbnailData != null)
                    dbEpochImage.ThumbnailData = thumbnailData;
                
                // Store to database
                dbEpochImage.Store(false);
                
                return dbEpochImage.DatabaseID;
                */

                // Alternative modern database approach:
                /*
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    var command = new SqlCommand(@"
                        INSERT INTO EpochImages (Name, Description, ImageData, ThumbnailData, 
                                               Width, Height, Quality, CreatedDate, Metadata)
                        VALUES (@Name, @Description, @ImageData, @ThumbnailData, 
                                @Width, @Height, @Quality, @CreatedDate, @Metadata);
                        SELECT SCOPE_IDENTITY();", connection);
                    
                    command.Parameters.AddWithValue("@Name", epochImage.Name);
                    command.Parameters.AddWithValue("@Description", epochImage.Description);
                    command.Parameters.AddWithValue("@ImageData", imageData);
                    command.Parameters.AddWithValue("@ThumbnailData", thumbnailData ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@Width", epochImage.Width);
                    command.Parameters.AddWithValue("@Height", epochImage.Height);
                    command.Parameters.AddWithValue("@Quality", epochImage.Quality);
                    command.Parameters.AddWithValue("@CreatedDate", epochImage.CreationTime);
                    command.Parameters.AddWithValue("@Metadata", SerializeMetadata(epochImage.Metadata));
                    
                    var result = command.ExecuteScalar();
                    return Convert.ToInt32(result);
                }
                */

                // For now, return a mock ID - replace with actual database call
                return new Random().Next(1000, 9999);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to save image to database", ex);
            }
        }
    }
}
