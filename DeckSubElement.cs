using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Xml;

namespace FS.AS.Core.RobotDeck
{
    /// <summary>
    /// Complete .NET implementation of DeckSubElement class
    /// Represents a sub-element within a deck element
    /// Uses C# 7.3 features
    /// </summary>
    [Serializable]
    public class DeckSubElement : IDeckSubElement
    {
        #region Private Fields

        private int position_ = 1;
        private string name_ = string.Empty;
        private string positionName_ = string.Empty;
        private string imageFilename_ = string.Empty;
        private List<string> supportedArmTypes_;
        private List<string> supportedPlateTypes_;
        private List<string> substrateNames_;
        private int numberOfRows_ = 1;
        private int numberOfCols_ = 1;
        private int bottomLeftX_ = 0;
        private int bottomLeftY_ = 0;
        private int height_ = 100;
        private int width_ = 100;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the DeckSubElement class
        /// </summary>
        public DeckSubElement()
        {
            supportedArmTypes_ = new List<string>();
            supportedPlateTypes_ = new List<string>();
            substrateNames_ = new List<string>();
        }

        #endregion

        #region IDeckSubElement Implementation

        /// <summary>
        /// The sub-element position starting from 1
        /// Used for identifying and ordering sub-elements within a parent element
        /// </summary>
        [DisplayName("Position")]
        [Description("The sub-element position starting from 1")]
        [Category("Sub-Element Properties")]
        public int Position
        {
            get => position_;
            set
            {
                if (value < 1)
                    position_ = 1;
                else
                    position_ = value;
            }
        }

        /// <summary>
        /// Name of the sub-element
        /// Human-readable identifier for the sub-element
        /// </summary>
        [DisplayName("Name")]
        [Description("Name of the sub-element")]
        [Category("Sub-Element Properties")]
        public string Name
        {
            get => name_;
            set => name_ = value ?? string.Empty;
        }

        /// <summary>
        /// Position name of the sub-element
        /// Used for training and position identification in the format "Deck X-Y ElementName Z"
        /// </summary>
        [DisplayName("Position Name")]
        [Description("Position name used for training and position identification")]
        [Category("Sub-Element Properties")]
        public string PositionName
        {
            get => positionName_;
            set => positionName_ = value ?? string.Empty;
        }

        /// <summary>
        /// Image filename for the sub-element
        /// Relative path to the image file representing this sub-element
        /// </summary>
        [DisplayName("Image Filename")]
        [Description("Image filename for the sub-element")]
        [Category("Sub-Element Properties")]
        public string ImageFilename
        {
            get => imageFilename_;
            set => imageFilename_ = value ?? string.Empty;
        }

        /// <summary>
        /// Supported arm types for the sub-element
        /// Collection of arm type names that can interact with this sub-element
        /// Examples: "VPG", "DispenseHead", "SingleTip", "OSRInjection"
        /// </summary>
        [DisplayName("Supported Arm Types")]
        [Description("Supported arm types for the sub-element")]
        [Category("Capabilities")]
        public ICollection<string> SupportedArmTypes => supportedArmTypes_;

        /// <summary>
        /// Supported plate types for the sub-element
        /// Collection of plate type names that can be placed on this sub-element
        /// Examples: "96-well", "384-well", "DeepWell", "PCR"
        /// </summary>
        [DisplayName("Supported Plate Types")]
        [Description("Supported plate types for the sub-element")]
        [Category("Capabilities")]
        public ICollection<string> SupportedPlateTypes => supportedPlateTypes_;

        /// <summary>
        /// Substrate names for the sub-element
        /// Collection of substrate names that can be placed on this sub-element
        /// Used for substrate tracking and position management
        /// </summary>
        [DisplayName("Substrate Names")]
        [Description("Substrate names that can be placed on this sub-element")]
        [Category("Capabilities")]
        public ICollection<string> SubstrateNames => substrateNames_;

        /// <summary>
        /// Number of rows supported by this sub-element
        /// Intended to be used by passive (tool) track and grid-based positioning
        /// Must be greater than 0
        /// </summary>
        [DisplayName("Number of Rows")]
        [Description("Number of rows supported by this sub-element")]
        [Category("Grid Layout")]
        public int NumberOfRows
        {
            get => numberOfRows_;
            set
            {
                if (value < 1)
                    numberOfRows_ = 1;
                else if (value > 100)
                    numberOfRows_ = 100;
                else
                    numberOfRows_ = value;
            }
        }

        /// <summary>
        /// Number of columns supported by this sub-element
        /// Intended to be used by passive (tool) track and grid-based positioning
        /// Must be greater than 0
        /// </summary>
        [DisplayName("Number of Columns")]
        [Description("Number of columns supported by this sub-element")]
        [Category("Grid Layout")]
        public int NumberOfCols
        {
            get => numberOfCols_;
            set
            {
                if (value < 1)
                    numberOfCols_ = 1;
                else if (value > 100)
                    numberOfCols_ = 100;
                else
                    numberOfCols_ = value;
            }
        }

        /// <summary>
        /// X coordinate of the bottom-left corner of the sub-element relative to the parent element
        /// Measured in millimeters from the parent element's origin
        /// </summary>
        [DisplayName("Bottom Left X")]
        [Description("X coordinate of the bottom-left corner relative to parent element")]
        [Category("Position")]
        public int BottomLeftX
        {
            get => bottomLeftX_;
            set => bottomLeftX_ = value;
        }

        /// <summary>
        /// Y coordinate of the bottom-left corner of the sub-element relative to the parent element
        /// Measured in millimeters from the parent element's origin
        /// </summary>
        [DisplayName("Bottom Left Y")]
        [Description("Y coordinate of the bottom-left corner relative to parent element")]
        [Category("Position")]
        public int BottomLeftY
        {
            get => bottomLeftY_;
            set => bottomLeftY_ = value;
        }

        /// <summary>
        /// Height of the sub-element in millimeters
        /// Physical height dimension of the sub-element
        /// Must be greater than 0
        /// </summary>
        [DisplayName("Height (mm)")]
        [Description("Height of the sub-element in millimeters")]
        [Category("Dimensions")]
        public int Height
        {
            get => height_;
            set
            {
                if (value < 1)
                    height_ = 1;
                else if (value > 10000)
                    height_ = 10000;
                else
                    height_ = value;
            }
        }

        /// <summary>
        /// Width of the sub-element in millimeters
        /// Physical width dimension of the sub-element
        /// Must be greater than 0
        /// </summary>
        [DisplayName("Width (mm)")]
        [Description("Width of the sub-element in millimeters")]
        [Category("Dimensions")]
        public int Width
        {
            get => width_;
            set
            {
                if (value < 1)
                    width_ = 1;
                else if (value > 10000)
                    width_ = 10000;
                else
                    width_ = value;
            }
        }

        #endregion

        #region XML Serialization

        /// <summary>
        /// Gets the XML representation of the sub-element
        /// </summary>
        /// <returns>XML string representing the sub-element</returns>
        public string GetXML()
        {
            using (var sw = new StringWriter())
            {
                using (var xmlWriter = new XmlTextWriter(sw))
                {
                    xmlWriter.Formatting = Formatting.None;
                    xmlWriter.WriteStartDocument();
                    WriteXML(xmlWriter);
                    xmlWriter.WriteEndDocument();
                }
                return sw.ToString();
            }
        }

        /// <summary>
        /// Sets the sub-element configuration from XML
        /// </summary>
        /// <param name="xml">XML string containing sub-element configuration</param>
        public void SetXML(string xml)
        {
            if (string.IsNullOrEmpty(xml))
                throw new ArgumentException("XML cannot be null or empty", nameof(xml));

            try
            {
                using (var sr = new StringReader(xml))
                {
                    using (var xmlReader = new XmlTextReader(sr))
                    {
                        ReadXML(xmlReader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to parse XML: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Writes the sub-element configuration to an XML writer
        /// </summary>
        /// <param name="xmlWriter">XML writer to write to</param>
        public void WriteXML(XmlWriter xmlWriter)
        {
            if (xmlWriter == null)
                throw new ArgumentNullException(nameof(xmlWriter));

            xmlWriter.WriteStartElement("Symyx.AutomationStudio.RobotDeck.DeckSubElement");

            // Write sub-element properties
            xmlWriter.WriteElementString("Position", Position.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("Name", Name);
            xmlWriter.WriteElementString("PositionName", PositionName);
            xmlWriter.WriteElementString("ImageFilename", ImageFilename);
            xmlWriter.WriteElementString("NumberOfRows", NumberOfRows.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("NumberOfCols", NumberOfCols.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("BottomLeftX", BottomLeftX.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("BottomLeftY", BottomLeftY.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("Height", Height.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("Width", Width.ToString(CultureInfo.InvariantCulture));

            // Write supported arm types
            if (SupportedArmTypes.Any())
            {
                xmlWriter.WriteStartElement("SupportedArmTypes");
                foreach (var armType in SupportedArmTypes)
                {
                    xmlWriter.WriteElementString("ArmType", armType);
                }
                xmlWriter.WriteEndElement();
            }

            // Write supported plate types
            if (SupportedPlateTypes.Any())
            {
                xmlWriter.WriteStartElement("SupportedPlateTypes");
                foreach (var plateType in SupportedPlateTypes)
                {
                    xmlWriter.WriteElementString("PlateType", plateType);
                }
                xmlWriter.WriteEndElement();
            }

            // Write substrate names
            if (SubstrateNames.Any())
            {
                xmlWriter.WriteStartElement("SubstrateNames");
                foreach (var substrateName in SubstrateNames)
                {
                    xmlWriter.WriteElementString("SubstrateName", substrateName);
                }
                xmlWriter.WriteEndElement();
            }

            xmlWriter.WriteEndElement();
        }

        /// <summary>
        /// Reads the sub-element configuration from an XML reader
        /// </summary>
        /// <param name="xmlRd">XML reader to read from</param>
        public void ReadXML(XmlReader xmlRd)
        {
            if (xmlRd == null)
                throw new ArgumentNullException(nameof(xmlRd));

            try
            {
                // Clear existing collections
                SupportedArmTypes.Clear();
                SupportedPlateTypes.Clear();
                SubstrateNames.Clear();

                while (xmlRd.Read())
                {
                    if (xmlRd.IsStartElement())
                    {
                        switch (xmlRd.Name)
                        {
                            case "Position":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int position))
                                    Position = position;
                                break;

                            case "Name":
                                Name = xmlRd.ReadElementContentAsString();
                                break;

                            case "PositionName":
                                PositionName = xmlRd.ReadElementContentAsString();
                                break;

                            case "ImageFilename":
                                ImageFilename = xmlRd.ReadElementContentAsString();
                                break;

                            case "NumberOfRows":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int rows))
                                    NumberOfRows = rows;
                                break;

                            case "NumberOfCols":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int cols))
                                    NumberOfCols = cols;
                                break;

                            case "BottomLeftX":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int x))
                                    BottomLeftX = x;
                                break;

                            case "BottomLeftY":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int y))
                                    BottomLeftY = y;
                                break;

                            case "Height":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int height))
                                    Height = height;
                                break;

                            case "Width":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int width))
                                    Width = width;
                                break;

                            case "SupportedArmTypes":
                                ReadSupportedArmTypes(xmlRd);
                                break;

                            case "SupportedPlateTypes":
                                ReadSupportedPlateTypes(xmlRd);
                                break;

                            case "SubstrateNames":
                                ReadSubstrateNames(xmlRd);
                                break;
                        }
                    }
                    else if (xmlRd.NodeType == XmlNodeType.EndElement && xmlRd.Name == "Symyx.AutomationStudio.RobotDeck.DeckSubElement")
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to read XML: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Reads supported arm types from XML
        /// </summary>
        /// <param name="xmlRd">XML reader positioned at SupportedArmTypes</param>
        private void ReadSupportedArmTypes(XmlReader xmlRd)
        {
            while (xmlRd.Read() && xmlRd.Name != "SupportedArmTypes")
            {
                if (xmlRd.IsStartElement() && xmlRd.Name == "ArmType")
                {
                    var armType = xmlRd.ReadElementContentAsString();
                    if (!string.IsNullOrEmpty(armType))
                    {
                        SupportedArmTypes.Add(armType);
                    }
                }
            }
        }

        /// <summary>
        /// Reads supported plate types from XML
        /// </summary>
        /// <param name="xmlRd">XML reader positioned at SupportedPlateTypes</param>
        private void ReadSupportedPlateTypes(XmlReader xmlRd)
        {
            while (xmlRd.Read() && xmlRd.Name != "SupportedPlateTypes")
            {
                if (xmlRd.IsStartElement() && xmlRd.Name == "PlateType")
                {
                    var plateType = xmlRd.ReadElementContentAsString();
                    if (!string.IsNullOrEmpty(plateType))
                    {
                        SupportedPlateTypes.Add(plateType);
                    }
                }
            }
        }

        /// <summary>
        /// Reads substrate names from XML
        /// </summary>
        /// <param name="xmlRd">XML reader positioned at SubstrateNames</param>
        private void ReadSubstrateNames(XmlReader xmlRd)
        {
            while (xmlRd.Read() && xmlRd.Name != "SubstrateNames")
            {
                if (xmlRd.IsStartElement() && xmlRd.Name == "SubstrateName")
                {
                    var substrateName = xmlRd.ReadElementContentAsString();
                    if (!string.IsNullOrEmpty(substrateName))
                    {
                        SubstrateNames.Add(substrateName);
                    }
                }
            }
        }

        #endregion

        #region Validation and Utilities

        /// <summary>
        /// Validates the sub-element configuration
        /// Checks that all required properties are set and within valid ranges
        /// </summary>
        /// <returns>True if the sub-element configuration is valid, false otherwise</returns>
        public bool IsValid()
        {
            try
            {
                // Basic validation
                if (Position <= 0 || NumberOfRows <= 0 || NumberOfCols <= 0)
                    return false;

                if (Height <= 0 || Width <= 0)
                    return false;

                if (string.IsNullOrEmpty(Name))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Checks if the sub-element supports the specified arm type
        /// </summary>
        /// <param name="armType">Arm type to check (case-insensitive)</param>
        /// <returns>True if the arm type is supported, false otherwise</returns>
        public bool SupportsArmType(string armType)
        {
            if (string.IsNullOrEmpty(armType))
                return false;

            return SupportedArmTypes.Contains(armType, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Checks if the sub-element supports the specified plate type
        /// </summary>
        /// <param name="plateType">Plate type to check (case-insensitive)</param>
        /// <returns>True if the plate type is supported, false otherwise</returns>
        public bool SupportsPlateType(string plateType)
        {
            if (string.IsNullOrEmpty(plateType))
                return false;

            return SupportedPlateTypes.Contains(plateType, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets the center point of the sub-element
        /// Calculates the center coordinates based on bottom-left position and dimensions
        /// </summary>
        /// <returns>Tuple containing center X and Y coordinates in millimeters</returns>
        public (int CenterX, int CenterY) GetCenterPoint()
        {
            var centerX = BottomLeftX + Width / 2;
            var centerY = BottomLeftY + Height / 2;
            return (centerX, centerY);
        }

        /// <summary>
        /// Gets the bounding rectangle of the sub-element
        /// </summary>
        /// <returns>Tuple containing X, Y, Width, Height defining the sub-element bounds</returns>
        public (int X, int Y, int Width, int Height) GetBounds()
        {
            return (BottomLeftX, BottomLeftY, Width, Height);
        }

        /// <summary>
        /// Checks if a point is within the sub-element bounds
        /// </summary>
        /// <param name="x">X coordinate to test</param>
        /// <param name="y">Y coordinate to test</param>
        /// <returns>True if the point is within the sub-element bounds, false otherwise</returns>
        public bool ContainsPoint(int x, int y)
        {
            return x >= BottomLeftX && x <= BottomLeftX + Width &&
                   y >= BottomLeftY && y <= BottomLeftY + Height;
        }

        /// <summary>
        /// Adds a supported arm type to the collection
        /// </summary>
        /// <param name="armType">Arm type to add</param>
        /// <remarks>Duplicate arm types are ignored</remarks>
        public void AddSupportedArmType(string armType)
        {
            if (!string.IsNullOrEmpty(armType) && !SupportedArmTypes.Contains(armType))
            {
                SupportedArmTypes.Add(armType);
            }
        }

        /// <summary>
        /// Adds a supported plate type to the collection
        /// </summary>
        /// <param name="plateType">Plate type to add</param>
        /// <remarks>Duplicate plate types are ignored</remarks>
        public void AddSupportedPlateType(string plateType)
        {
            if (!string.IsNullOrEmpty(plateType) && !SupportedPlateTypes.Contains(plateType))
            {
                SupportedPlateTypes.Add(plateType);
            }
        }

        /// <summary>
        /// Removes a supported arm type from the collection
        /// </summary>
        /// <param name="armType">Arm type to remove</param>
        /// <returns>True if the arm type was found and removed, false otherwise</returns>
        public bool RemoveSupportedArmType(string armType)
        {
            return !string.IsNullOrEmpty(armType) && SupportedArmTypes.Remove(armType);
        }

        /// <summary>
        /// Removes a supported plate type from the collection
        /// </summary>
        /// <param name="plateType">Plate type to remove</param>
        /// <returns>True if the plate type was found and removed, false otherwise</returns>
        public bool RemoveSupportedPlateType(string plateType)
        {
            return !string.IsNullOrEmpty(plateType) && SupportedPlateTypes.Remove(plateType);
        }

        /// <summary>
        /// Adds a substrate name to the collection
        /// </summary>
        /// <param name="substrateName">Substrate name to add</param>
        /// <remarks>Duplicate substrate names are ignored</remarks>
        public void AddSubstrateName(string substrateName)
        {
            if (!string.IsNullOrEmpty(substrateName) && !SubstrateNames.Contains(substrateName))
            {
                SubstrateNames.Add(substrateName);
            }
        }

        /// <summary>
        /// Removes a substrate name from the collection
        /// </summary>
        /// <param name="substrateName">Substrate name to remove</param>
        /// <returns>True if the substrate name was found and removed, false otherwise</returns>
        public bool RemoveSubstrateName(string substrateName)
        {
            return !string.IsNullOrEmpty(substrateName) && SubstrateNames.Remove(substrateName);
        }

        /// <summary>
        /// Creates a deep copy of the sub-element
        /// All properties and collections are copied to the new instance
        /// </summary>
        /// <returns>Deep copy of the sub-element</returns>
        public IDeckSubElement Clone()
        {
            var xml = GetXML();
            var clone = new DeckSubElement();
            clone.SetXML(xml);
            return clone;
        }

        /// <summary>
        /// Creates a deep copy of the sub-element as concrete type
        /// All properties and collections are copied to the new instance
        /// </summary>
        /// <returns>Deep copy of the sub-element as DeckSubElement</returns>
        public DeckSubElement CloneAsConcrete()
        {
            var xml = GetXML();
            var clone = new DeckSubElement();
            clone.SetXML(xml);
            return clone;
        }

        #endregion

        #region Object Overrides

        /// <summary>
        /// Returns a string representation of the sub-element
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"DeckSubElement: {Name} (Position {Position}, {Width}x{Height}mm, {SupportedArmTypes.Count} arm types, {SubstrateNames.Count} substrates)";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">Object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object obj)
        {
            if (obj is DeckSubElement other)
            {
                return Position == other.Position &&
                       Name == other.Name &&
                       PositionName == other.PositionName &&
                       ImageFilename == other.ImageFilename &&
                       BottomLeftX == other.BottomLeftX &&
                       BottomLeftY == other.BottomLeftY &&
                       Width == other.Width &&
                       Height == other.Height;
            }
            return false;
        }

        /// <summary>
        /// Serves as the default hash function (.NET Framework compatible)
        /// </summary>
        /// <returns>Hash code for the current object</returns>
        public override int GetHashCode()
        {
            unchecked
            {
                int hash = 17;
                hash = hash * 23 + Position.GetHashCode();
                hash = hash * 23 + (Name?.GetHashCode() ?? 0);
                hash = hash * 23 + (PositionName?.GetHashCode() ?? 0);
                hash = hash * 23 + BottomLeftX.GetHashCode();
                hash = hash * 23 + BottomLeftY.GetHashCode();
                hash = hash * 23 + Width.GetHashCode();
                hash = hash * 23 + Height.GetHashCode();
                return hash;
            }
        }

        #endregion
    }
}
