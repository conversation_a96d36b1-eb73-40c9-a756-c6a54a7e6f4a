﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">Win32</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{5D1F4C60-1D0B-44D7-B679-0DC5A3C09A4B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>FS.AS.HardwareResources</RootNamespace>
    <AssemblyName>FS.AS.HardwareResources.TipRackManager.Net</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|Win32' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\..\..\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <RegisterForComInterop>true</RegisterForComInterop>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|Win32' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\..\..\bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <RegisterForComInterop>true</RegisterForComInterop>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.C1FlexGrid.2, Version=2.6.20092.416, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\libs\C1.Win.C1FlexGrid.2.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="FS.AS.Interfaces.DataResources, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <Private>False</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)FS.AS.Interfaces.DataResources.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="FS.AS.Interfaces.DataResources.Net, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <Private>False</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)FS.AS.Interfaces.DataResources.Net.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="FS.AS.Interfaces.HardwareResources, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <Private>False</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)FS.AS.Interfaces.HardwareResources.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="FS.AS.Interfaces.HardwareResources.Net, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <Private>False</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)FS.AS.Interfaces.HardwareResources.Net.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="FS.AS.Interfaces.Internal, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)FS.AS.Interfaces.Internal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="FS.AS.Interfaces.Public, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <Private>False</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)FS.AS.Interfaces.Public.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Interop.FS.AS.Core.HardwareResources, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>..\..\..\..\bin\Release\Interop.FS.AS.Core.HardwareResources.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Interop.FS.AS.Core.Internal, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)Interop.FS.AS.Core.Internal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Interop.FS.AS.Core.Public, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)Interop.FS.AS.Core.Public.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Interop.FS.AS.Core.Service, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)Interop.FS.AS.Core.Service.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="FS.AS.Core.Internal.Net">
      <Private>False</Private>
      <HintPath>$(OutDir)FS.AS.Core.Internal.Net.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="FS.AS.Windows.Forms.Controls.General.Net">
      <Private>False</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)FS.AS.Windows.Forms.Controls.General.Net.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="FS.AS.Core.UI.ClientControls.Net, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <Private>False</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)FS.AS.Core.UI.ClientControls.Net.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="FS.AS.Core.Public.Net">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(OutDir)FS.AS.Core.Public.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <None Include="CollectionEditorHelper.cs" />
    <Compile Include="TipCollection.cs" />
    <Compile Include="TipItem.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TipItem.designer.cs">
      <DependentUpon>TipItem.cs</DependentUpon>
    </Compile>
    <Compile Include="TipReuseManager.cs" />
    <Compile Include="VialPositionEditor.cs" />
    <Compile Include="PlatePositionEditor.cs" />
    <Compile Include="SubstrateEditor.cs" />
    <Compile Include="CustomEventArgs.cs" />
    <Compile Include="PlateCollection.cs" />
    <Compile Include="PlateCollectionEditor.cs" />
    <Compile Include="PlateItem.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="PlateItem.designer.cs">
      <DependentUpon>PlateItem.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="..\..\..\..\include\SharedVersionInfo.cs">
      <Link>Properties\SharedVersionInfo.cs</Link>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="PlateSubstrateEditor.cs" />
    <Compile Include="PositionEditor.cs" />
    <Compile Include="TipRack.cs" />
    <Compile Include="TipRackConfiguration.cs" />
    <Compile Include="TipRackCollection.cs" />
    <Compile Include="TipRackItem.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TipRackItem.designer.cs">
      <DependentUpon>TipRackItem.cs</DependentUpon>
    </Compile>
    <Compile Include="TipRackManager.cs" />
    <None Include="TipRackManagerUIControl.cs">
      <SubType>UserControl</SubType>
    </None>
    <None Include="TipRackManagerUIControl.Designer.cs">
      <DependentUpon>TipRackManagerUIControl.cs</DependentUpon>
    </None>
    <Compile Include="TipSizeEditor.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <SubType>Designer</SubType>
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="TipRackManagerUIControl.resx">
      <DependentUpon>TipRackManagerUIControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Resources\TipRackManager.ico" />
    <EmbeddedResource Include="Resources\TipReuseManager.ico" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>..\..\..\..\include\fsPublicKey.snk</AssemblyOriginatorKeyFile>
    <DelaySign>true</DelaySign>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
</Project>