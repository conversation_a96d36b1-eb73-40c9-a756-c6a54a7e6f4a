using System;
using System.Linq;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// ImageComparisonResult with core properties and ConfidenceLevel for C# 7.3
    /// </summary>
    public partial class ImageComparisonResult
    {
        #region Core Comparison Properties

        /// <summary>
        /// Gets or sets the similarity percentage between the two images (0-100)
        /// 100% means the images are identical, 0% means completely different
        /// </summary>
        public double SimilarityPercentage { get; set; }

        /// <summary>
        /// Gets or sets the Mean Squared Error (MSE) between the two images
        /// Lower values indicate more similar images
        /// </summary>
        public double MeanSquaredError { get; set; }

        /// <summary>
        /// Gets or sets the Peak Signal-to-Noise Ratio (PSNR) in decibels
        /// Higher values indicate more similar images
        /// </summary>
        public double PeakSignalToNoiseRatio { get; set; }

        /// <summary>
        /// Gets or sets the Structural Similarity Index (SSIM)
        /// Values range from -1 to 1, where 1 indicates perfect similarity
        /// </summary>
        public double StructuralSimilarityIndex { get; set; }

        /// <summary>
        /// Gets or sets whether the two images are identical
        /// </summary>
        public bool AreIdentical { get; set; }

        /// <summary>
        /// Gets or sets the normalized cross-correlation coefficient
        /// Values range from -1 to 1, where 1 indicates perfect positive correlation
        /// </summary>
        public double NormalizedCrossCorrelation { get; set; }

        /// <summary>
        /// Gets or sets the histogram correlation coefficient
        /// Measures similarity based on color distribution
        /// </summary>
        public double HistogramCorrelation { get; set; }

        /// <summary>
        /// Gets or sets the perceptual hash distance
        /// Lower values indicate more similar images
        /// </summary>
        public int PerceptualHashDistance { get; set; }

        /// <summary>
        /// Gets or sets the average color difference (Delta E)
        /// Measures perceptual color difference
        /// </summary>
        public double AverageColorDifference { get; set; }

        /// <summary>
        /// Gets or sets the edge similarity score
        /// Measures similarity of edge structures in the images
        /// </summary>
        public double EdgeSimilarity { get; set; }

        /// <summary>
        /// Gets or sets the texture similarity score
        /// Measures similarity of texture patterns
        /// </summary>
        public double TextureSimilarity { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the ImageComparisonResult class
        /// </summary>
        public ImageComparisonResult()
        {
            // Initialize with default values
            SimilarityPercentage = -1; // -1 indicates not set
            MeanSquaredError = -1;
            PeakSignalToNoiseRatio = double.NaN;
            StructuralSimilarityIndex = double.NaN;
            NormalizedCrossCorrelation = double.NaN;
            HistogramCorrelation = 0;
            PerceptualHashDistance = -1;
            AverageColorDifference = -1;
            EdgeSimilarity = -1;
            TextureSimilarity = -1;
            AreIdentical = false;
        }

        /// <summary>
        /// Initializes a new instance of the ImageComparisonResult class with similarity percentage
        /// </summary>
        /// <param name="similarityPercentage">The similarity percentage (0-100)</param>
        public ImageComparisonResult(double similarityPercentage) : this()
        {
            SimilarityPercentage = Math.Max(0, Math.Min(100, similarityPercentage));
            AreIdentical = Math.Abs(SimilarityPercentage - 100.0) < 0.001;
        }

        #endregion

        #region Computed Properties
        /// <summary>
        /// Gets the comparison confidence level based on available metrics
        /// Uses C# 7.3 compatible syntax without switch expressions
        /// </summary>
        public ComparisonConfidence ConfidenceLevel
        {
            get
            {
                var metricsCount = 0;

                // Count available metrics
                if (SimilarityPercentage >= 0)
                    metricsCount++;

                if (MeanSquaredError >= 0)
                    metricsCount++;

                if (!double.IsNaN(PeakSignalToNoiseRatio) && !double.IsInfinity(PeakSignalToNoiseRatio))
                    metricsCount++;

                if (!double.IsNaN(StructuralSimilarityIndex))
                    metricsCount++;

                if (HistogramCorrelation != 0)
                    metricsCount++;

                if (!double.IsNaN(NormalizedCrossCorrelation) && NormalizedCrossCorrelation != 0)
                    metricsCount++;

                if (PerceptualHashDistance >= 0)
                    metricsCount++;

                if (AverageColorDifference >= 0)
                    metricsCount++;

                if (EdgeSimilarity >= 0)
                    metricsCount++;

                if (TextureSimilarity >= 0)
                    metricsCount++;

                // Determine confidence level based on metrics count
                if (metricsCount >= 7)
                    return ComparisonConfidence.High;
                else if (metricsCount >= 4)
                    return ComparisonConfidence.Medium;
                else if (metricsCount >= 2)
                    return ComparisonConfidence.Low;
                else
                    return ComparisonConfidence.None;
            }
        }

        /// <summary>
        /// Alternative implementation using traditional switch statement (C# 7.3 compatible)
        /// </summary>
        public ComparisonConfidence ConfidenceLevelAlternative
        {
            get
            {
                var metricsCount = GetAvailableMetricsCount();

                switch (metricsCount)
                {
                    case var count when count >= 7:
                        return ComparisonConfidence.High;
                    case var count when count >= 4:
                        return ComparisonConfidence.Medium;
                    case var count when count >= 2:
                        return ComparisonConfidence.Low;
                    default:
                        return ComparisonConfidence.None;
                }
            }
        }

        /// <summary>
        /// Helper method to count available metrics (C# 7.3 compatible)
        /// </summary>
        private int GetAvailableMetricsCount()
        {
            var count = 0;

            // Core metrics
            if (IsValidMetric(SimilarityPercentage)) count++;
            if (IsValidMetric(MeanSquaredError)) count++;
            if (IsValidMetric(PeakSignalToNoiseRatio)) count++;
            if (IsValidMetric(StructuralSimilarityIndex)) count++;

            // Advanced metrics
            if (IsValidMetric(NormalizedCrossCorrelation)) count++;
            if (IsValidMetric(HistogramCorrelation)) count++;
            if (IsValidMetric(AverageColorDifference)) count++;
            if (IsValidMetric(EdgeSimilarity)) count++;
            if (IsValidMetric(TextureSimilarity)) count++;

            // Special case for PerceptualHashDistance (integer)
            if (PerceptualHashDistance >= 0) count++;

            return count;
        }

        /// <summary>
        /// Helper method to validate if a metric value is meaningful (C# 7.3 compatible)
        /// </summary>
        private bool IsValidMetric(double value)
        {
            return !double.IsNaN(value) &&
                   !double.IsInfinity(value) &&
                   value >= 0;
        }

        /// <summary>
        /// Gets detailed confidence information (C# 7.3 compatible)
        /// </summary>
        public ConfidenceDetails GetConfidenceDetails()
        {
            var details = new ConfidenceDetails();
            details.Level = ConfidenceLevel;
            details.AvailableMetricsCount = GetAvailableMetricsCount();
            details.TotalPossibleMetrics = 10; // Total number of metrics we check
            details.ConfidencePercentage = (double)details.AvailableMetricsCount / details.TotalPossibleMetrics * 100;

            // Determine confidence description
            switch (details.Level)
            {
                case ComparisonConfidence.High:
                    details.Description = "High confidence - comprehensive metrics available";
                    break;
                case ComparisonConfidence.Medium:
                    details.Description = "Medium confidence - adequate metrics available";
                    break;
                case ComparisonConfidence.Low:
                    details.Description = "Low confidence - limited metrics available";
                    break;
                case ComparisonConfidence.None:
                default:
                    details.Description = "No confidence - insufficient metrics available";
                    break;
            }

            return details;
        }

        /// <summary>
        /// Gets confidence level with custom thresholds (C# 7.3 compatible)
        /// </summary>
        /// <param name="highThreshold">Minimum metrics for high confidence</param>
        /// <param name="mediumThreshold">Minimum metrics for medium confidence</param>
        /// <param name="lowThreshold">Minimum metrics for low confidence</param>
        /// <returns>Confidence level based on custom thresholds</returns>
        public ComparisonConfidence GetConfidenceLevel(int highThreshold = 7, int mediumThreshold = 4, int lowThreshold = 2)
        {
            var metricsCount = GetAvailableMetricsCount();

            if (metricsCount >= highThreshold)
                return ComparisonConfidence.High;
            else if (metricsCount >= mediumThreshold)
                return ComparisonConfidence.Medium;
            else if (metricsCount >= lowThreshold)
                return ComparisonConfidence.Low;
            else
                return ComparisonConfidence.None;
        }

        /// <summary>
        /// Checks if specific metric categories are available (C# 7.3 compatible)
        /// </summary>
        public MetricAvailability GetMetricAvailability()
        {
            return new MetricAvailability
            {
                HasCoreMetrics = IsValidMetric(SimilarityPercentage) &&
                                IsValidMetric(MeanSquaredError),

                HasAdvancedMetrics = IsValidMetric(StructuralSimilarityIndex) &&
                                    IsValidMetric(NormalizedCrossCorrelation),

                HasQualityMetrics = IsValidMetric(PeakSignalToNoiseRatio) &&
                                   IsValidMetric(EdgeSimilarity),

                HasColorMetrics = IsValidMetric(HistogramCorrelation) &&
                                 IsValidMetric(AverageColorDifference),

                HasPerceptualMetrics = PerceptualHashDistance >= 0 &&
                                      IsValidMetric(TextureSimilarity)
            };
        }
    }

    /// <summary>
    /// Detailed confidence information (C# 7.3 compatible)
    /// </summary>
    public class ConfidenceDetails
    {
        /// <summary>
        /// Gets or sets the confidence level
        /// </summary>
        public ComparisonConfidence Level { get; set; }

        /// <summary>
        /// Gets or sets the number of available metrics
        /// </summary>
        public int AvailableMetricsCount { get; set; }

        /// <summary>
        /// Gets or sets the total possible metrics
        /// </summary>
        public int TotalPossibleMetrics { get; set; }

        /// <summary>
        /// Gets or sets the confidence percentage
        /// </summary>
        public double ConfidencePercentage { get; set; }

        /// <summary>
        /// Gets or sets the confidence description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Returns a string representation of the confidence details
        /// </summary>
        /// <returns>Formatted confidence information</returns>
        public override string ToString()
        {
            return $"{Level} ({AvailableMetricsCount}/{TotalPossibleMetrics} metrics, {ConfidencePercentage:F1}%)";
        }
    }

    /// <summary>
    /// Metric availability information (C# 7.3 compatible)
    /// </summary>
    public class MetricAvailability
    {
        /// <summary>
        /// Gets or sets whether core metrics are available
        /// </summary>
        public bool HasCoreMetrics { get; set; }

        /// <summary>
        /// Gets or sets whether advanced metrics are available
        /// </summary>
        public bool HasAdvancedMetrics { get; set; }

        /// <summary>
        /// Gets or sets whether quality metrics are available
        /// </summary>
        public bool HasQualityMetrics { get; set; }

        /// <summary>
        /// Gets or sets whether color metrics are available
        /// </summary>
        public bool HasColorMetrics { get; set; }

        /// <summary>
        /// Gets or sets whether perceptual metrics are available
        /// </summary>
        public bool HasPerceptualMetrics { get; set; }

        /// <summary>
        /// Gets the overall metric coverage score
        /// </summary>
        public double CoverageScore
        {
            get
            {
                var availableCategories = 0;
                if (HasCoreMetrics) availableCategories++;
                if (HasAdvancedMetrics) availableCategories++;
                if (HasQualityMetrics) availableCategories++;
                if (HasColorMetrics) availableCategories++;
                if (HasPerceptualMetrics) availableCategories++;

                return (double)availableCategories / 5 * 100;
            }
        }

        /// <summary>
        /// Returns a string representation of metric availability
        /// </summary>
        /// <returns>Formatted availability information</returns>
        public override string ToString()
        {
            var categories = new System.Collections.Generic.List<string>();
            if (HasCoreMetrics) categories.Add("Core");
            if (HasAdvancedMetrics) categories.Add("Advanced");
            if (HasQualityMetrics) categories.Add("Quality");
            if (HasColorMetrics) categories.Add("Color");
            if (HasPerceptualMetrics) categories.Add("Perceptual");

            return $"Available: {string.Join(", ", categories)} ({CoverageScore:F1}% coverage)";
        }
    }

    /// <summary>
    /// Example usage class demonstrating C# 7.3 compatible ConfidenceLevel property
    /// </summary>
    public static class ConfidenceLevelExample
    {
        /// <summary>
        /// Demonstrates usage of ConfidenceLevel property in C# 7.3
        /// </summary>
        public static void DemonstrateUsage()
        {
            Console.WriteLine("=== ImageComparisonResult C# 7.3 Demo ===");
            Console.WriteLine();

            // Example 1: Basic comparison result
            Console.WriteLine("1. Basic Comparison Result:");
            var basicResult = new ImageComparisonResult(85.5);
            Console.WriteLine($"   Similarity: {basicResult.SimilarityPercentage:F1}%");
            Console.WriteLine($"   Are Identical: {basicResult.AreIdentical}");
            Console.WriteLine($"   Confidence: {basicResult.ConfidenceLevel}");
            Console.WriteLine();

            // Example 2: Detailed comparison with multiple metrics
            Console.WriteLine("2. Detailed Comparison with Multiple Metrics:");
            var detailedResult = new ImageComparisonResult();
            detailedResult.SimilarityPercentage = 92.3;
            detailedResult.MeanSquaredError = 8.7;
            detailedResult.PeakSignalToNoiseRatio = 31.2;
            detailedResult.StructuralSimilarityIndex = 0.89;
            detailedResult.NormalizedCrossCorrelation = 0.91;
            detailedResult.HistogramCorrelation = 0.88;
            detailedResult.PerceptualHashDistance = 3;
            detailedResult.AverageColorDifference = 2.1;
            detailedResult.EdgeSimilarity = 87.5;
            detailedResult.TextureSimilarity = 89.2;

            Console.WriteLine($"   Similarity: {detailedResult.SimilarityPercentage:F1}%");
            Console.WriteLine($"   MSE: {detailedResult.MeanSquaredError:F1}");
            Console.WriteLine($"   PSNR: {detailedResult.PeakSignalToNoiseRatio:F1} dB");
            Console.WriteLine($"   SSIM: {detailedResult.StructuralSimilarityIndex:F3}");
            Console.WriteLine($"   Confidence: {detailedResult.ConfidenceLevel}");
            Console.WriteLine();

            // Example 3: Confidence analysis
            Console.WriteLine("3. Confidence Analysis:");
            var details = detailedResult.GetConfidenceDetails();
            Console.WriteLine($"   Level: {details.Level}");
            Console.WriteLine($"   Available Metrics: {details.AvailableMetricsCount}/{details.TotalPossibleMetrics}");
            Console.WriteLine($"   Confidence Percentage: {details.ConfidencePercentage:F1}%");
            Console.WriteLine($"   Description: {details.Description}");
            Console.WriteLine();

            // Example 4: Metric availability
            Console.WriteLine("4. Metric Availability:");
            var availability = detailedResult.GetMetricAvailability();
            Console.WriteLine($"   Core Metrics: {availability.HasCoreMetrics}");
            Console.WriteLine($"   Advanced Metrics: {availability.HasAdvancedMetrics}");
            Console.WriteLine($"   Quality Metrics: {availability.HasQualityMetrics}");
            Console.WriteLine($"   Color Metrics: {availability.HasColorMetrics}");
            Console.WriteLine($"   Perceptual Metrics: {availability.HasPerceptualMetrics}");
            Console.WriteLine($"   Coverage Score: {availability.CoverageScore:F1}%");
            Console.WriteLine();

            // Example 5: Custom confidence thresholds
            Console.WriteLine("5. Custom Confidence Thresholds:");
            var customHigh = detailedResult.GetConfidenceLevel(8, 5, 2);
            var customMedium = detailedResult.GetConfidenceLevel(6, 3, 1);
            var customLow = detailedResult.GetConfidenceLevel(4, 2, 1);

            Console.WriteLine($"   Strict Thresholds (8,5,2): {customHigh}");
            Console.WriteLine($"   Medium Thresholds (6,3,1): {customMedium}");
            Console.WriteLine($"   Lenient Thresholds (4,2,1): {customLow}");
            Console.WriteLine();

            // Example 6: Different scenarios
            Console.WriteLine("6. Different Comparison Scenarios:");

            var scenarios = new[]
            {
                ("Identical Images", CreateIdenticalResult()),
                ("Very Similar", CreateVerySimilarResult()),
                ("Moderately Similar", CreateModerateSimilarResult()),
                ("Different Images", CreateDifferentResult()),
                ("Minimal Data", CreateMinimalDataResult())
            };

            Console.WriteLine("   Scenario           | Similarity | Confidence | Metrics");
            Console.WriteLine("   -------------------|------------|------------|--------");

            foreach (var scenario in scenarios)
            {
                var name = scenario.Item1;
                var result = scenario.Item2;
                var conf = result.ConfidenceLevel;
                var metricCount = result.GetAvailableMetricsCount();

                Console.WriteLine($"   {name,-18} | {result.SimilarityPercentage,8:F1}% | {conf,-10} | {metricCount,7}");
            }
            Console.WriteLine();

            // Example 7: Traditional C# 7.3 conditional logic
            Console.WriteLine("7. Traditional C# 7.3 Conditional Logic:");
            var testResult = detailedResult;

            // Traditional if-else approach (C# 7.3 compatible)
            string confidenceDescription;
            if (testResult.ConfidenceLevel == ComparisonConfidence.High)
                confidenceDescription = "Highly reliable comparison with comprehensive metrics";
            else if (testResult.ConfidenceLevel == ComparisonConfidence.Medium)
                confidenceDescription = "Moderately reliable comparison with adequate metrics";
            else if (testResult.ConfidenceLevel == ComparisonConfidence.Low)
                confidenceDescription = "Limited reliability due to insufficient metrics";
            else
                confidenceDescription = "Unreliable comparison - more metrics needed";

            Console.WriteLine($"   Assessment: {confidenceDescription}");

            // Traditional switch statement (C# 7.3 compatible)
            string recommendation;
            switch (testResult.ConfidenceLevel)
            {
                case ComparisonConfidence.High:
                    recommendation = "Results can be trusted for decision making";
                    break;
                case ComparisonConfidence.Medium:
                    recommendation = "Results are generally reliable but consider additional validation";
                    break;
                case ComparisonConfidence.Low:
                    recommendation = "Use results with caution and seek additional metrics";
                    break;
                case ComparisonConfidence.None:
                default:
                    recommendation = "Results should not be used for decision making";
                    break;
            }

            Console.WriteLine($"   Recommendation: {recommendation}");
        }

        // Helper methods for creating test scenarios
        private static ImageComparisonResult CreateIdenticalResult()
        {
            var result = new ImageComparisonResult(100.0);
            result.AreIdentical = true;
            result.MeanSquaredError = 0;
            result.StructuralSimilarityIndex = 1.0;
            result.NormalizedCrossCorrelation = 1.0;
            result.HistogramCorrelation = 1.0;
            result.PerceptualHashDistance = 0;
            result.AverageColorDifference = 0;
            result.EdgeSimilarity = 100;
            result.TextureSimilarity = 100;
            return result;
        }

        private static ImageComparisonResult CreateVerySimilarResult()
        {
            var result = new ImageComparisonResult(95.2);
            result.MeanSquaredError = 2.1;
            result.PeakSignalToNoiseRatio = 42.5;
            result.StructuralSimilarityIndex = 0.96;
            result.HistogramCorrelation = 0.94;
            result.EdgeSimilarity = 93.8;
            return result;
        }

        private static ImageComparisonResult CreateModerateSimilarResult()
        {
            var result = new ImageComparisonResult(72.8);
            result.MeanSquaredError = 25.4;
            result.PeakSignalToNoiseRatio = 18.2;
            result.StructuralSimilarityIndex = 0.71;
            return result;
        }

        private static ImageComparisonResult CreateDifferentResult()
        {
            var result = new ImageComparisonResult(15.3);
            result.MeanSquaredError = 180.5;
            result.StructuralSimilarityIndex = 0.12;
            return result;
        }

        private static ImageComparisonResult CreateMinimalDataResult()
        {
            var result = new ImageComparisonResult(68.5);
            // Only basic similarity percentage set
            return result;
        }
    }
}
