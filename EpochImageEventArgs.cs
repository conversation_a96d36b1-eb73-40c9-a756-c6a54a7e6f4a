using System;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// Event arguments for epoch image events
    /// </summary>
    public class EpochImageEventArgs : EventArgs
    {
        /// <summary>
        /// Initializes a new instance of the EpochImageEventArgs class
        /// </summary>
        /// <param name="image">The epoch image associated with the event</param>
        public EpochImageEventArgs(IEpochImage image)
        {
            Image = image;
        }

        /// <summary>
        /// Gets the epoch image associated with the event
        /// </summary>
        public IEpochImage Image { get; }
    }

    /// <summary>
    /// Event arguments for current image changed events
    /// </summary>
    public class CurrentImageChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Initializes a new instance of the CurrentImageChangedEventArgs class
        /// </summary>
        /// <param name="currentImage">The current epoch image</param>
        /// <param name="currentIndex">The current index</param>
        public CurrentImageChangedEventArgs(IEpochImage currentImage, int currentIndex)
        {
            CurrentImage = currentImage;
            CurrentIndex = currentIndex;
        }

        /// <summary>
        /// Gets the current epoch image
        /// </summary>
        public IEpochImage CurrentImage { get; }

        /// <summary>
        /// Gets the current index
        /// </summary>
        public int CurrentIndex { get; }
    }
}
