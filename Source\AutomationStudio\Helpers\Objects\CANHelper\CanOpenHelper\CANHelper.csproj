﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">Win32</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{5C043A9E-0111-4B37-B3FF-BA3D705B7B90}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>FS.AS.HardwareResources</RootNamespace>
    <AssemblyName>FS.AS.Core.CANHelper.Net</AssemblyName>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|Win32' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\..\..\..\..\bin\$(Configuration)\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RunCodeAnalysis>false</RunCodeAnalysis>
    <CodeAnalysisRules>
    </CodeAnalysisRules>
    <DocumentationFile>
    </DocumentationFile>
    <CodeAnalysisRuleSet>Migrated rules for CANHelper.ruleset</CodeAnalysisRuleSet>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|Win32' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\..\..\..\..\bin\$(Configuration)\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <RegisterForComInterop>false</RegisterForComInterop>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="FS.AS.Core.Public.Net, Version=8.4.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(OutDir)FS.AS.Core.Public.Net.dll</HintPath>
    </Reference>
    <Reference Include="FS.AS.Interfaces.HardwareResources, Version=1.0.0.0, Culture=neutral, PublicKeyToken=904057908d0fa0b1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>False</EmbedInteropTypes>
      <HintPath>$(OutDir)FS.AS.Interfaces.HardwareResources.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CANDeviceInfo.cs" />
    <Compile Include="CANFullMessage.cs" />
    <Compile Include="CANMessage.cs" />
    <Compile Include="CanOpenHelper.cs" />
    <Compile Include="COMCanEnum.cs" />
    <Compile Include="COMCanStruct.cs" />
    <Compile Include="CANExceptions.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="..\..\..\..\..\..\include\SharedVersionInfo.cs">
      <Link>Properties\SharedVersionInfo.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="CanOpenHelper.txt" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="Migrated rules for CANHelper.ruleset" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>..\..\..\..\..\..\include\fsPublicKey.snk</AssemblyOriginatorKeyFile>
    <DelaySign>false</DelaySign>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
</Project>