using System.Collections.ObjectModel;
using System.Xml;

namespace FS.AS.Core.RobotDeck
{
    /// <summary>
    /// Interface for object which represent a deck work surface within Symyx Automation Studio.
    /// Defines the contract for deck configuration and management.
    /// </summary>
    public interface IDeck
    {
        /// <summary>
        /// Physically accessible (by arms) width of the overall deck, in millimeters
        /// </summary>
        double Width { get; set; }

        /// <summary>
        /// Physically accessible (by arms) depth of the overall deck, in millimeters
        /// </summary>
        double Depth { get; set; }

        /// <summary>
        /// Physical size (in mm) of the left border of the robot deck
        /// </summary>
        double LeftBorder { get; set; }

        /// <summary>
        /// Physical size (in mm) of the right border of the robot deck
        /// </summary>
        double RightBorder { get; set; }

        /// <summary>
        /// Physical size (in mm) of the front border of the robot deck
        /// </summary>
        double FrontBorder { get; set; }

        /// <summary>
        /// Physical size (in mm) of the back border of the robot deck
        /// </summary>
        double BackBorder { get; set; }

        /// <summary>
        /// Number of evenly spaced deck positions on the deck
        /// </summary>
        int UnitsWide { get; set; }

        /// <summary>
        /// Graphical render size of the top unit shelf as a percent of the deck depth (0 - 100)
        /// </summary>
        double TopShelfWidth { get; set; }

        /// <summary>
        /// Graphical render size of the bottom unit shelf as a percent of the deck depth (0 - 100)
        /// </summary>
        double BottomShelfWidth { get; set; }

        /// <summary>
        /// X coordinate of the deck reference point in millimeters
        /// </summary>
        double ReferenceX { get; set; }

        /// <summary>
        /// Y coordinate of the deck reference point in millimeters
        /// </summary>
        double ReferenceY { get; set; }

        /// <summary>
        /// Starting deck element position offset for positioning calculations
        /// </summary>
        int StartingDeckElementPositionOffset { get; set; }

        /// <summary>
        /// Extended deck element position offset for extended deck configurations
        /// </summary>
        int ExtendedDeckElementPositionOffset { get; set; }

        /// <summary>
        /// The current deck elements associated with the deck
        /// </summary>
        Collection<IDeckElement> DeckElements { get; }

        /// <summary>
        /// Gets the XML representation of the deck configuration
        /// </summary>
        /// <returns>XML string representing the deck</returns>
        string GetXML();

        /// <summary>
        /// Sets the state of the deck from a string containing XML
        /// </summary>
        /// <param name="xml">XML string containing deck configuration</param>
        void SetXML(string xml);

        /// <summary>
        /// Writes the XML of the robot deck and its subobjects to an XML writer
        /// </summary>
        /// <param name="xmlWriter">XML writer to write the deck configuration to</param>
        void WriteXML(XmlWriter xmlWriter);

        /// <summary>
        /// Reads the state of the deck from an XML Reader
        /// </summary>
        /// <param name="xmlRd">XML reader to read the deck configuration from</param>
        void ReadXML(XmlReader xmlRd);
    }
}
