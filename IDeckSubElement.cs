using System.Collections.Generic;

namespace FS.AS.Core.RobotDeck
{
    /// <summary>
    /// Interface for deck sub-elements that are contained within deck elements.
    /// Defines the contract for sub-element configuration and positioning.
    /// </summary>
    public interface IDeckSubElement
    {
        /// <summary>
        /// The sub-element position starting from 1
        /// </summary>
        int Position { get; }

        /// <summary>
        /// Name of the sub-element
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Image filename for the sub-element
        /// </summary>
        string ImageFilename { get; }

        /// <summary>
        /// Supported arm types for the sub-element
        /// </summary>
        ICollection<string> SupportedArmTypes { get; }

        /// <summary>
        /// Supported plate types for the sub-element
        /// </summary>
        ICollection<string> SupportedPlateTypes { get; }

        /// <summary>
        /// Number of rows supported by this sub-element - intended to be used by passive (tool) track
        /// </summary>
        int NumberOfRows { get; }

        /// <summary>
        /// Number of columns supported by this sub-element - intended to be used by passive (tool) track
        /// </summary>
        int NumberOfCols { get; }

        /// <summary>
        /// X coordinate of the bottom-left corner of the sub-element relative to the parent element
        /// </summary>
        int BottomLeftX { get; set; }

        /// <summary>
        /// Y coordinate of the bottom-left corner of the sub-element relative to the parent element
        /// </summary>
        int BottomLeftY { get; set; }

        /// <summary>
        /// Height of the sub-element in millimeters
        /// </summary>
        int Height { get; set; }

        /// <summary>
        /// Width of the sub-element in millimeters
        /// </summary>
        int Width { get; set; }
    }
}
