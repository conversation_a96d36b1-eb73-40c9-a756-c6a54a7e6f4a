using System.Collections.Generic;
using System.Xml;

namespace FS.AS.Core.RobotDeck
{
    /// <summary>
    /// Complete interface for deck sub-elements that are contained within deck elements.
    /// Defines the contract for sub-element configuration, positioning, capabilities, and serialization.
    /// Supports both database-enabled and BenchTop (file system only) environments.
    /// </summary>
    public interface IDeckSubElement
    {
        #region Core Properties

        /// <summary>
        /// The sub-element position starting from 1
        /// Used for identifying and ordering sub-elements within a parent element
        /// </summary>
        int Position { get; set; }

        /// <summary>
        /// Name of the sub-element
        /// Human-readable identifier for the sub-element
        /// </summary>
        string Name { get; set; }

        /// <summary>
        /// Position name of the sub-element
        /// Used for training and position identification in the format "Deck X-Y ElementName Z"
        /// </summary>
        string PositionName { get; set; }

        /// <summary>
        /// Image filename for the sub-element
        /// Relative path to the image file representing this sub-element
        /// </summary>
        string ImageFilename { get; set; }

        #endregion

        #region Capabilities

        /// <summary>
        /// Supported arm types for the sub-element
        /// Collection of arm type names that can interact with this sub-element
        /// Examples: "VPG", "DispenseHead", "SingleTip", "OSRInjection"
        /// </summary>
        ICollection<string> SupportedArmTypes { get; }

        /// <summary>
        /// Supported plate types for the sub-element
        /// Collection of plate type names that can be placed on this sub-element
        /// Examples: "96-well", "384-well", "DeepWell", "PCR"
        /// </summary>
        ICollection<string> SupportedPlateTypes { get; }

        /// <summary>
        /// Substrate names for the sub-element
        /// Collection of substrate names that can be placed on this sub-element
        /// Used for substrate tracking and position management
        /// </summary>
        ICollection<string> SubstrateNames { get; }

        #endregion

        #region Grid Layout Properties

        /// <summary>
        /// Number of rows supported by this sub-element
        /// Intended to be used by passive (tool) track and grid-based positioning
        /// Must be greater than 0
        /// </summary>
        int NumberOfRows { get; set; }

        /// <summary>
        /// Number of columns supported by this sub-element
        /// Intended to be used by passive (tool) track and grid-based positioning
        /// Must be greater than 0
        /// </summary>
        int NumberOfCols { get; set; }

        #endregion

        #region Positioning and Dimensions

        /// <summary>
        /// X coordinate of the bottom-left corner of the sub-element relative to the parent element
        /// Measured in millimeters from the parent element's origin
        /// </summary>
        int BottomLeftX { get; set; }

        /// <summary>
        /// Y coordinate of the bottom-left corner of the sub-element relative to the parent element
        /// Measured in millimeters from the parent element's origin
        /// </summary>
        int BottomLeftY { get; set; }

        /// <summary>
        /// Height of the sub-element in millimeters
        /// Physical height dimension of the sub-element
        /// Must be greater than 0
        /// </summary>
        int Height { get; set; }

        /// <summary>
        /// Width of the sub-element in millimeters
        /// Physical width dimension of the sub-element
        /// Must be greater than 0
        /// </summary>
        int Width { get; set; }

        #endregion

        #region XML Serialization

        /// <summary>
        /// Gets the XML representation of the sub-element configuration
        /// </summary>
        /// <returns>XML string representing the sub-element</returns>
        string GetXML();

        /// <summary>
        /// Sets the state of the sub-element from a string containing XML
        /// </summary>
        /// <param name="xml">XML string containing sub-element configuration</param>
        /// <exception cref="System.ArgumentException">Thrown when XML is null or empty</exception>
        /// <exception cref="System.InvalidOperationException">Thrown when XML parsing fails</exception>
        void SetXML(string xml);

        /// <summary>
        /// Writes the XML of the sub-element and its properties to an XML writer
        /// </summary>
        /// <param name="xmlWriter">XML writer to write the sub-element configuration to</param>
        /// <exception cref="System.ArgumentNullException">Thrown when xmlWriter is null</exception>
        void WriteXML(XmlWriter xmlWriter);

        /// <summary>
        /// Reads the state of the sub-element from an XML Reader
        /// </summary>
        /// <param name="xmlRd">XML reader to read the sub-element configuration from</param>
        /// <exception cref="System.ArgumentNullException">Thrown when xmlRd is null</exception>
        /// <exception cref="System.InvalidOperationException">Thrown when XML reading fails</exception>
        void ReadXML(XmlReader xmlRd);

        #endregion

        #region Validation and Utilities

        /// <summary>
        /// Validates the sub-element configuration
        /// Checks that all required properties are set and within valid ranges
        /// </summary>
        /// <returns>True if the sub-element configuration is valid, false otherwise</returns>
        bool IsValid();

        /// <summary>
        /// Checks if the sub-element supports the specified arm type
        /// </summary>
        /// <param name="armType">Arm type to check (case-insensitive)</param>
        /// <returns>True if the arm type is supported, false otherwise</returns>
        bool SupportsArmType(string armType);

        /// <summary>
        /// Checks if the sub-element supports the specified plate type
        /// </summary>
        /// <param name="plateType">Plate type to check (case-insensitive)</param>
        /// <returns>True if the plate type is supported, false otherwise</returns>
        bool SupportsPlateType(string plateType);

        /// <summary>
        /// Gets the center point of the sub-element
        /// Calculates the center coordinates based on bottom-left position and dimensions
        /// </summary>
        /// <returns>Tuple containing center X and Y coordinates in millimeters</returns>
        (int CenterX, int CenterY) GetCenterPoint();

        /// <summary>
        /// Gets the bounding rectangle of the sub-element
        /// </summary>
        /// <returns>Tuple containing X, Y, Width, Height defining the sub-element bounds</returns>
        (int X, int Y, int Width, int Height) GetBounds();

        /// <summary>
        /// Checks if a point is within the sub-element bounds
        /// </summary>
        /// <param name="x">X coordinate to test</param>
        /// <param name="y">Y coordinate to test</param>
        /// <returns>True if the point is within the sub-element bounds, false otherwise</returns>
        bool ContainsPoint(int x, int y);

        #endregion

        #region Collection Management

        /// <summary>
        /// Adds a supported arm type to the collection
        /// </summary>
        /// <param name="armType">Arm type to add</param>
        /// <remarks>Duplicate arm types are ignored</remarks>
        void AddSupportedArmType(string armType);

        /// <summary>
        /// Adds a supported plate type to the collection
        /// </summary>
        /// <param name="plateType">Plate type to add</param>
        /// <remarks>Duplicate plate types are ignored</remarks>
        void AddSupportedPlateType(string plateType);

        /// <summary>
        /// Removes a supported arm type from the collection
        /// </summary>
        /// <param name="armType">Arm type to remove</param>
        /// <returns>True if the arm type was found and removed, false otherwise</returns>
        bool RemoveSupportedArmType(string armType);

        /// <summary>
        /// Removes a supported plate type from the collection
        /// </summary>
        /// <param name="plateType">Plate type to remove</param>
        /// <returns>True if the plate type was found and removed, false otherwise</returns>
        bool RemoveSupportedPlateType(string plateType);

        /// <summary>
        /// Adds a substrate name to the collection
        /// </summary>
        /// <param name="substrateName">Substrate name to add</param>
        /// <remarks>Duplicate substrate names are ignored</remarks>
        void AddSubstrateName(string substrateName);

        /// <summary>
        /// Removes a substrate name from the collection
        /// </summary>
        /// <param name="substrateName">Substrate name to remove</param>
        /// <returns>True if the substrate name was found and removed, false otherwise</returns>
        bool RemoveSubstrateName(string substrateName);

        #endregion

        #region Cloning

        /// <summary>
        /// Creates a deep copy of the sub-element
        /// All properties and collections are copied to the new instance
        /// </summary>
        /// <returns>Deep copy of the sub-element</returns>
        IDeckSubElement Clone();

        #endregion
    }
}
