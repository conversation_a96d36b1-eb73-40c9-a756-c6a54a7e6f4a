using System;
using System.Linq;
using FS.AS.Core.Imaging;

namespace FS.AS.Core.Imaging.Examples
{
    /// <summary>
    /// Example usage of ImageFormat enum and its extension methods
    /// </summary>
    public static class ImageFormatExample
    {
        /// <summary>
        /// Demonstrates various ImageFormat enum usage scenarios
        /// </summary>
        public static void RunImageFormatExamples()
        {
            Console.WriteLine("=== ImageFormat Enum Examples ===");
            Console.WriteLine();

            // Example 1: Basic enum usage
            Example1_BasicEnumUsage();

            // Example 2: Extension methods
            Example2_ExtensionMethods();

            // Example 3: Format capabilities
            Example3_FormatCapabilities();

            // Example 4: File extension parsing
            Example4_FileExtensionParsing();

            // Example 5: Format recommendations
            Example5_FormatRecommendations();

            // Example 6: Web compatibility
            Example6_WebCompatibility();
        }

        private static void Example1_BasicEnumUsage()
        {
            Console.WriteLine("1. Basic ImageFormat Enum Usage");
            Console.WriteLine("-------------------------------");

            // Direct enum usage
            var jpegFormat = ImageFormat.Jpeg;
            var pngFormat = ImageFormat.Png;
            var gifFormat = ImageFormat.Gif;

            Console.WriteLine($"JPEG Format: {jpegFormat}");
            Console.WriteLine($"PNG Format: {pngFormat}");
            Console.WriteLine($"GIF Format: {gifFormat}");

            // Get description
            Console.WriteLine($"JPEG Description: {jpegFormat.GetDescription()}");
            Console.WriteLine($"PNG Description: {pngFormat.GetDescription()}");

            // Enum comparison
            if (jpegFormat == ImageFormat.Jpeg)
            {
                Console.WriteLine("✓ JPEG format correctly identified");
            }

            Console.WriteLine();
        }

        private static void Example2_ExtensionMethods()
        {
            Console.WriteLine("2. Extension Methods");
            Console.WriteLine("-------------------");

            var formats = new ImageFormat[]
            {
                ImageFormat.Jpeg,
                ImageFormat.Png,
                ImageFormat.Bmp,
                ImageFormat.Gif,
                ImageFormat.WebP
            };

            Console.WriteLine("Format | Extension | MIME Type           | Description");
            Console.WriteLine("-------|-----------|---------------------|------------");

            foreach (var format in formats)
            {
                var extension = format.GetFileExtension();
                var mimeType = format.GetMimeType();
                var description = format.GetDescription();

                Console.WriteLine($"{format,-6} | {extension,-9} | {mimeType,-19} | {description}");
            }

            Console.WriteLine();
        }

        private static void Example3_FormatCapabilities()
        {
            Console.WriteLine("3. Format Capabilities");
            Console.WriteLine("---------------------");

            var formats = new ImageFormat[]
            {
                ImageFormat.Jpeg,
                ImageFormat.Png,
                ImageFormat.Bmp,
                ImageFormat.Gif,
                ImageFormat.Tiff,
                ImageFormat.WebP,
                ImageFormat.Heif,
                ImageFormat.Avif
            };

            Console.WriteLine("Format | Quality | Transparency | Lossy | Web Compatible");
            Console.WriteLine("-------|---------|--------------|-------|---------------");

            foreach (var format in formats)
            {
                var supportsQuality = format.SupportsQuality();
                var supportsTransparency = format.SupportsTransparency();
                var isLossy = format.IsLossy();
                var isWebCompatible = format.IsWebCompatible();

                Console.WriteLine($"{format,-6} | {(supportsQuality ? "Yes" : "No"),7} | " +
                                $"{(supportsTransparency ? "Yes" : "No"),12} | " +
                                $"{(isLossy ? "Yes" : "No"),5} | " +
                                $"{(isWebCompatible ? "Yes" : "No"),14}");
            }

            Console.WriteLine();
        }

        private static void Example4_FileExtensionParsing()
        {
            Console.WriteLine("4. File Extension Parsing");
            Console.WriteLine("-------------------------");

            var testExtensions = new string[]
            {
                ".jpg",
                ".jpeg",
                ".png",
                ".gif",
                ".bmp",
                ".tiff",
                ".webp",
                ".heif",
                ".avif",
                ".svg",
                ".unknown"
            };

            Console.WriteLine("Extension | Detected Format | Multiple Extensions");
            Console.WriteLine("----------|-----------------|-------------------");

            foreach (var extension in testExtensions)
            {
                var detectedFormat = ImageFormatExtensions.FromFileExtension(extension);
                var allExtensions = detectedFormat.GetAllFileExtensions();
                var extensionList = string.Join(", ", allExtensions);

                Console.WriteLine($"{extension,-9} | {detectedFormat,-15} | {extensionList}");
            }

            // Test without dot
            var formatFromExtension = ImageFormatExtensions.FromFileExtension("jpg");
            Console.WriteLine($"\nParsing 'jpg' (no dot): {formatFromExtension}");

            Console.WriteLine();
        }

        private static void Example5_FormatRecommendations()
        {
            Console.WriteLine("5. Format Recommendations");
            Console.WriteLine("-------------------------");

            // Simulate different use cases
            var useCases = new[]
            {
                ("Photograph", true, false, false),      // photographic, no transparency, not web
                ("Logo with transparency", false, true, true),  // not photo, has transparency, for web
                ("Simple icon", false, false, true),    // not photo, no transparency, for web
                ("Print quality image", true, false, false),    // photo, no transparency, print
                ("Animation", false, true, true),       // not photo, transparency, web, animation
                ("Archive storage", true, false, false) // photo, no transparency, archival
            };

            Console.WriteLine("Use Case                 | Recommended Format | Reason");
            Console.WriteLine("-------------------------|-------------------|--------");

            foreach (var useCase in useCases)
            {
                var name = useCase.Item1;
                var isPhoto = useCase.Item2;
                var needsTransparency = useCase.Item3;
                var isForWeb = useCase.Item4;

                ImageFormat recommendedFormat;
                string reason;

                // Traditional C# 7.3 compatible logic
                if (needsTransparency && isForWeb)
                {
                    recommendedFormat = ImageFormat.Png;
                    reason = "Web + transparency";
                }
                else if (needsTransparency && !isForWeb)
                {
                    recommendedFormat = ImageFormat.Tiff;
                    reason = "High quality + transparency";
                }
                else if (isPhoto && isForWeb)
                {
                    recommendedFormat = ImageFormat.Jpeg;
                    reason = "Photo compression for web";
                }
                else if (isPhoto && !isForWeb)
                {
                    recommendedFormat = ImageFormat.Tiff;
                    reason = "High quality photo";
                }
                else if (isForWeb)
                {
                    recommendedFormat = ImageFormat.Png;
                    reason = "Web graphics";
                }
                else
                {
                    recommendedFormat = ImageFormat.Bmp;
                    reason = "Simple uncompressed";
                }

                Console.WriteLine($"{name,-24} | {recommendedFormat,-17} | {reason}");
            }

            Console.WriteLine();
        }

        private static void Example6_WebCompatibility()
        {
            Console.WriteLine("6. Web Compatibility Analysis");
            Console.WriteLine("-----------------------------");

            // Get all enum values
            var allFormats = Enum.GetValues(typeof(ImageFormat)).Cast<ImageFormat>().ToArray();

            var webCompatible = allFormats.Where(f => f.IsWebCompatible()).ToArray();
            var notWebCompatible = allFormats.Where(f => !f.IsWebCompatible()).ToArray();

            Console.WriteLine("Web Compatible Formats:");
            foreach (var format in webCompatible)
            {
                var mimeType = format.GetMimeType();
                var extension = format.GetFileExtension();
                Console.WriteLine($"  {format} ({extension}) - {mimeType}");
            }

            Console.WriteLine("\nNot Web Compatible:");
            foreach (var format in notWebCompatible.Take(10)) // Limit output
            {
                var extension = format.GetFileExtension();
                Console.WriteLine($"  {format} ({extension})");
            }

            // Modern vs Legacy formats
            Console.WriteLine("\nModern Web Formats:");
            var modernFormats = new ImageFormat[] { ImageFormat.WebP, ImageFormat.Avif, ImageFormat.Heif };
            foreach (var format in modernFormats)
            {
                if (format.IsWebCompatible())
                {
                    var supportsQuality = format.SupportsQuality();
                    var supportsTransparency = format.SupportsTransparency();
                    Console.WriteLine($"  {format}: Quality={supportsQuality}, Transparency={supportsTransparency}");
                }
            }

            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrates practical usage in export scenarios
        /// </summary>
        public static void DemonstrateExportUsage()
        {
            Console.WriteLine("=== Practical Export Usage ===");
            Console.WriteLine();

            // Simulate choosing format based on requirements
            var requirements = new
            {
                NeedsTransparency = true,
                IsForWeb = true,
                MaxFileSize = 100000, // 100KB
                QualityImportant = false
            };

            Console.WriteLine("Export Requirements:");
            Console.WriteLine($"  Needs Transparency: {requirements.NeedsTransparency}");
            Console.WriteLine($"  Is For Web: {requirements.IsForWeb}");
            Console.WriteLine($"  Max File Size: {requirements.MaxFileSize:N0} bytes");
            Console.WriteLine($"  Quality Important: {requirements.QualityImportant}");
            Console.WriteLine();

            // Choose format based on requirements
            ImageFormat chosenFormat;
            string reason;

            if (requirements.NeedsTransparency)
            {
                if (requirements.IsForWeb)
                {
                    chosenFormat = ImageFormat.Png;
                    reason = "PNG supports transparency and is web-compatible";
                }
                else
                {
                    chosenFormat = ImageFormat.Tiff;
                    reason = "TIFF supports transparency with high quality";
                }
            }
            else if (requirements.IsForWeb)
            {
                if (requirements.QualityImportant)
                {
                    chosenFormat = ImageFormat.WebP;
                    reason = "WebP offers best compression with quality";
                }
                else
                {
                    chosenFormat = ImageFormat.Jpeg;
                    reason = "JPEG is universally supported on web";
                }
            }
            else
            {
                chosenFormat = ImageFormat.Bmp;
                reason = "BMP for simple uncompressed storage";
            }

            Console.WriteLine("Format Selection Result:");
            Console.WriteLine($"  Chosen Format: {chosenFormat}");
            Console.WriteLine($"  File Extension: {chosenFormat.GetFileExtension()}");
            Console.WriteLine($"  MIME Type: {chosenFormat.GetMimeType()}");
            Console.WriteLine($"  Reason: {reason}");
            Console.WriteLine($"  Supports Quality: {chosenFormat.SupportsQuality()}");
            Console.WriteLine($"  Supports Transparency: {chosenFormat.SupportsTransparency()}");
            Console.WriteLine($"  Is Lossy: {chosenFormat.IsLossy()}");
            Console.WriteLine($"  Web Compatible: {chosenFormat.IsWebCompatible()}");
        }
    }
}
