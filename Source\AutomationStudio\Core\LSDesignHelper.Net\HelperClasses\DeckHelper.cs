using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using System.Windows.Media.Imaging;
using FS.AS.Core.HardwareResources;
using FS.AS.Core.Helpers;
using FS.AS.Core.LSDesignHelper.Net.Deck;
using FS.AS.Core.RobotDeck;
using FS.AS.Helpers;
using FS.AS.Helpers.Data;
using Symyx.ImageServer.Interop;
using Image = System.Drawing.Image;
using ImageFormat = System.Drawing.Imaging.ImageFormat;

namespace FS.AS.Core.LSDesignHelper.Net.HelperClasses
{
    internal static class DeckHelper
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="filename"></param>
        /// <returns></returns>
        private static int SaveImage(string filename)
        {
            try
            {
                IEpochImages epochImages = new EpochImages();

                var epochImage = epochImages.Add();
                epochImage.ImageDataFormat = EpochImageFormat.imJPEG;
                epochImage.ImageFileName = filename;
                epochImage.Store(false);

                return epochImage.DatabaseID;
            }
            catch (Exception)
            {
                throw new Exception(
                    string.Format(
                        "Unable to save deck image {0} to the database.  There may be a problem with the connection.",
                        filename));
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="filename"></param>
        /// <returns></returns>
        public static int SaveDeckImage(string filename)
        {
            return SaveImage(SettingsHelper.GetDeckImagesDirectory().Combine(filename));
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public static int SaveDeckLayout()
        {
            var fileName = Path.GetTempFileName();

            try
            {
                using (var bitMap = TrainingHelper.GetDeckImage(0.625))
                {
                    bitMap.Save(fileName, ImageFormat.Jpeg);
                    return SaveImage(fileName);
                }
            }
            catch (Exception)
            {
                throw new ApplicationException("Unable to load sub element reference deck image");
            }
            finally
            {
                File.Delete(fileName);
            }
        }
       
        public static BitmapImage GetDBImage(int id)
        {
            if (id == 0)
                return null;

            var fileName = Path.GetTempFileName();
            try
            {
                var obj = ObjectHelper.GetObject("Image2", id);
                Images2 images = new Images2Class();
                var image = images.Add();
                image.XML = obj;
                image.WriteImageFile(fileName, Symyx.ImageServer.Interop.ImageFormat.imgJPEG);

                using (var stream = new FileStream(fileName, FileMode.Open, FileAccess.Read))
                {
                    var newImage = Image.FromStream(stream);
                    return Utilities.ConvertImage(newImage);
                }
            }
            catch (Exception)
            {
                throw new ApplicationException("Unable to load sub element reference deck image");
            }
            finally
            {
                File.Delete(fileName);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public static Dictionary<string, string[]> GetDeckPositions(bool includeDoNotTrain=false)
        {
            var names = TrainingHelper.GetDeckTrainingPositionNames();

            return names.ToDictionary(name => name, name => TrainingHelper.GetSubstrateNames(name, includeDoNotTrain).ToArray());
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="includeDoNotTrain"></param>
        /// <returns></returns>
        public static List<OffDeckDeckSubstrate> GetOffDeckPositions(bool includeDoNotTrain = false)
        {
            var names = new List<string> {"Carousel", "Hotel", "Liconic Left", "Liconic Middle", "Liconic Right", "Incubator" };

            return (from name in names
                let list = TrainingHelper.GetSubstrateNames(name, includeDoNotTrain)
                where list.Count > 0
                select
                    new OffDeckDeckSubstrate() {PositionName = name, SupportedSubstrateTypes = new List<string>(list)})
                .ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public static IList<string> GetMovePlatePositions()
        {
            var deckPositions = new List<string>();
            var substrates = SubstrateHelper.GetSubstrates();
            foreach (var substrate in substrates)
            {
                var vialSubstrate = substrate as VialClass;
                if (vialSubstrate == null || !vialSubstrate.Name.Contains("Plate Gripper") ||
                    vialSubstrate.Name.ToLower().Contains("arm out of way")) continue;
                var list = (from VialPosition position in vialSubstrate.Children select position.Name).ToList();

                var query = list.Select(c =>
                        new
                        {
                            SplitArray = c.Split(' '), //to avoid multiple split
                            Value = c,
                        })
                    .Select(c => new
                    {
                        Prefix = c.SplitArray.First(),
                        //you can avoid multiple split if you split first and use it later
                        PostFix = c.SplitArray.Last(),
                        Value = c.Value,
                    })
                    .GroupBy(r => r.Prefix)
                    .Select(grp => new
                    {
                        //Key = grp.Key,
                        Value = grp,
                    }).ToDictionary(x => x.Value.Key, x => x.Value.Select(s => s.Value).ToList());


                foreach (var item in query)
                {
                    item.Value.RemoveAll(
                        s =>
                            s.Contains("Gripper") || s.Contains("Regrip") || s.Contains("*") || s.Contains("Cover") ||
                            s.Contains("Balance"));

                    if (item.Key == "Deck")
                    {
                        foreach (var position in item.Value)
                        {
                            if (position.Contains("Tip Rack")) continue;
                            var m =
                                new Regex(@"Deck\s[0-9]{1,2}-[0-9]{1,2}\s[A-Za-z\- ]+\s[1-3]").Match(position);
                            if (m.Success)
                            {
                                var pos = m.Value.Trim();
                                if (!deckPositions.Contains(pos))
                                    deckPositions.Add(pos);
                            }
                        }

                        item.Value.Clear();
                    }
                    else if (item.Key == "Off")
                    {
                        foreach (var position in item.Value)
                        {
                            if (position.Contains("Tip Rack")) continue;
                            var m =
                                new Regex(@"Off Deck\s[0-9]{1,2}-[0-9]{1,2}\s[A-Za-z\- ]+\s[1-3]").Match(position);
                            if (m.Success)
                            {
                                var pos = m.Value.Trim();
                                if (!deckPositions.Contains(pos))
                                    deckPositions.Add(pos);
                            }
                        }

                        item.Value.Clear();
                    }
                    else if (item.Key == "Carousel")
                    {
                        deckPositions.Add(item.Key);
                    }
                    else if (item.Key == "Hotel")
                    {
                        deckPositions.Add(item.Key);
                    }
                    else if (item.Key == "Liconic")
                    {
                        var positions =
                        (from position in item.Value
                            let split = position.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries)
                            where split.Length >= 2
                            select split[0] + " " + split[1]);

                        deckPositions.AddRange(positions.Distinct());
                    }
                    else if (item.Value.Count > 0)
                    {
                        var common = new string(
                            item.Value.First().Substring(0, item.Value.Min(s => s.Length))
                                .TakeWhile((c, i) => item.Value.All(s => s[i] == c)).ToArray());
                        common = common.Trim();
                        if (!deckPositions.Contains(common))
                            deckPositions.Add(common);
                    }
                }
            }

            return deckPositions.Distinct().ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public static IList<string> GetPlateGripperTrainedPositions()
        {
            var deckPositions = new List<string>();
            var substrates = SubstrateHelper.GetSubstrates();
            foreach (var substrate in substrates)
            {
                var vialSubstrate = substrate as VialClass;
                if (vialSubstrate == null || !vialSubstrate.Name.Contains("Plate Gripper") || vialSubstrate.Name.ToLower().Contains("arm out of way")) continue;
                var list = (from VialPosition position in vialSubstrate.Children select position.Name).ToList();

                var query = list.Select(c =>
                    new
                    {
                        SplitArray = c.Split(' '), //to avoid multiple split
                        Value = c,
                    })
                    .Select(c => new
                    {
                        Prefix = c.SplitArray.First(),
                        //you can avoid multiple split if you split first and use it later
                        PostFix = c.SplitArray.Last(),
                        Value = c.Value,
                    })
                    .GroupBy(r => r.Prefix)
                    .Select(grp => new
                    {
                        //Key = grp.Key,
                        Value = grp,
                    }).ToDictionary(x => x.Value.Key, x => x.Value.Select(s => s.Value).ToList());


                foreach (var item in query)
                {
                    item.Value.RemoveAll(s => s.Contains("Gripper") || s.Contains("Regrip") || s.Contains("*") || s.Contains("Cover"));

                    if (item.Key == "Deck")
                    {
                        foreach (var position in item.Value.Where(position => !position.Contains("Tip Rack")).Where(position => !deckPositions.Contains(position)))
                        {
                            deckPositions.Add(position);
                        }

                        item.Value.Clear();
                    }
                    else if (item.Key == "Off")
                    {
                        foreach (var position in item.Value.Where(position => !position.Contains("Tip Rack")).Where(position => !deckPositions.Contains(position)))
                        {
                            deckPositions.Add(position);
                        }

                        item.Value.Clear();
                    }
                    //else if (item.Key == "Carousel")
                    //{
                    //    deckPositions.AddRange(item.Value);
                    //}
                    //else if (item.Key == "Liconic")
                    //{
                    //    //deckPositions.AddRange(from position in item.Value
                    //    //                       let split = position.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries)
                    //    //                       where split.Length == 2
                    //    //                       select split[0] + " " + split[1]);
                    //    deckPositions.AddRange(item.Value);
                    //}
                    //else if (item.Key == "Hotel")
                    //{
                    //    deckPositions.AddRange(item.Value);
                    //}
                    else if (item.Value.Count > 0)
                    {
                        deckPositions.AddRange(item.Value);
                    }
                }
            }

            return deckPositions;
        } 

        /// <summary>
        /// 
        /// </summary>
        /// <param name="elementPosition"></param>
        /// <param name="subElementPosition"></param>
        /// <returns></returns>
        public static string GetDeckPositionName(int elementPosition, int subElementPosition)
        {
            return TrainingHelper.GetDeckTrainingPositionName(elementPosition, subElementPosition);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public static IDeck GetCurrentDeck()
        {
            TrainingHelper.RefreshSubtrates();
            TrainingHelper.RefreshDeck();
            return TrainingHelper.GetDeck();
        }
       
        public static string GetElementName(Point point, Rectangle rect, DeckLayout deck)
        {
            try
            {
                var size = rect.Size;

                if (point.Y < 0 || point.Y > size.Height)
                {
                    return string.Empty;
                }

                var relLeftBorder = (int)(size.Width * deck.LeftBorder / deck.Width);
                var unitWidth = (size.Width - (size.Width * deck.LeftBorder / deck.Width) - (size.Width * deck.RightBorder / deck.Width)) / deck.UnitsWide;
                var localX = point.X - relLeftBorder;
                var localY = size.Height - point.Y;
                if (localX <= 0) return string.Empty;
                //figure out which unit is clicked in
                var startUnit = (int)(localX / unitWidth) + 1;
                if (startUnit > deck.UnitsWide)
                {
                    return string.Empty;
                }

                //make sure there is an element there and set it to the startunit of the element
                foreach (var deckElement in deck.DeckElements)
                {
                    if ((startUnit < deckElement.StartUnit) || (startUnit >= deckElement.StartUnit + deckElement.Width))
                        continue;
                    startUnit = deckElement.StartUnit;
                    var count = deckElement.DeckSubElements.Count;
                    if (count <= 0) return string.Empty;
                    var subElementHeight = size.Height / count;
                    var index = localY / subElementHeight;
                    var subElementPositon = deckElement.DeckSubElements.ElementAt(index).Position;

                    var elementName = TrainingHelper.GetDeckTrainingPositionName(startUnit, subElementPositon);

                    return elementName;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        internal class AxHostConverter : AxHost
        {
            private AxHostConverter() : base("") { }

            public static stdole.IPictureDisp ImageToPictureDisp(Image image)
            {
                return (stdole.IPictureDisp)GetIPictureDispFromPicture(image);
            }

            public static Image PictureDispToImage(stdole.IPictureDisp pictureDisp)
            {
                return GetPictureFromIPicture(pictureDisp);
            }
        }
    }
}
