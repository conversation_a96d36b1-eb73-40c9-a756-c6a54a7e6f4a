using System;
using System.Collections.Generic;
using System.IO;
using FS.AS.Core.Imaging;

namespace FS.AS.Core.Imaging.Examples
{
    /// <summary>
    /// Example usage of EpochImage Export method in C# 7.3
    /// </summary>
    public static class EpochImageExportExample
    {
        /// <summary>
        /// Demonstrates various export scenarios using C# 7.3 compatible syntax
        /// </summary>
        public static void RunExportExamples()
        {
            Console.WriteLine("=== EpochImage Export Examples (C# 7.3) ===");
            Console.WriteLine();

            // Example 1: Basic export to different formats
            Example1_BasicExport();

            // Example 2: Export with quality settings
            Example2_ExportWithQuality();

            // Example 3: Multiple format export
            Example3_MultipleFormatExport();

            // Example 4: Format validation and recommendations
            Example4_FormatValidationAndRecommendations();

            // Example 5: Export information and capabilities
            Example5_ExportInformation();

            // Example 6: Error handling scenarios
            Example6_ErrorHandling();

            // Example 7: Traditional C# 7.3 conditional logic
            Example7_TraditionalConditionalLogic();
        }

        private static void Example1_BasicExport()
        {
            Console.WriteLine("1. Basic Export to Different Formats");
            Console.WriteLine("------------------------------------");

            try
            {
                // Create a sample epoch image (simulated)
                var epochImage = CreateSampleEpochImage();

                // Export to different formats using C# 7.3 compatible syntax
                var formats = new ImageFormat[]
                {
                    ImageFormat.Png,
                    ImageFormat.Jpeg,
                    ImageFormat.Bmp,
                    ImageFormat.Gif,
                    ImageFormat.Tiff
                };

                foreach (var format in formats)
                {
                    var exportedData = epochImage.Export(format);
                    
                    if (exportedData != null)
                    {
                        Console.WriteLine($"   {format}: {exportedData.Length:N0} bytes");
                    }
                    else
                    {
                        Console.WriteLine($"   {format}: Export failed");
                    }
                }

                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error in basic export: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static void Example2_ExportWithQuality()
        {
            Console.WriteLine("2. Export with Quality Settings");
            Console.WriteLine("-------------------------------");

            try
            {
                var epochImage = CreateSampleEpochImage();

                // Test different quality levels for JPEG (C# 7.3 compatible)
                var qualityLevels = new int[] { 10, 25, 50, 75, 90, 100 };

                Console.WriteLine("   JPEG Quality Comparison:");
                Console.WriteLine("   Quality | Size (bytes)");
                Console.WriteLine("   --------|-------------");

                foreach (var quality in qualityLevels)
                {
                    var exportedData = epochImage.ExportWithQuality(ImageFormat.Jpeg, quality);
                    
                    if (exportedData != null)
                    {
                        Console.WriteLine($"   {quality,7} | {exportedData.Length,11:N0}");
                    }
                    else
                    {
                        Console.WriteLine($"   {quality,7} | Failed");
                    }
                }

                // Test quality with non-JPEG format (should ignore quality)
                var pngWithQuality = epochImage.ExportWithQuality(ImageFormat.Png, 50);
                if (pngWithQuality != null)
                {
                    Console.WriteLine($"   PNG (quality ignored): {pngWithQuality.Length:N0} bytes");
                }

                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error in quality export: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static void Example3_MultipleFormatExport()
        {
            Console.WriteLine("3. Multiple Format Export");
            Console.WriteLine("-------------------------");

            try
            {
                var epochImage = CreateSampleEpochImage();

                // Export to multiple formats simultaneously
                var formatsToExport = new ImageFormat[]
                {
                    ImageFormat.Png,
                    ImageFormat.Jpeg,
                    ImageFormat.Bmp,
                    ImageFormat.Gif
                };

                var results = epochImage.ExportMultiple(formatsToExport);

                Console.WriteLine($"   Exported to {results.Count} formats:");
                
                // C# 7.3 compatible iteration
                foreach (var kvp in results)
                {
                    var format = kvp.Key;
                    var data = kvp.Value;
                    Console.WriteLine($"   {format}: {data.Length:N0} bytes");
                }

                // Calculate total size
                var totalSize = 0;
                foreach (var kvp in results)
                {
                    totalSize += kvp.Value.Length;
                }
                Console.WriteLine($"   Total size: {totalSize:N0} bytes");

                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error in multiple format export: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static void Example4_FormatValidationAndRecommendations()
        {
            Console.WriteLine("4. Format Validation and Recommendations");
            Console.WriteLine("----------------------------------------");

            try
            {
                var epochImage = CreateSampleEpochImage();

                // Check format support using C# 7.3 compatible approach
                var allFormats = new ImageFormat[]
                {
                    ImageFormat.Png,
                    ImageFormat.Jpeg,
                    ImageFormat.Bmp,
                    ImageFormat.Gif,
                    ImageFormat.Tiff,
                    ImageFormat.Ico,
                    ImageFormat.Emf,
                    ImageFormat.Wmf
                };

                Console.WriteLine("   Format Support:");
                Console.WriteLine("   Format    | Supported");
                Console.WriteLine("   ----------|----------");

                foreach (var format in allFormats)
                {
                    var isSupported = epochImage.IsFormatSupported(format);
                    Console.WriteLine($"   {format,-9} | {(isSupported ? "Yes" : "No"),9}");
                }

                // Get recommended format
                var recommendedFormat = epochImage.GetRecommendedExportFormat();
                Console.WriteLine($"\n   Recommended format: {recommendedFormat}");

                // Traditional C# 7.3 conditional logic for format selection
                ImageFormat selectedFormat;
                if (recommendedFormat == ImageFormat.Jpeg)
                {
                    selectedFormat = ImageFormat.Jpeg;
                    Console.WriteLine("   Selected JPEG for photographic content");
                }
                else if (recommendedFormat == ImageFormat.Png)
                {
                    selectedFormat = ImageFormat.Png;
                    Console.WriteLine("   Selected PNG for general purpose or transparency");
                }
                else if (recommendedFormat == ImageFormat.Gif)
                {
                    selectedFormat = ImageFormat.Gif;
                    Console.WriteLine("   Selected GIF for simple graphics");
                }
                else
                {
                    selectedFormat = ImageFormat.Png; // Default fallback
                    Console.WriteLine("   Defaulted to PNG as fallback");
                }

                var exportedData = epochImage.Export(selectedFormat);
                if (exportedData != null)
                {
                    Console.WriteLine($"   Exported using recommended format: {exportedData.Length:N0} bytes");
                }

                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error in format validation: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static void Example5_ExportInformation()
        {
            Console.WriteLine("5. Export Information and Capabilities");
            Console.WriteLine("--------------------------------------");

            try
            {
                var epochImage = CreateSampleEpochImage();

                var formats = new ImageFormat[]
                {
                    ImageFormat.Jpeg,
                    ImageFormat.Png,
                    ImageFormat.Bmp,
                    ImageFormat.Gif
                };

                Console.WriteLine("   Format Capabilities:");
                Console.WriteLine("   Format | Quality | Transparency | Lossy | Extension | MIME Type");
                Console.WriteLine("   -------|---------|--------------|-------|-----------|----------");

                foreach (var format in formats)
                {
                    var info = epochImage.GetExportInfo(format);
                    
                    Console.WriteLine($"   {format,-6} | {(info.SupportsQuality ? "Yes" : "No"),7} | " +
                                    $"{(info.SupportsTransparency ? "Yes" : "No"),12} | " +
                                    $"{(info.IsLossy ? "Yes" : "No"),5} | " +
                                    $"{info.TypicalExtension,-9} | {info.MimeType}");
                }

                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error getting export information: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static void Example6_ErrorHandling()
        {
            Console.WriteLine("6. Error Handling Scenarios");
            Console.WriteLine("---------------------------");

            try
            {
                // Test with null/disposed image
                var epochImage = CreateSampleEpochImage();
                epochImage.Dispose(); // Dispose first

                var result = epochImage.Export(ImageFormat.Png);
                if (result == null)
                {
                    Console.WriteLine("   ✓ Correctly handled disposed image (returned null)");
                }

                // Test with invalid quality values
                var validImage = CreateSampleEpochImage();
                
                var invalidQualityResults = new[]
                {
                    validImage.ExportWithQuality(ImageFormat.Jpeg, -10),  // Too low
                    validImage.ExportWithQuality(ImageFormat.Jpeg, 150),  // Too high
                    validImage.ExportWithQuality(ImageFormat.Jpeg, 0),    // Minimum
                    validImage.ExportWithQuality(ImageFormat.Jpeg, 100)   // Maximum
                };

                Console.WriteLine("   Quality validation results:");
                var qualityValues = new int[] { -10, 150, 0, 100 };
                for (int i = 0; i < invalidQualityResults.Length; i++)
                {
                    var data = invalidQualityResults[i];
                    var quality = qualityValues[i];
                    var status = data != null ? $"{data.Length:N0} bytes" : "Failed";
                    Console.WriteLine($"   Quality {quality,3}: {status}");
                }

                validImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error in error handling test: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static void Example7_TraditionalConditionalLogic()
        {
            Console.WriteLine("7. Traditional C# 7.3 Conditional Logic");
            Console.WriteLine("----------------------------------------");

            try
            {
                var epochImage = CreateSampleEpochImage();

                // Traditional if-else chain for format selection (C# 7.3 compatible)
                ImageFormat chosenFormat;
                string reason;

                var hasTransparency = epochImage.HasTransparency(); // Assume this method exists
                var isLargeImage = (epochImage.Width * epochImage.Height) > 500000;
                var isSmallImage = (epochImage.Width * epochImage.Height) < 10000;

                if (hasTransparency)
                {
                    chosenFormat = ImageFormat.Png;
                    reason = "Image has transparency - PNG required";
                }
                else if (isLargeImage)
                {
                    chosenFormat = ImageFormat.Jpeg;
                    reason = "Large image - JPEG for compression";
                }
                else if (isSmallImage)
                {
                    chosenFormat = ImageFormat.Gif;
                    reason = "Small image - GIF suitable";
                }
                else
                {
                    chosenFormat = ImageFormat.Png;
                    reason = "General purpose - PNG is versatile";
                }

                Console.WriteLine($"   Selected format: {chosenFormat}");
                Console.WriteLine($"   Reason: {reason}");

                // Traditional switch statement for export handling (C# 7.3 compatible)
                byte[] exportResult;
                switch (chosenFormat)
                {
                    case ImageFormat.Jpeg:
                        exportResult = epochImage.ExportWithQuality(chosenFormat, 85);
                        Console.WriteLine("   Used JPEG with 85% quality");
                        break;
                    case ImageFormat.Png:
                        exportResult = epochImage.Export(chosenFormat);
                        Console.WriteLine("   Used PNG with default settings");
                        break;
                    case ImageFormat.Gif:
                        exportResult = epochImage.Export(chosenFormat);
                        Console.WriteLine("   Used GIF with default settings");
                        break;
                    default:
                        exportResult = epochImage.Export(ImageFormat.Png);
                        Console.WriteLine("   Fallback to PNG");
                        break;
                }

                if (exportResult != null)
                {
                    Console.WriteLine($"   Export successful: {exportResult.Length:N0} bytes");
                    
                    // Traditional approach to determine file size category
                    string sizeCategory;
                    if (exportResult.Length < 10000)
                        sizeCategory = "Small";
                    else if (exportResult.Length < 100000)
                        sizeCategory = "Medium";
                    else if (exportResult.Length < 1000000)
                        sizeCategory = "Large";
                    else
                        sizeCategory = "Very Large";
                    
                    Console.WriteLine($"   Size category: {sizeCategory}");
                }
                else
                {
                    Console.WriteLine("   Export failed");
                }

                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error in conditional logic example: {ex.Message}");
            }

            Console.WriteLine();
        }

        #region Helper Methods

        /// <summary>
        /// Creates a sample EpochImage for testing (simulated)
        /// </summary>
        /// <returns>A sample EpochImage instance</returns>
        private static EpochImage CreateSampleEpochImage()
        {
            // This would normally create a real image
            // For this example, we'll simulate the creation
            var epochImage = new EpochImage(DateTime.Now);
            
            // In a real implementation, you would load or create actual image data
            // epochImage.LoadFromFile("sample.jpg") or similar
            
            return epochImage;
        }

        #endregion
    }
}
