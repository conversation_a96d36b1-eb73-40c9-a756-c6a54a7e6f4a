using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading.Tasks;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// Interface for a single epoch image
    /// </summary>
    public interface IEpochImage : IDisposable
    {
        #region Properties

        /// <summary>
        /// Gets the unique identifier of the epoch image
        /// </summary>
        Guid Id { get; }

        /// <summary>
        /// Gets the timestamp of the epoch
        /// </summary>
        DateTime Timestamp { get; }

        /// <summary>
        /// Gets the image data
        /// </summary>
        Image Image { get; }

        /// <summary>
        /// Gets the raw image data as bytes
        /// </summary>
        byte[] ImageData { get; }

        /// <summary>
        /// Gets the image format
        /// </summary>
        string Format { get; }

        /// <summary>
        /// Gets the image width
        /// </summary>
        int Width { get; }

        /// <summary>
        /// Gets the image height
        /// </summary>
        int Height { get; }

        /// <summary>
        /// Gets the image size in bytes
        /// </summary>
        long Size { get; }

        /// <summary>
        /// Gets or sets the metadata associated with the epoch image
        /// </summary>
        Dictionary<string, object> Metadata { get; set; }

        /// <summary>
        /// Gets or sets the file path of the epoch image
        /// </summary>
        string FilePath { get; set; }

        /// <summary>
        /// Gets a value indicating whether the image is loaded in memory
        /// </summary>
        bool IsLoaded { get; }

        /// <summary>
        /// Gets or sets the name of the epoch image
        /// </summary>
        string Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the epoch image
        /// </summary>
        string Description { get; set; }

        /// <summary>
        /// Gets or sets the quality of the epoch image (0-100)
        /// </summary>
        int Quality { get; set; }

        /// <summary>
        /// Gets the creation time of the epoch image
        /// </summary>
        DateTime CreationTime { get; }

        /// <summary>
        /// Gets the last modified time of the epoch image
        /// </summary>
        DateTime LastModifiedTime { get; }

        /// <summary>
        /// Gets or sets the compression type used for the image
        /// </summary>
        string CompressionType { get; set; }

        /// <summary>
        /// Gets the pixel format of the image
        /// </summary>
        string PixelFormat { get; }

        /// <summary>
        /// Gets the horizontal resolution (DPI) of the image
        /// </summary>
        float HorizontalResolution { get; }

        /// <summary>
        /// Gets the vertical resolution (DPI) of the image
        /// </summary>
        float VerticalResolution { get; }

        /// <summary>
        /// Gets or sets the tags associated with the epoch image
        /// </summary>
        List<string> Tags { get; set; }

        /// <summary>
        /// Gets a value indicating whether the image has been modified
        /// </summary>
        bool IsModified { get; }

        #endregion

        #region Methods

        /// <summary>
        /// Loads the image into memory
        /// </summary>
        /// <returns>True if the image was loaded successfully; otherwise, false</returns>
        Task<bool> LoadAsync();

        /// <summary>
        /// Loads the image into memory from the specified file path
        /// </summary>
        /// <param name="filePath">The file path to load from</param>
        /// <returns>True if the image was loaded successfully; otherwise, false</returns>
        Task<bool> LoadAsync(string filePath);

        /// <summary>
        /// Unloads the image from memory
        /// </summary>
        void Unload();

        /// <summary>
        /// Saves the epoch image to the specified file path
        /// </summary>
        /// <param name="filePath">The file path to save to</param>
        /// <returns>True if the image was saved successfully; otherwise, false</returns>
        Task<bool> SaveAsync(string filePath);

        /// <summary>
        /// Saves the epoch image to its current file path
        /// </summary>
        /// <returns>True if the image was saved successfully; otherwise, false</returns>
        Task<bool> SaveAsync();

        /// <summary>
        /// Creates a copy of the epoch image
        /// </summary>
        /// <returns>A copy of the epoch image</returns>
        IEpochImage Clone();

        /// <summary>
        /// Creates a deep copy of the epoch image
        /// </summary>
        /// <returns>A deep copy of the epoch image</returns>
        IEpochImage DeepClone();

        /// <summary>
        /// Resizes the image to the specified dimensions
        /// </summary>
        /// <param name="width">The new width</param>
        /// <param name="height">The new height</param>
        /// <returns>A new epoch image with the resized image</returns>
        IEpochImage Resize(int width, int height);

        /// <summary>
        /// Crops the image to the specified rectangle
        /// </summary>
        /// <param name="cropRectangle">The rectangle to crop to</param>
        /// <returns>A new epoch image with the cropped image</returns>
        IEpochImage Crop(Rectangle cropRectangle);

        /// <summary>
        /// Rotates the image by the specified angle
        /// </summary>
        /// <param name="angle">The angle to rotate by (in degrees)</param>
        /// <returns>A new epoch image with the rotated image</returns>
        IEpochImage Rotate(float angle);

        /// <summary>
        /// Flips the image horizontally
        /// </summary>
        /// <returns>A new epoch image with the flipped image</returns>
        IEpochImage FlipHorizontal();

        /// <summary>
        /// Flips the image vertically
        /// </summary>
        /// <returns>A new epoch image with the flipped image</returns>
        IEpochImage FlipVertical();

        /// <summary>
        /// Converts the image to grayscale
        /// </summary>
        /// <returns>A new epoch image with the grayscale image</returns>
        IEpochImage ToGrayscale();

        /// <summary>
        /// Adjusts the brightness of the image
        /// </summary>
        /// <param name="brightness">The brightness adjustment (-100 to 100)</param>
        /// <returns>A new epoch image with the adjusted brightness</returns>
        IEpochImage AdjustBrightness(int brightness);

        /// <summary>
        /// Adjusts the contrast of the image
        /// </summary>
        /// <param name="contrast">The contrast adjustment (-100 to 100)</param>
        /// <returns>A new epoch image with the adjusted contrast</returns>
        IEpochImage AdjustContrast(int contrast);

        /// <summary>
        /// Gets a thumbnail of the image
        /// </summary>
        /// <param name="width">The thumbnail width</param>
        /// <param name="height">The thumbnail height</param>
        /// <returns>A thumbnail image</returns>
        Image GetThumbnail(int width, int height);

        /// <summary>
        /// Gets the histogram of the image
        /// </summary>
        /// <returns>The histogram data</returns>
        int[] GetHistogram();

        /// <summary>
        /// Gets statistics about the image
        /// </summary>
        /// <returns>Image statistics</returns>
        ImageStatistics GetStatistics();

        /// <summary>
        /// Validates the integrity of the epoch image
        /// </summary>
        /// <returns>True if the image is valid; otherwise, false</returns>
        bool Validate();

        /// <summary>
        /// Compares this epoch image with another
        /// </summary>
        /// <param name="other">The other epoch image to compare with</param>
        /// <returns>The comparison result</returns>
        ImageComparisonResult Compare(IEpochImage other);

        /// <summary>
        /// Exports the image to the specified format
        /// </summary>
        /// <param name="format">The format to export to</param>
        /// <returns>The exported image data</returns>
        byte[] Export(ImageFormat format);

        /// <summary>
        /// Refreshes the image data from the file system
        /// </summary>
        /// <returns>True if the refresh was successful; otherwise, false</returns>
        Task<bool> RefreshAsync();

        #endregion
    }
}
