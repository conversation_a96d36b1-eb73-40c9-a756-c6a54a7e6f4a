using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Threading.Tasks;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// Interface for advanced image operations
    /// </summary>
    public interface IImage2 : IDisposable
    {
        #region Properties

        /// <summary>
        /// Gets the unique identifier of the image
        /// </summary>
        Guid Id { get; }

        /// <summary>
        /// Gets or sets the name of the image
        /// </summary>
        string Name { get; set; }

        /// <summary>
        /// Gets the width of the image in pixels
        /// </summary>
        int Width { get; }

        /// <summary>
        /// Gets the height of the image in pixels
        /// </summary>
        int Height { get; }

        /// <summary>
        /// Gets the pixel format of the image
        /// </summary>
        PixelFormat PixelFormat { get; }

        /// <summary>
        /// Gets the horizontal resolution (DPI) of the image
        /// </summary>
        float HorizontalResolution { get; }

        /// <summary>
        /// Gets the vertical resolution (DPI) of the image
        /// </summary>
        float VerticalResolution { get; }

        /// <summary>
        /// Gets the size of the image in bytes
        /// </summary>
        long SizeInBytes { get; }

        /// <summary>
        /// Gets or sets the file path of the image
        /// </summary>
        string FilePath { get; set; }

        /// <summary>
        /// Gets the creation time of the image
        /// </summary>
        DateTime CreationTime { get; }

        /// <summary>
        /// Gets the last modified time of the image
        /// </summary>
        DateTime LastModifiedTime { get; }

        /// <summary>
        /// Gets a value indicating whether the image is loaded in memory
        /// </summary>
        bool IsLoaded { get; }

        /// <summary>
        /// Gets a value indicating whether the image has been modified
        /// </summary>
        bool IsModified { get; }

        /// <summary>
        /// Gets the underlying bitmap image
        /// </summary>
        Bitmap Bitmap { get; }

        /// <summary>
        /// Gets or sets the image format
        /// </summary>
        ImageFormat Format { get; set; }

        /// <summary>
        /// Gets or sets the compression quality (0-100)
        /// </summary>
        int Quality { get; set; }

        #endregion

        #region Loading and Saving Methods

        /// <summary>
        /// Loads the image from the specified file path
        /// </summary>
        /// <param name="filePath">The file path to load from</param>
        /// <returns>True if the image was loaded successfully; otherwise, false</returns>
        Task<bool> LoadAsync(string filePath);

        /// <summary>
        /// Loads the image from a byte array
        /// </summary>
        /// <param name="imageData">The image data as bytes</param>
        /// <returns>True if the image was loaded successfully; otherwise, false</returns>
        Task<bool> LoadAsync(byte[] imageData);

        /// <summary>
        /// Loads the image from a stream
        /// </summary>
        /// <param name="stream">The stream to load from</param>
        /// <returns>True if the image was loaded successfully; otherwise, false</returns>
        Task<bool> LoadAsync(Stream stream);

        /// <summary>
        /// Saves the image to the specified file path
        /// </summary>
        /// <param name="filePath">The file path to save to</param>
        /// <returns>True if the image was saved successfully; otherwise, false</returns>
        Task<bool> SaveAsync(string filePath);

        /// <summary>
        /// Saves the image to the specified file path with the given format
        /// </summary>
        /// <param name="filePath">The file path to save to</param>
        /// <param name="format">The image format to save as</param>
        /// <returns>True if the image was saved successfully; otherwise, false</returns>
        Task<bool> SaveAsync(string filePath, ImageFormat format);

        /// <summary>
        /// Saves the image to a stream
        /// </summary>
        /// <param name="stream">The stream to save to</param>
        /// <param name="format">The image format to save as</param>
        /// <returns>True if the image was saved successfully; otherwise, false</returns>
        Task<bool> SaveAsync(Stream stream, ImageFormat format);

        /// <summary>
        /// Gets the image data as a byte array
        /// </summary>
        /// <returns>The image data as bytes</returns>
        byte[] ToByteArray();

        /// <summary>
        /// Gets the image data as a byte array with the specified format
        /// </summary>
        /// <param name="format">The image format</param>
        /// <returns>The image data as bytes</returns>
        byte[] ToByteArray(ImageFormat format);

        #endregion

        #region Transformation Methods

        /// <summary>
        /// Resizes the image to the specified dimensions
        /// </summary>
        /// <param name="width">The new width</param>
        /// <param name="height">The new height</param>
        /// <returns>A new resized image</returns>
        IImage2 Resize(int width, int height);

        /// <summary>
        /// Resizes the image maintaining aspect ratio
        /// </summary>
        /// <param name="maxWidth">The maximum width</param>
        /// <param name="maxHeight">The maximum height</param>
        /// <returns>A new resized image</returns>
        IImage2 ResizeProportional(int maxWidth, int maxHeight);

        /// <summary>
        /// Crops the image to the specified rectangle
        /// </summary>
        /// <param name="cropArea">The area to crop</param>
        /// <returns>A new cropped image</returns>
        IImage2 Crop(Rectangle cropArea);

        /// <summary>
        /// Rotates the image by the specified angle
        /// </summary>
        /// <param name="angle">The angle in degrees</param>
        /// <returns>A new rotated image</returns>
        IImage2 Rotate(float angle);

        /// <summary>
        /// Flips the image horizontally
        /// </summary>
        /// <returns>A new flipped image</returns>
        IImage2 FlipHorizontal();

        /// <summary>
        /// Flips the image vertically
        /// </summary>
        /// <returns>A new flipped image</returns>
        IImage2 FlipVertical();

        /// <summary>
        /// Scales the image by the specified factor
        /// </summary>
        /// <param name="scaleFactor">The scale factor</param>
        /// <returns>A new scaled image</returns>
        IImage2 Scale(float scaleFactor);

        #endregion

        #region Color and Filter Methods

        /// <summary>
        /// Converts the image to grayscale
        /// </summary>
        /// <returns>A new grayscale image</returns>
        IImage2 ToGrayscale();

        /// <summary>
        /// Adjusts the brightness of the image
        /// </summary>
        /// <param name="brightness">The brightness adjustment (-100 to 100)</param>
        /// <returns>A new image with adjusted brightness</returns>
        IImage2 AdjustBrightness(int brightness);

        /// <summary>
        /// Adjusts the contrast of the image
        /// </summary>
        /// <param name="contrast">The contrast adjustment (-100 to 100)</param>
        /// <returns>A new image with adjusted contrast</returns>
        IImage2 AdjustContrast(int contrast);

        /// <summary>
        /// Adjusts the gamma of the image
        /// </summary>
        /// <param name="gamma">The gamma value</param>
        /// <returns>A new image with adjusted gamma</returns>
        IImage2 AdjustGamma(float gamma);

        /// <summary>
        /// Adjusts the hue of the image
        /// </summary>
        /// <param name="hue">The hue adjustment (-180 to 180)</param>
        /// <returns>A new image with adjusted hue</returns>
        IImage2 AdjustHue(int hue);

        /// <summary>
        /// Adjusts the saturation of the image
        /// </summary>
        /// <param name="saturation">The saturation adjustment (-100 to 100)</param>
        /// <returns>A new image with adjusted saturation</returns>
        IImage2 AdjustSaturation(int saturation);

        /// <summary>
        /// Applies a blur filter to the image
        /// </summary>
        /// <param name="radius">The blur radius</param>
        /// <returns>A new blurred image</returns>
        IImage2 Blur(int radius);

        /// <summary>
        /// Applies a sharpen filter to the image
        /// </summary>
        /// <param name="amount">The sharpen amount</param>
        /// <returns>A new sharpened image</returns>
        IImage2 Sharpen(float amount);

        #endregion

        #region Analysis Methods

        /// <summary>
        /// Gets the histogram of the image
        /// </summary>
        /// <returns>The histogram data for each color channel</returns>
        ImageHistogram GetHistogram();

        /// <summary>
        /// Gets statistics about the image
        /// </summary>
        /// <returns>Image statistics</returns>
        ImageStatistics GetStatistics();

        /// <summary>
        /// Gets the dominant colors in the image
        /// </summary>
        /// <param name="colorCount">The number of dominant colors to return</param>
        /// <returns>An array of dominant colors</returns>
        Color[] GetDominantColors(int colorCount);

        /// <summary>
        /// Calculates the average color of the image
        /// </summary>
        /// <returns>The average color</returns>
        Color GetAverageColor();

        /// <summary>
        /// Detects edges in the image
        /// </summary>
        /// <returns>A new image with detected edges</returns>
        IImage2 DetectEdges();

        #endregion

        #region Utility Methods

        /// <summary>
        /// Creates a copy of the image
        /// </summary>
        /// <returns>A copy of the image</returns>
        IImage2 Clone();

        /// <summary>
        /// Creates a thumbnail of the image
        /// </summary>
        /// <param name="width">The thumbnail width</param>
        /// <param name="height">The thumbnail height</param>
        /// <returns>A thumbnail image</returns>
        IImage2 CreateThumbnail(int width, int height);

        /// <summary>
        /// Validates the integrity of the image
        /// </summary>
        /// <returns>True if the image is valid; otherwise, false</returns>
        bool Validate();

        /// <summary>
        /// Compares this image with another image
        /// </summary>
        /// <param name="other">The other image to compare with</param>
        /// <returns>The comparison result</returns>
        ImageComparisonResult Compare(IImage2 other);

        /// <summary>
        /// Refreshes the image data from the file system
        /// </summary>
        /// <returns>True if the refresh was successful; otherwise, false</returns>
        Task<bool> RefreshAsync();

        /// <summary>
        /// Unloads the image from memory
        /// </summary>
        void Unload();

        #endregion
    }
}
