using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing.Design;
using System.Globalization;
using System.IO;
using System.Xml;

namespace FS.AS.Core.RobotDeck
{
    /// <summary>
    /// Complete .NET implementation of XCMDeck class
    /// Represents a robot deck configuration with elements and sub-elements
    /// Uses C# 7.3 features
    /// </summary>
    [Serializable]
    public class XCMDeck : IDeck
    {
        #region Private Fields

        private double width_ = 840;
        private double depth_ = 540;
        private double leftBorder_ = 105;
        private double rightBorder_ = 105;
        private double frontBorder_ = 67.5;
        private double backBorder_ = 67.5;
        private int unitsWide_ = 12;
        private double topShelfWidth_ = 15;
        private double bottomShelfWidth_ = 15;
        private double referenceX_ = 420;
        private double referenceY_ = 270;
        private int startingDeckElementPositionOffset_ = 0;
        private int extendedDeckElementPositionOffset_ = 0;
        private Collection<IDeckElement> deckElements_;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the XCMDeck class
        /// </summary>
        public XCMDeck()
        {
            StartingDeckElementPositionOffset = 0;
            ExtendedDeckElementPositionOffset = 0;
            deckElements_ = new Collection<IDeckElement>();
        }

        #endregion

        #region IDeck Implementation

        /// <summary>
        /// Physically accessible (by arms) width of the overall deck, in millimeters
        /// </summary>
        [DisplayName("Width (mm)")]
        [Description("Width of the overall deck space in mm")]
        [Category("Deck Size")]
        public double Width
        {
            get => width_;
            set
            {
                if (value < 1)
                    width_ = 1;
                else if (value > 10000)
                    width_ = 10000;
                else
                    width_ = value;

                if (leftBorder_ + rightBorder_ > width_)
                {
                    leftBorder_ = width_ / 8;
                    rightBorder_ = width_ / 8;
                }
            }
        }

        /// <summary>
        /// Physically accessible (by arms) depth of the overall deck, in millimeters
        /// </summary>
        [DisplayName("Depth (mm)")]
        [Description("Depth of the overall deck space in mm")]
        [Category("Deck Size")]
        public double Depth
        {
            get => depth_;
            set
            {
                if (value < 1)
                    depth_ = 1;
                else if (value > 10000)
                    depth_ = 10000;
                else
                    depth_ = value;

                if (frontBorder_ + backBorder_ > depth_)
                {
                    frontBorder_ = depth_ / 8;
                    backBorder_ = depth_ / 8;
                }
            }
        }

        /// <summary>
        /// Physical size (in mm) of the left border of the robot deck
        /// </summary>
        [DisplayName("Left Border (mm)")]
        [Description("Physical size (in mm) of the left border of the robot deck")]
        [Category("Deck Borders")]
        public double LeftBorder
        {
            get => leftBorder_;
            set
            {
                if (value < 0)
                    leftBorder_ = 0;
                else if (value > width_ / 2)
                    leftBorder_ = width_ / 2;
                else
                    leftBorder_ = value;
            }
        }

        /// <summary>
        /// Physical size (in mm) of the right border of the robot deck
        /// </summary>
        [DisplayName("Right Border (mm)")]
        [Description("Physical size (in mm) of the right border of the robot deck")]
        [Category("Deck Borders")]
        public double RightBorder
        {
            get => rightBorder_;
            set
            {
                if (value < 0)
                    rightBorder_ = 0;
                else if (value > width_ / 2)
                    rightBorder_ = width_ / 2;
                else
                    rightBorder_ = value;
            }
        }

        /// <summary>
        /// Physical size (in mm) of the front border of the robot deck
        /// </summary>
        [DisplayName("Front Border (mm)")]
        [Description("Physical size (in mm) of the front border of the robot deck")]
        [Category("Deck Borders")]
        public double FrontBorder
        {
            get => frontBorder_;
            set
            {
                if (value < 0)
                    frontBorder_ = 0;
                else if (value > depth_ / 2)
                    frontBorder_ = depth_ / 2;
                else
                    frontBorder_ = value;
            }
        }

        /// <summary>
        /// Physical size (in mm) of the back border of the robot deck
        /// </summary>
        [DisplayName("Back Border (mm)")]
        [Description("Physical size (in mm) of the back border of the robot deck")]
        [Category("Deck Borders")]
        public double BackBorder
        {
            get => backBorder_;
            set
            {
                if (value < 0)
                    backBorder_ = 0;
                else if (value > depth_ / 2)
                    backBorder_ = depth_ / 2;
                else
                    backBorder_ = value;
            }
        }

        /// <summary>
        /// Number of evenly spaced deck positions on the deck
        /// </summary>
        [DisplayName("Units Wide")]
        [Description("Number of evenly spaced deck positions on the deck")]
        [Category("Deck Layout")]
        public int UnitsWide
        {
            get => unitsWide_;
            set
            {
                if (value < 1)
                    unitsWide_ = 1;
                else if (value > 50)
                    unitsWide_ = 50;
                else
                    unitsWide_ = value;
            }
        }

        /// <summary>
        /// Graphical render size of the top unit shelf as a percent of the deck depth (0 - 100)
        /// </summary>
        [DisplayName("Top Shelf Width (%)")]
        [Description("Graphical render size of the top unit shelf as a percent of the deck depth (0 - 100)")]
        [Category("Deck Shelves")]
        public double TopShelfWidth
        {
            get => topShelfWidth_;
            set
            {
                if (value < 0)
                    topShelfWidth_ = 0;
                else if (value > 100)
                    topShelfWidth_ = 100;
                else
                    topShelfWidth_ = value;
            }
        }

        /// <summary>
        /// Graphical render size of the bottom unit shelf as a percent of the deck depth (0 - 100)
        /// </summary>
        [DisplayName("Bottom Shelf Width (%)")]
        [Description("Graphical render size of the bottom unit shelf as a percent of the deck depth (0 - 100)")]
        [Category("Deck Shelves")]
        public double BottomShelfWidth
        {
            get => bottomShelfWidth_;
            set
            {
                if (value < 0)
                    bottomShelfWidth_ = 0;
                else if (value > 100)
                    bottomShelfWidth_ = 100;
                else
                    bottomShelfWidth_ = value;
            }
        }

        /// <summary>
        /// X coordinate of the deck reference point
        /// </summary>
        [DisplayName("Reference X (mm)")]
        [Description("X coordinate of the deck reference point")]
        [Category("Reference Point")]
        public double ReferenceX
        {
            get => referenceX_;
            set
            {
                if (value < 0)
                    referenceX_ = 0;
                else if (value > width_)
                    referenceX_ = width_;
                else
                    referenceX_ = value;
            }
        }

        /// <summary>
        /// Y coordinate of the deck reference point
        /// </summary>
        [DisplayName("Reference Y (mm)")]
        [Description("Y coordinate of the deck reference point")]
        [Category("Reference Point")]
        public double ReferenceY
        {
            get => referenceY_;
            set
            {
                if (value < 0)
                    referenceY_ = 0;
                else if (value > depth_)
                    referenceY_ = depth_;
                else
                    referenceY_ = value;
            }
        }

        /// <summary>
        /// Starting deck element position offset
        /// </summary>
        [DisplayName("Starting Position Offset")]
        [Description("Starting deck element position offset")]
        [Category("Position Offsets")]
        public int StartingDeckElementPositionOffset
        {
            get => startingDeckElementPositionOffset_;
            set => startingDeckElementPositionOffset_ = value;
        }

        /// <summary>
        /// Extended deck element position offset
        /// </summary>
        [DisplayName("Extended Position Offset")]
        [Description("Extended deck element position offset")]
        [Category("Position Offsets")]
        public int ExtendedDeckElementPositionOffset
        {
            get => extendedDeckElementPositionOffset_;
            set => extendedDeckElementPositionOffset_ = value;
        }

        /// <summary>
        /// The current deck elements associated with the deck
        /// </summary>
        [DisplayName("Deck Elements")]
        [Description("The current deck elements associated with the deck")]
        [Category("Deck Elements")]
        [Editor(typeof(CollectionEditor), typeof(UITypeEditor))]
        public Collection<IDeckElement> DeckElements => deckElements_;

        #endregion

        #region XML Serialization

        /// <summary>
        /// Gets the XML representation of the deck
        /// </summary>
        /// <returns>XML string representing the deck configuration</returns>
        public string GetXML()
        {
            using (var sw = new StringWriter())
            {
                using (var xmlWriter = new XmlTextWriter(sw))
                {
                    xmlWriter.Formatting = Formatting.None;
                    xmlWriter.WriteStartDocument();
                    WriteXML(xmlWriter);
                    xmlWriter.WriteEndDocument();
                }
                return sw.ToString();
            }
        }

        /// <summary>
        /// Sets the deck configuration from XML
        /// </summary>
        /// <param name="xml">XML string containing deck configuration</param>
        public void SetXML(string xml)
        {
            if (string.IsNullOrEmpty(xml))
                throw new ArgumentException("XML cannot be null or empty", nameof(xml));

            try
            {
                using (var sr = new StringReader(xml))
                {
                    using (var xmlReader = new XmlTextReader(sr))
                    {
                        ReadXML(xmlReader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to parse XML: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Writes the deck configuration to an XML writer
        /// </summary>
        /// <param name="xmlWriter">XML writer to write to</param>
        public void WriteXML(XmlWriter xmlWriter)
        {
            if (xmlWriter == null)
                throw new ArgumentNullException(nameof(xmlWriter));

            xmlWriter.WriteStartElement("Symyx.AutomationStudio.RobotDeck.XCMDeck");

            // Write deck properties
            xmlWriter.WriteElementString("Width", Width.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("Depth", Depth.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("LeftBorder", LeftBorder.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("RightBorder", RightBorder.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("FrontBorder", FrontBorder.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("BackBorder", BackBorder.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("UnitsWide", UnitsWide.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("TopShelfWidth", TopShelfWidth.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("BottomShelfWidth", BottomShelfWidth.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("ReferenceX", ReferenceX.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("ReferenceY", ReferenceY.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("StartingDeckElementPositionOffset", StartingDeckElementPositionOffset.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("ExtendedDeckElementPositionOffset", ExtendedDeckElementPositionOffset.ToString(CultureInfo.InvariantCulture));

            // Write deck elements
            if (DeckElements.Count > 0)
            {
                xmlWriter.WriteStartElement("Symyx.AutomationStudio.RobotDeck.DeckElements");
                foreach (var element in DeckElements)
                {
                    element.WriteXML(xmlWriter);
                }
                xmlWriter.WriteEndElement();
            }

            xmlWriter.WriteEndElement();
        }

        /// <summary>
        /// Reads the deck configuration from an XML reader
        /// </summary>
        /// <param name="xmlRd">XML reader to read from</param>
        public void ReadXML(XmlReader xmlRd)
        {
            if (xmlRd == null)
                throw new ArgumentNullException(nameof(xmlRd));

            try
            {
                // Clear existing elements
                DeckElements.Clear();

                while (xmlRd.Read())
                {
                    if (xmlRd.IsStartElement())
                    {
                        switch (xmlRd.Name)
                        {
                            case "Width":
                                if (double.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double width))
                                    Width = width;
                                break;

                            case "Depth":
                                if (double.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double depth))
                                    Depth = depth;
                                break;

                            case "LeftBorder":
                                if (double.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double leftBorder))
                                    LeftBorder = leftBorder;
                                break;

                            case "RightBorder":
                                if (double.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double rightBorder))
                                    RightBorder = rightBorder;
                                break;

                            case "FrontBorder":
                                if (double.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double frontBorder))
                                    FrontBorder = frontBorder;
                                break;

                            case "BackBorder":
                                if (double.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double backBorder))
                                    BackBorder = backBorder;
                                break;

                            case "UnitsWide":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int unitsWide))
                                    UnitsWide = unitsWide;
                                break;

                            case "TopShelfWidth":
                                if (double.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double topShelfWidth))
                                    TopShelfWidth = topShelfWidth;
                                break;

                            case "BottomShelfWidth":
                                if (double.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double bottomShelfWidth))
                                    BottomShelfWidth = bottomShelfWidth;
                                break;

                            case "ReferenceX":
                                if (double.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double referenceX))
                                    ReferenceX = referenceX;
                                break;

                            case "ReferenceY":
                                if (double.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double referenceY))
                                    ReferenceY = referenceY;
                                break;

                            case "StartingDeckElementPositionOffset":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int startingOffset))
                                    StartingDeckElementPositionOffset = startingOffset;
                                break;

                            case "ExtendedDeckElementPositionOffset":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int extendedOffset))
                                    ExtendedDeckElementPositionOffset = extendedOffset;
                                break;

                            case "Symyx.AutomationStudio.RobotDeck.DeckElements":
                                ReadDeckElements(xmlRd);
                                break;
                        }
                    }
                    else if (xmlRd.NodeType == XmlNodeType.EndElement && xmlRd.Name == "Symyx.AutomationStudio.RobotDeck.XCMDeck")
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to read XML: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Reads deck elements from XML
        /// </summary>
        /// <param name="xmlRd">XML reader positioned at DeckElements</param>
        private void ReadDeckElements(XmlReader xmlRd)
        {
            while (xmlRd.Read() && xmlRd.Name != "Symyx.AutomationStudio.RobotDeck.DeckElements")
            {
                if (xmlRd.IsStartElement())
                {
                    var element = new XCMElement();
                    element.ReadXML(xmlRd);
                    DeckElements.Add(element);
                }
            }
        }

        #endregion

        #region Validation and Utilities

        /// <summary>
        /// Validates the deck configuration
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            try
            {
                // Basic validation
                if (Width <= 0 || Depth <= 0 || UnitsWide <= 0)
                    return false;

                // Border validation
                if (LeftBorder + RightBorder >= Width)
                    return false;

                if (FrontBorder + BackBorder >= Depth)
                    return false;

                // Reference point validation
                if (ReferenceX < 0 || ReferenceX > Width)
                    return false;

                if (ReferenceY < 0 || ReferenceY > Depth)
                    return false;

                // Validate deck elements
                foreach (var element in DeckElements)
                {
                    if (element.StartUnit <= 0 || element.Width <= 0)
                        return false;

                    if (element.StartUnit + element.Width - 1 > UnitsWide)
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the usable deck area (excluding borders)
        /// </summary>
        /// <returns>Tuple containing usable width and depth</returns>
        public (double UsableWidth, double UsableDepth) GetUsableArea()
        {
            var usableWidth = Width - LeftBorder - RightBorder;
            var usableDepth = Depth - FrontBorder - BackBorder;
            return (Math.Max(0, usableWidth), Math.Max(0, usableDepth));
        }

        /// <summary>
        /// Gets the width of each deck unit
        /// </summary>
        /// <returns>Width per unit in millimeters</returns>
        public double GetUnitWidth()
        {
            var (usableWidth, _) = GetUsableArea();
            return UnitsWide > 0 ? usableWidth / UnitsWide : 0;
        }

        /// <summary>
        /// Calculates the physical position of a deck unit
        /// </summary>
        /// <param name="unitNumber">Unit number (1-based)</param>
        /// <returns>Tuple containing X and Y coordinates</returns>
        public (double X, double Y) GetUnitPosition(int unitNumber)
        {
            if (unitNumber < 1 || unitNumber > UnitsWide)
                throw new ArgumentOutOfRangeException(nameof(unitNumber), "Unit number must be between 1 and UnitsWide");

            var unitWidth = GetUnitWidth();
            var x = LeftBorder + (unitNumber - 1) * unitWidth + unitWidth / 2;
            var y = FrontBorder + Depth / 2;

            return (x, y);
        }

        /// <summary>
        /// Creates a deep copy of the deck
        /// </summary>
        /// <returns>Deep copy of the deck</returns>
        public XCMDeck Clone()
        {
            var xml = GetXML();
            var clone = new XCMDeck();
            clone.SetXML(xml);
            return clone;
        }

        /// <summary>
        /// Gets the depth of each shelf in millimeters
        /// </summary>
        /// <returns>Tuple containing top shelf depth and bottom shelf depth</returns>
        public (double TopShelfDepth, double BottomShelfDepth) GetShelfDepths()
        {
            var topShelfDepth = Depth * (TopShelfWidth / 100.0);
            var bottomShelfDepth = Depth * (BottomShelfWidth / 100.0);
            return (topShelfDepth, bottomShelfDepth);
        }

        /// <summary>
        /// Gets the center point of the deck
        /// </summary>
        /// <returns>Tuple containing center X and Y coordinates</returns>
        public (double CenterX, double CenterY) GetCenterPoint()
        {
            return (Width / 2, Depth / 2);
        }

        /// <summary>
        /// Gets the center point of the usable deck area (excluding borders)
        /// </summary>
        /// <returns>Tuple containing usable center X and Y coordinates</returns>
        public (double UsableCenterX, double UsableCenterY) GetUsableCenterPoint()
        {
            var (usableWidth, usableDepth) = GetUsableArea();
            return (LeftBorder + usableWidth / 2, FrontBorder + usableDepth / 2);
        }

        /// <summary>
        /// Checks if a point is within the deck boundaries
        /// </summary>
        /// <param name="x">X coordinate to test</param>
        /// <param name="y">Y coordinate to test</param>
        /// <returns>True if the point is within deck boundaries, false otherwise</returns>
        public bool ContainsPoint(double x, double y)
        {
            return x >= 0 && x <= Width && y >= 0 && y <= Depth;
        }

        /// <summary>
        /// Checks if a point is within the usable deck area (excluding borders)
        /// </summary>
        /// <param name="x">X coordinate to test</param>
        /// <param name="y">Y coordinate to test</param>
        /// <returns>True if the point is within usable area, false otherwise</returns>
        public bool ContainsPointInUsableArea(double x, double y)
        {
            return x >= LeftBorder && x <= Width - RightBorder &&
                   y >= FrontBorder && y <= Depth - BackBorder;
        }

        /// <summary>
        /// Gets the deck element at the specified unit position
        /// </summary>
        /// <param name="unitNumber">Unit number (1-based)</param>
        /// <returns>Deck element at the position, or null if none found</returns>
        public IDeckElement GetElementAtUnit(int unitNumber)
        {
            if (unitNumber < 1 || unitNumber > UnitsWide)
                return null;

            foreach (var element in DeckElements)
            {
                if (element.StartUnit <= unitNumber &&
                    unitNumber <= element.StartUnit + element.Width - 1)
                {
                    return element;
                }
            }

            return null;
        }

        /// <summary>
        /// Gets all deck elements that overlap with the specified unit range
        /// </summary>
        /// <param name="startUnit">Starting unit number (1-based)</param>
        /// <param name="endUnit">Ending unit number (1-based)</param>
        /// <returns>Collection of overlapping deck elements</returns>
        public Collection<IDeckElement> GetElementsInRange(int startUnit, int endUnit)
        {
            var elements = new Collection<IDeckElement>();

            if (startUnit < 1 || endUnit > UnitsWide || startUnit > endUnit)
                return elements;

            foreach (var element in DeckElements)
            {
                var elementStart = element.StartUnit;
                var elementEnd = element.StartUnit + element.Width - 1;

                // Check for overlap
                if (elementStart <= endUnit && elementEnd >= startUnit)
                {
                    elements.Add(element);
                }
            }

            return elements;
        }

        /// <summary>
        /// Checks if the specified unit range is available (no overlapping elements)
        /// </summary>
        /// <param name="startUnit">Starting unit number (1-based)</param>
        /// <param name="width">Width in units</param>
        /// <returns>True if the range is available, false otherwise</returns>
        public bool IsUnitRangeAvailable(int startUnit, int width)
        {
            if (startUnit < 1 || width < 1 || startUnit + width - 1 > UnitsWide)
                return false;

            var endUnit = startUnit + width - 1;
            var overlappingElements = GetElementsInRange(startUnit, endUnit);
            return overlappingElements.Count == 0;
        }

        /// <summary>
        /// Finds the next available unit range of the specified width
        /// </summary>
        /// <param name="width">Width in units required</param>
        /// <param name="startSearchFrom">Unit to start searching from (1-based)</param>
        /// <returns>Starting unit of available range, or -1 if none found</returns>
        public int FindNextAvailableUnitRange(int width, int startSearchFrom = 1)
        {
            if (width < 1 || startSearchFrom < 1)
                return -1;

            for (int unit = startSearchFrom; unit <= UnitsWide - width + 1; unit++)
            {
                if (IsUnitRangeAvailable(unit, width))
                {
                    return unit;
                }
            }

            return -1;
        }

        /// <summary>
        /// Gets the total number of occupied units
        /// </summary>
        /// <returns>Number of units occupied by deck elements</returns>
        public int GetOccupiedUnitsCount()
        {
            var occupiedUnits = new bool[UnitsWide];

            foreach (var element in DeckElements)
            {
                for (int i = element.StartUnit - 1; i < element.StartUnit + element.Width - 1 && i < UnitsWide; i++)
                {
                    occupiedUnits[i] = true;
                }
            }

            int count = 0;
            foreach (bool occupied in occupiedUnits)
            {
                if (occupied) count++;
            }

            return count;
        }

        /// <summary>
        /// Gets the percentage of deck utilization
        /// </summary>
        /// <returns>Percentage of deck units that are occupied (0-100)</returns>
        public double GetDeckUtilizationPercentage()
        {
            if (UnitsWide == 0) return 0;
            return (GetOccupiedUnitsCount() / (double)UnitsWide) * 100;
        }

        #endregion

        #region Element Management

        /// <summary>
        /// Adds a deck element to the deck if the position is available
        /// </summary>
        /// <param name="element">Deck element to add</param>
        /// <returns>True if element was added successfully, false if position is occupied</returns>
        public bool TryAddElement(IDeckElement element)
        {
            if (element == null)
                throw new ArgumentNullException(nameof(element));

            if (IsUnitRangeAvailable(element.StartUnit, element.Width))
            {
                DeckElements.Add(element);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes a deck element from the deck
        /// </summary>
        /// <param name="element">Deck element to remove</param>
        /// <returns>True if element was found and removed, false otherwise</returns>
        public bool RemoveElement(IDeckElement element)
        {
            if (element == null)
                return false;

            return DeckElements.Remove(element);
        }

        /// <summary>
        /// Removes the deck element at the specified unit position
        /// </summary>
        /// <param name="unitNumber">Unit number (1-based)</param>
        /// <returns>True if element was found and removed, false otherwise</returns>
        public bool RemoveElementAtUnit(int unitNumber)
        {
            var element = GetElementAtUnit(unitNumber);
            if (element != null)
            {
                return DeckElements.Remove(element);
            }

            return false;
        }

        /// <summary>
        /// Moves a deck element to a new position if available
        /// </summary>
        /// <param name="element">Deck element to move</param>
        /// <param name="newStartUnit">New starting unit position</param>
        /// <returns>True if element was moved successfully, false if new position is occupied</returns>
        public bool TryMoveElement(IDeckElement element, int newStartUnit)
        {
            if (element == null)
                throw new ArgumentNullException(nameof(element));

            if (!DeckElements.Contains(element))
                return false;

            // Temporarily remove element to check availability
            var originalStartUnit = element.StartUnit;
            DeckElements.Remove(element);

            if (IsUnitRangeAvailable(newStartUnit, element.Width))
            {
                element.StartUnit = newStartUnit;
                DeckElements.Add(element);
                return true;
            }
            else
            {
                // Restore original position
                element.StartUnit = originalStartUnit;
                DeckElements.Add(element);
                return false;
            }
        }

        /// <summary>
        /// Gets all available unit ranges of the specified width
        /// </summary>
        /// <param name="width">Width in units required</param>
        /// <returns>Collection of starting unit positions for available ranges</returns>
        public Collection<int> GetAvailableUnitRanges(int width)
        {
            var availableRanges = new Collection<int>();

            if (width < 1)
                return availableRanges;

            for (int unit = 1; unit <= UnitsWide - width + 1; unit++)
            {
                if (IsUnitRangeAvailable(unit, width))
                {
                    availableRanges.Add(unit);
                }
            }

            return availableRanges;
        }

        /// <summary>
        /// Optimizes deck layout by moving elements to minimize gaps
        /// </summary>
        /// <param name="leftAlign">If true, align elements to the left; if false, distribute evenly</param>
        public void OptimizeDeckLayout(bool leftAlign = true)
        {
            if (DeckElements.Count == 0)
                return;

            // Sort elements by current position
            var sortedElements = DeckElements.OrderBy(e => e.StartUnit).ToList();

            // Clear current positions
            DeckElements.Clear();

            if (leftAlign)
            {
                // Pack elements to the left
                int currentPosition = 1;
                foreach (var element in sortedElements)
                {
                    element.StartUnit = currentPosition;
                    DeckElements.Add(element);
                    currentPosition += element.Width;
                }
            }
            else
            {
                // Distribute elements evenly
                var totalElementWidth = sortedElements.Sum(e => e.Width);
                var availableSpace = UnitsWide - totalElementWidth;
                var gaps = sortedElements.Count + 1;
                var gapSize = gaps > 0 ? availableSpace / gaps : 0;

                int currentPosition = (int)Math.Max(1, gapSize);
                foreach (var element in sortedElements)
                {
                    element.StartUnit = currentPosition;
                    DeckElements.Add(element);
                    currentPosition += element.Width + (int)gapSize;
                }
            }
        }

        #endregion

        #region Advanced Utilities

        /// <summary>
        /// Gets detailed deck statistics
        /// </summary>
        /// <returns>Comprehensive deck statistics</returns>
        public DeckStatistics GetDeckStatistics()
        {
            return new DeckStatistics
            {
                TotalUnits = UnitsWide,
                OccupiedUnits = GetOccupiedUnitsCount(),
                AvailableUnits = UnitsWide - GetOccupiedUnitsCount(),
                UtilizationPercentage = GetDeckUtilizationPercentage(),
                ElementCount = DeckElements.Count,
                TotalSubElementCount = DeckElements.Sum(e => e.DeckSubElements.Count),
                UsableArea = GetUsableArea(),
                TotalArea = (Width, Depth),
                LargestAvailableRange = GetLargestAvailableRange()
            };
        }

        /// <summary>
        /// Gets the largest available contiguous unit range
        /// </summary>
        /// <returns>Size of the largest available range</returns>
        public int GetLargestAvailableRange()
        {
            var occupiedUnits = new bool[UnitsWide];

            foreach (var element in DeckElements)
            {
                for (int i = element.StartUnit - 1; i < element.StartUnit + element.Width - 1 && i < UnitsWide; i++)
                {
                    occupiedUnits[i] = true;
                }
            }

            int maxRange = 0;
            int currentRange = 0;

            for (int i = 0; i < UnitsWide; i++)
            {
                if (!occupiedUnits[i])
                {
                    currentRange++;
                    maxRange = Math.Max(maxRange, currentRange);
                }
                else
                {
                    currentRange = 0;
                }
            }

            return maxRange;
        }

        /// <summary>
        /// Validates deck element overlaps and returns any conflicts
        /// </summary>
        /// <returns>Collection of overlapping element pairs</returns>
        public Collection<(IDeckElement Element1, IDeckElement Element2)> GetElementOverlaps()
        {
            var overlaps = new Collection<(IDeckElement, IDeckElement)>();

            for (int i = 0; i < DeckElements.Count; i++)
            {
                for (int j = i + 1; j < DeckElements.Count; j++)
                {
                    var element1 = DeckElements[i];
                    var element2 = DeckElements[j];

                    var element1End = element1.StartUnit + element1.Width - 1;
                    var element2End = element2.StartUnit + element2.Width - 1;

                    // Check for overlap
                    if (element1.StartUnit <= element2End && element2.StartUnit <= element1End)
                    {
                        overlaps.Add((element1, element2));
                    }
                }
            }

            return overlaps;
        }

        /// <summary>
        /// Checks if the deck configuration has any element overlaps
        /// </summary>
        /// <returns>True if there are overlaps, false otherwise</returns>
        public bool HasElementOverlaps()
        {
            return GetElementOverlaps().Count > 0;
        }

        /// <summary>
        /// Resets the deck to default configuration
        /// </summary>
        public void ResetToDefaults()
        {
            Width = 840;
            Depth = 540;
            UnitsWide = 12;
            LeftBorder = 105;
            RightBorder = 105;
            FrontBorder = 67.5;
            BackBorder = 67.5;
            TopShelfWidth = 15;
            BottomShelfWidth = 15;
            ReferenceX = 420;
            ReferenceY = 270;
            StartingDeckElementPositionOffset = 0;
            ExtendedDeckElementPositionOffset = 0;
            DeckElements.Clear();
        }

        #endregion

        #region Object Overrides

        /// <summary>
        /// Returns a string representation of the deck
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"XCMDeck: {Width}x{Depth}mm, {UnitsWide} units, {DeckElements.Count} elements";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">Object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object obj)
        {
            if (obj is XCMDeck other)
            {
                return Math.Abs(Width - other.Width) < 0.001 &&
                       Math.Abs(Depth - other.Depth) < 0.001 &&
                       UnitsWide == other.UnitsWide &&
                       DeckElements.Count == other.DeckElements.Count;
            }
            return false;
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>Hash code for the current object</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(Width, Depth, UnitsWide, DeckElements.Count);
        }

        #endregion
    }
}
