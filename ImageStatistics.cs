using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// Represents comprehensive statistical information about an epoch image
    /// Contains color analysis, distribution metrics, and quality measurements
    /// </summary>
    public class ImageStatistics
    {
        #region Constructors

        /// <summary>
        /// Initializes a new instance of the ImageStatistics class
        /// </summary>
        public ImageStatistics()
        {
            AnalysisTimestamp = DateTime.Now;
            StatisticsId = Guid.NewGuid();
            ColorDistribution = new Dictionary<Color, int>();
            ChannelHistograms = new Dictionary<string, int[]>();
            QualityMetrics = new Dictionary<string, double>();
        }

        /// <summary>
        /// Initializes a new instance of the ImageStatistics class with basic color information
        /// </summary>
        /// <param name="mean">Mean color</param>
        /// <param name="pixelCount">Total pixel count</param>
        public ImageStatistics(Color mean, long pixelCount) : this()
        {
            Mean = mean;
            PixelCount = pixelCount;
        }

        #endregion

        #region Core Statistical Properties

        /// <summary>
        /// Gets or sets the mean (average) color of the image
        /// </summary>
        public Color Mean { get; set; }

        /// <summary>
        /// Gets or sets the standard deviation of pixel values
        /// </summary>
        public double StandardDeviation { get; set; }

        /// <summary>
        /// Gets or sets the minimum color values found in the image
        /// </summary>
        public Color Minimum { get; set; }

        /// <summary>
        /// Gets or sets the maximum color values found in the image
        /// </summary>
        public Color Maximum { get; set; }

        /// <summary>
        /// Gets or sets the median color values of the image
        /// </summary>
        public Color Median { get; set; }

        /// <summary>
        /// Gets or sets the total number of pixels in the image
        /// </summary>
        public long PixelCount { get; set; }

        /// <summary>
        /// Gets or sets the average brightness of the image (0-255)
        /// </summary>
        public double BrightnessAverage { get; set; }

        /// <summary>
        /// Gets or sets the contrast measure of the image
        /// </summary>
        public double Contrast { get; set; }

        /// <summary>
        /// Gets or sets the entropy (information content) of the image
        /// </summary>
        public double Entropy { get; set; }

        #endregion

        #region Advanced Statistical Properties

        /// <summary>
        /// Gets or sets the variance of pixel values
        /// </summary>
        public double Variance { get; set; }

        /// <summary>
        /// Gets or sets the skewness of the pixel distribution
        /// </summary>
        public double Skewness { get; set; }

        /// <summary>
        /// Gets or sets the kurtosis of the pixel distribution
        /// </summary>
        public double Kurtosis { get; set; }

        /// <summary>
        /// Gets or sets the range of pixel values (max - min)
        /// </summary>
        public Color Range { get; set; }

        /// <summary>
        /// Gets or sets the mode (most frequent) color in the image
        /// </summary>
        public Color Mode { get; set; }

        /// <summary>
        /// Gets or sets the frequency of the mode color
        /// </summary>
        public int ModeFrequency { get; set; }

        /// <summary>
        /// Gets or sets the number of unique colors in the image
        /// </summary>
        public int UniqueColorCount { get; set; }

        #endregion

        #region Color Channel Statistics

        /// <summary>
        /// Gets or sets the red channel statistics
        /// </summary>
        public ChannelStatistics RedChannel { get; set; } = new ChannelStatistics();

        /// <summary>
        /// Gets or sets the green channel statistics
        /// </summary>
        public ChannelStatistics GreenChannel { get; set; } = new ChannelStatistics();

        /// <summary>
        /// Gets or sets the blue channel statistics
        /// </summary>
        public ChannelStatistics BlueChannel { get; set; } = new ChannelStatistics();

        /// <summary>
        /// Gets or sets the alpha channel statistics
        /// </summary>
        public ChannelStatistics AlphaChannel { get; set; } = new ChannelStatistics();

        /// <summary>
        /// Gets or sets the luminance (brightness) channel statistics
        /// </summary>
        public ChannelStatistics LuminanceChannel { get; set; } = new ChannelStatistics();

        /// <summary>
        /// Gets or sets the saturation channel statistics
        /// </summary>
        public ChannelStatistics SaturationChannel { get; set; } = new ChannelStatistics();

        /// <summary>
        /// Gets or sets the hue channel statistics
        /// </summary>
        public ChannelStatistics HueChannel { get; set; } = new ChannelStatistics();

        #endregion

        #region Quality and Analysis Metrics

        /// <summary>
        /// Gets or sets the perceived image quality score (0-100)
        /// </summary>
        public double QualityScore { get; set; }

        /// <summary>
        /// Gets or sets the noise level estimate
        /// </summary>
        public double NoiseLevel { get; set; }

        /// <summary>
        /// Gets or sets the sharpness measure
        /// </summary>
        public double Sharpness { get; set; }

        /// <summary>
        /// Gets or sets the blur estimate
        /// </summary>
        public double BlurEstimate { get; set; }

        /// <summary>
        /// Gets or sets the edge density (edges per unit area)
        /// </summary>
        public double EdgeDensity { get; set; }

        /// <summary>
        /// Gets or sets the texture complexity measure
        /// </summary>
        public double TextureComplexity { get; set; }

        /// <summary>
        /// Gets or sets the color diversity index
        /// </summary>
        public double ColorDiversity { get; set; }

        /// <summary>
        /// Gets or sets the dynamic range of the image
        /// </summary>
        public double DynamicRange { get; set; }

        #endregion

        #region Histogram and Distribution Data

        /// <summary>
        /// Gets or sets the color distribution (color -> frequency)
        /// </summary>
        public Dictionary<Color, int> ColorDistribution { get; set; }

        /// <summary>
        /// Gets or sets the channel histograms (channel name -> histogram array)
        /// </summary>
        public Dictionary<string, int[]> ChannelHistograms { get; set; }

        /// <summary>
        /// Gets or sets additional quality metrics
        /// </summary>
        public Dictionary<string, double> QualityMetrics { get; set; }

        #endregion

        #region Metadata Properties

        /// <summary>
        /// Gets or sets the unique identifier for this statistics analysis
        /// </summary>
        public Guid StatisticsId { get; set; }

        /// <summary>
        /// Gets or sets the timestamp when the analysis was performed
        /// </summary>
        public DateTime AnalysisTimestamp { get; set; }

        /// <summary>
        /// Gets or sets the time taken to perform the analysis
        /// </summary>
        public TimeSpan AnalysisDuration { get; set; }

        /// <summary>
        /// Gets or sets the analysis algorithm used
        /// </summary>
        public string AnalysisAlgorithm { get; set; }

        /// <summary>
        /// Gets or sets the analysis quality level
        /// </summary>
        public AnalysisQuality QualityLevel { get; set; }

        /// <summary>
        /// Gets or sets the image dimensions analyzed
        /// </summary>
        public Size ImageDimensions { get; set; }

        /// <summary>
        /// Gets or sets the color space used for analysis
        /// </summary>
        public string ColorSpace { get; set; } = "RGB";

        /// <summary>
        /// Gets or sets additional analysis metadata
        /// </summary>
        public Dictionary<string, object> AnalysisMetadata { get; set; } = new Dictionary<string, object>();

        #endregion

        #region Computed Properties

        /// <summary>
        /// Gets the aspect ratio of the analyzed image
        /// </summary>
        public double AspectRatio
        {
            get
            {
                return ImageDimensions.Height != 0 ? (double)ImageDimensions.Width / ImageDimensions.Height : 0;
            }
        }

        /// <summary>
        /// Gets the total image area in pixels
        /// </summary>
        public long ImageArea => ImageDimensions.Width * ImageDimensions.Height;

        /// <summary>
        /// Gets the brightness classification
        /// </summary>
        public BrightnessClassification BrightnessClass
        {
            get
            {
                return BrightnessAverage switch
                {
                    < 50 => BrightnessClassification.Dark,
                    < 100 => BrightnessClassification.Dim,
                    < 150 => BrightnessClassification.Medium,
                    < 200 => BrightnessClassification.Bright,
                    _ => BrightnessClassification.VeryBright
                };
            }
        }

        /// <summary>
        /// Gets the contrast classification
        /// </summary>
        public ContrastClassification ContrastClass
        {
            get
            {
                return Contrast switch
                {
                    < 20 => ContrastClassification.VeryLow,
                    < 40 => ContrastClassification.Low,
                    < 60 => ContrastClassification.Medium,
                    < 80 => ContrastClassification.High,
                    _ => ContrastClassification.VeryHigh
                };
            }
        }

        /// <summary>
        /// Gets the color richness based on unique color count
        /// </summary>
        public ColorRichness ColorRichnessLevel
        {
            get
            {
                var ratio = (double)UniqueColorCount / PixelCount;
                return ratio switch
                {
                    < 0.1 => ColorRichness.VeryLow,
                    < 0.3 => ColorRichness.Low,
                    < 0.6 => ColorRichness.Medium,
                    < 0.8 => ColorRichness.High,
                    _ => ColorRichness.VeryHigh
                };
            }
        }

        /// <summary>
        /// Gets whether the image is predominantly grayscale
        /// </summary>
        public bool IsGrayscale
        {
            get
            {
                var redGreenDiff = Math.Abs(RedChannel.Mean - GreenChannel.Mean);
                var redBlueDiff = Math.Abs(RedChannel.Mean - BlueChannel.Mean);
                var greenBlueDiff = Math.Abs(GreenChannel.Mean - BlueChannel.Mean);
                
                return redGreenDiff < 5 && redBlueDiff < 5 && greenBlueDiff < 5;
            }
        }

        /// <summary>
        /// Gets the dominant color channel
        /// </summary>
        public string DominantChannel
        {
            get
            {
                var maxMean = Math.Max(Math.Max(RedChannel.Mean, GreenChannel.Mean), BlueChannel.Mean);
                
                if (Math.Abs(RedChannel.Mean - maxMean) < 0.001) return "Red";
                if (Math.Abs(GreenChannel.Mean - maxMean) < 0.001) return "Green";
                if (Math.Abs(BlueChannel.Mean - maxMean) < 0.001) return "Blue";
                
                return "Balanced";
            }
        }

        #endregion

        #region Methods

        /// <summary>
        /// Adds a color to the distribution
        /// </summary>
        /// <param name="color">The color to add</param>
        /// <param name="frequency">The frequency of the color</param>
        public void AddColorToDistribution(Color color, int frequency = 1)
        {
            if (ColorDistribution.ContainsKey(color))
            {
                ColorDistribution[color] += frequency;
            }
            else
            {
                ColorDistribution[color] = frequency;
            }
        }

        /// <summary>
        /// Gets the most frequent colors in the image
        /// </summary>
        /// <param name="count">Number of top colors to return</param>
        /// <returns>List of colors ordered by frequency</returns>
        public List<(Color Color, int Frequency)> GetTopColors(int count = 10)
        {
            return ColorDistribution
                .OrderByDescending(kvp => kvp.Value)
                .Take(count)
                .Select(kvp => (kvp.Key, kvp.Value))
                .ToList();
        }

        /// <summary>
        /// Gets the histogram for a specific channel
        /// </summary>
        /// <param name="channelName">Name of the channel (Red, Green, Blue, etc.)</param>
        /// <returns>Histogram array or null if not found</returns>
        public int[] GetChannelHistogram(string channelName)
        {
            return ChannelHistograms.TryGetValue(channelName, out var histogram) ? histogram : null;
        }

        /// <summary>
        /// Sets the histogram for a specific channel
        /// </summary>
        /// <param name="channelName">Name of the channel</param>
        /// <param name="histogram">Histogram data</param>
        public void SetChannelHistogram(string channelName, int[] histogram)
        {
            if (!string.IsNullOrEmpty(channelName) && histogram != null)
            {
                ChannelHistograms[channelName] = histogram;
            }
        }

        /// <summary>
        /// Calculates derived statistics from basic values
        /// </summary>
        public void CalculateDerivedStatistics()
        {
            // Calculate range
            Range = Color.FromArgb(
                Maximum.R - Minimum.R,
                Maximum.G - Minimum.G,
                Maximum.B - Minimum.B);

            // Calculate variance from standard deviation
            Variance = StandardDeviation * StandardDeviation;

            // Calculate dynamic range
            var maxLuminance = Math.Max(Math.Max(Maximum.R, Maximum.G), Maximum.B);
            var minLuminance = Math.Min(Math.Min(Minimum.R, Minimum.G), Minimum.B);
            DynamicRange = maxLuminance > 0 ? (double)maxLuminance / Math.Max(1, minLuminance) : 0;

            // Update unique color count
            UniqueColorCount = ColorDistribution.Count;

            // Find mode
            if (ColorDistribution.Count > 0)
            {
                var modeEntry = ColorDistribution.OrderByDescending(kvp => kvp.Value).First();
                Mode = modeEntry.Key;
                ModeFrequency = modeEntry.Value;
            }
        }

        /// <summary>
        /// Gets a summary of the image statistics
        /// </summary>
        /// <returns>A formatted summary string</returns>
        public string GetSummary()
        {
            var summary = new StringBuilder();
            summary.AppendLine($"Image Statistics Summary");
            summary.AppendLine($"Analysis ID: {StatisticsId}");
            summary.AppendLine($"Timestamp: {AnalysisTimestamp:yyyy-MM-dd HH:mm:ss}");
            summary.AppendLine($"Duration: {AnalysisDuration.TotalMilliseconds:F0}ms");
            summary.AppendLine();

            summary.AppendLine($"Image Dimensions: {ImageDimensions.Width}x{ImageDimensions.Height}");
            summary.AppendLine($"Pixel Count: {PixelCount:N0}");
            summary.AppendLine($"Aspect Ratio: {AspectRatio:F2}");
            summary.AppendLine();

            summary.AppendLine($"Color Analysis:");
            summary.AppendLine($"  Mean Color: R={Mean.R}, G={Mean.G}, B={Mean.B}");
            summary.AppendLine($"  Brightness: {BrightnessAverage:F1} ({BrightnessClass})");
            summary.AppendLine($"  Contrast: {Contrast:F1} ({ContrastClass})");
            summary.AppendLine($"  Unique Colors: {UniqueColorCount:N0} ({ColorRichnessLevel})");
            summary.AppendLine($"  Dominant Channel: {DominantChannel}");
            summary.AppendLine($"  Is Grayscale: {IsGrayscale}");
            summary.AppendLine();

            summary.AppendLine($"Quality Metrics:");
            summary.AppendLine($"  Quality Score: {QualityScore:F1}");
            summary.AppendLine($"  Sharpness: {Sharpness:F2}");
            summary.AppendLine($"  Noise Level: {NoiseLevel:F2}");
            summary.AppendLine($"  Edge Density: {EdgeDensity:F2}");
            summary.AppendLine($"  Texture Complexity: {TextureComplexity:F2}");

            return summary.ToString();
        }

        /// <summary>
        /// Gets a detailed report of all statistics
        /// </summary>
        /// <returns>A formatted detailed report string</returns>
        public string GetDetailedReport()
        {
            var report = new StringBuilder();
            report.AppendLine(GetSummary());
            report.AppendLine();

            report.AppendLine("Detailed Statistics:");
            report.AppendLine($"  Standard Deviation: {StandardDeviation:F4}");
            report.AppendLine($"  Variance: {Variance:F4}");
            report.AppendLine($"  Skewness: {Skewness:F4}");
            report.AppendLine($"  Kurtosis: {Kurtosis:F4}");
            report.AppendLine($"  Entropy: {Entropy:F4}");
            report.AppendLine($"  Dynamic Range: {DynamicRange:F2}");
            report.AppendLine();

            report.AppendLine("Channel Statistics:");
            report.AppendLine($"  Red   - Mean: {RedChannel.Mean:F1}, StdDev: {RedChannel.StandardDeviation:F2}");
            report.AppendLine($"  Green - Mean: {GreenChannel.Mean:F1}, StdDev: {GreenChannel.StandardDeviation:F2}");
            report.AppendLine($"  Blue  - Mean: {BlueChannel.Mean:F1}, StdDev: {BlueChannel.StandardDeviation:F2}");
            report.AppendLine($"  Alpha - Mean: {AlphaChannel.Mean:F1}, StdDev: {AlphaChannel.StandardDeviation:F2}");
            report.AppendLine();

            if (ColorDistribution.Count > 0)
            {
                var topColors = GetTopColors(5);
                report.AppendLine("Top Colors:");
                foreach (var (color, frequency) in topColors)
                {
                    var percentage = (double)frequency / PixelCount * 100;
                    report.AppendLine($"  RGB({color.R},{color.G},{color.B}): {frequency:N0} pixels ({percentage:F1}%)");
                }
            }

            return report.ToString();
        }

        /// <summary>
        /// Returns a string representation of the image statistics
        /// </summary>
        /// <returns>A summary string</returns>
        public override string ToString()
        {
            return $"ImageStats: {ImageDimensions.Width}x{ImageDimensions.Height}, " +
                   $"Brightness: {BrightnessAverage:F0}, Contrast: {Contrast:F0}, " +
                   $"Colors: {UniqueColorCount:N0}, Quality: {QualityScore:F0}";
        }

        #endregion
    }

    /// <summary>
    /// Represents statistics for a single color channel
    /// </summary>
    public class ChannelStatistics
    {
        /// <summary>
        /// Gets or sets the mean value of the channel
        /// </summary>
        public double Mean { get; set; }

        /// <summary>
        /// Gets or sets the standard deviation of the channel
        /// </summary>
        public double StandardDeviation { get; set; }

        /// <summary>
        /// Gets or sets the minimum value in the channel
        /// </summary>
        public int Minimum { get; set; }

        /// <summary>
        /// Gets or sets the maximum value in the channel
        /// </summary>
        public int Maximum { get; set; }

        /// <summary>
        /// Gets or sets the median value of the channel
        /// </summary>
        public double Median { get; set; }

        /// <summary>
        /// Gets or sets the mode (most frequent value) of the channel
        /// </summary>
        public int Mode { get; set; }

        /// <summary>
        /// Gets or sets the variance of the channel
        /// </summary>
        public double Variance { get; set; }

        /// <summary>
        /// Gets the range of values in the channel
        /// </summary>
        public int Range => Maximum - Minimum;
    }

    #region Enumerations

    /// <summary>
    /// Enumeration of analysis quality levels
    /// </summary>
    public enum AnalysisQuality
    {
        /// <summary>
        /// Basic analysis with core metrics only
        /// </summary>
        Basic = 0,

        /// <summary>
        /// Standard analysis with common metrics
        /// </summary>
        Standard = 1,

        /// <summary>
        /// Detailed analysis with advanced metrics
        /// </summary>
        Detailed = 2,

        /// <summary>
        /// Comprehensive analysis with all available metrics
        /// </summary>
        Comprehensive = 3
    }

    /// <summary>
    /// Enumeration of brightness classifications
    /// </summary>
    public enum BrightnessClassification
    {
        /// <summary>
        /// Very dark image (0-49)
        /// </summary>
        Dark = 0,

        /// <summary>
        /// Dim image (50-99)
        /// </summary>
        Dim = 1,

        /// <summary>
        /// Medium brightness (100-149)
        /// </summary>
        Medium = 2,

        /// <summary>
        /// Bright image (150-199)
        /// </summary>
        Bright = 3,

        /// <summary>
        /// Very bright image (200-255)
        /// </summary>
        VeryBright = 4
    }

    /// <summary>
    /// Enumeration of contrast classifications
    /// </summary>
    public enum ContrastClassification
    {
        /// <summary>
        /// Very low contrast (0-19)
        /// </summary>
        VeryLow = 0,

        /// <summary>
        /// Low contrast (20-39)
        /// </summary>
        Low = 1,

        /// <summary>
        /// Medium contrast (40-59)
        /// </summary>
        Medium = 2,

        /// <summary>
        /// High contrast (60-79)
        /// </summary>
        High = 3,

        /// <summary>
        /// Very high contrast (80+)
        /// </summary>
        VeryHigh = 4
    }

    /// <summary>
    /// Enumeration of color richness levels
    /// </summary>
    public enum ColorRichness
    {
        /// <summary>
        /// Very few unique colors
        /// </summary>
        VeryLow = 0,

        /// <summary>
        /// Limited color palette
        /// </summary>
        Low = 1,

        /// <summary>
        /// Moderate color variety
        /// </summary>
        Medium = 2,

        /// <summary>
        /// Rich color palette
        /// </summary>
        High = 3,

        /// <summary>
        /// Very diverse colors
        /// </summary>
        VeryHigh = 4
    }

    #endregion
}
