using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using FS.AS.Core.Imaging;

namespace FS.AS.Core.Imaging.Examples
{
    /// <summary>
    /// Example usage of the EpochImage implementation
    /// </summary>
    public class EpochImageUsageExample
    {
        public static async Task RunExamples()
        {
            Console.WriteLine("EpochImage .NET Implementation Examples");
            Console.WriteLine("=====================================");

            // Example 1: Create an EpochImage from a file
            await Example1_LoadFromFile();

            // Example 2: Create an EpochImage from byte array
            await Example2_LoadFromBytes();

            // Example 3: Image transformations
            await Example3_ImageTransformations();

            // Example 4: Image analysis
            await Example4_ImageAnalysis();

            // Example 5: Image comparison
            await Example5_ImageComparison();

            // Example 6: Metadata management
            await Example6_MetadataManagement();

            // Example 7: Export and save operations
            await Example7_ExportAndSave();
        }

        private static async Task Example1_LoadFromFile()
        {
            Console.WriteLine("\n1. Loading EpochImage from File");
            Console.WriteLine("-------------------------------");

            try
            {
                // Create an EpochImage with a timestamp and file path
                var timestamp = DateTime.Now;
                var epochImage = new EpochImage(timestamp, @"C:\Images\sample.jpg");

                // Load the image asynchronously
                bool loaded = await epochImage.LoadAsync();
                
                if (loaded)
                {
                    Console.WriteLine($"Image loaded successfully!");
                    Console.WriteLine($"ID: {epochImage.Id}");
                    Console.WriteLine($"Timestamp: {epochImage.Timestamp}");
                    Console.WriteLine($"Format: {epochImage.Format}");
                    Console.WriteLine($"Size: {epochImage.Width}x{epochImage.Height}");
                    Console.WriteLine($"File Size: {epochImage.Size} bytes");
                }
                else
                {
                    Console.WriteLine("Failed to load image from file.");
                }

                // Clean up
                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        private static async Task Example2_LoadFromBytes()
        {
            Console.WriteLine("\n2. Loading EpochImage from Byte Array");
            Console.WriteLine("------------------------------------");

            try
            {
                // Simulate loading image data from a byte array
                // In real scenario, this could come from a database, network, etc.
                byte[] imageData = File.ReadAllBytes(@"C:\Images\sample.jpg");
                
                var timestamp = DateTime.Now;
                var epochImage = new EpochImage(timestamp, imageData);

                Console.WriteLine($"Image created from byte array!");
                Console.WriteLine($"ID: {epochImage.Id}");
                Console.WriteLine($"Timestamp: {epochImage.Timestamp}");
                Console.WriteLine($"Is Loaded: {epochImage.IsLoaded}");
                Console.WriteLine($"Size: {epochImage.Width}x{epochImage.Height}");

                // Clean up
                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        private static async Task Example3_ImageTransformations()
        {
            Console.WriteLine("\n3. Image Transformations");
            Console.WriteLine("------------------------");

            try
            {
                var timestamp = DateTime.Now;
                var originalImage = new EpochImage(timestamp, @"C:\Images\sample.jpg");
                
                if (await originalImage.LoadAsync())
                {
                    Console.WriteLine($"Original image: {originalImage.Width}x{originalImage.Height}");

                    // Resize the image
                    var resizedImage = originalImage.Resize(800, 600);
                    if (resizedImage != null)
                    {
                        Console.WriteLine($"Resized image: {resizedImage.Width}x{resizedImage.Height}");
                        resizedImage.Dispose();
                    }

                    // Convert to grayscale
                    var grayscaleImage = originalImage.ToGrayscale();
                    if (grayscaleImage != null)
                    {
                        Console.WriteLine("Converted to grayscale");
                        grayscaleImage.Dispose();
                    }

                    // Adjust brightness
                    var brighterImage = originalImage.AdjustBrightness(20);
                    if (brighterImage != null)
                    {
                        Console.WriteLine("Brightness adjusted (+20)");
                        brighterImage.Dispose();
                    }

                    // Flip horizontally
                    var flippedImage = originalImage.FlipHorizontal();
                    if (flippedImage != null)
                    {
                        Console.WriteLine("Flipped horizontally");
                        flippedImage.Dispose();
                    }

                    // Rotate 90 degrees
                    var rotatedImage = originalImage.Rotate(90);
                    if (rotatedImage != null)
                    {
                        Console.WriteLine("Rotated 90 degrees");
                        rotatedImage.Dispose();
                    }

                    // Crop a section
                    var cropRect = new Rectangle(100, 100, 200, 200);
                    var croppedImage = originalImage.Crop(cropRect);
                    if (croppedImage != null)
                    {
                        Console.WriteLine($"Cropped to: {croppedImage.Width}x{croppedImage.Height}");
                        croppedImage.Dispose();
                    }
                }

                originalImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        private static async Task Example4_ImageAnalysis()
        {
            Console.WriteLine("\n4. Image Analysis");
            Console.WriteLine("-----------------");

            try
            {
                var timestamp = DateTime.Now;
                var epochImage = new EpochImage(timestamp, @"C:\Images\sample.jpg");
                
                if (await epochImage.LoadAsync())
                {
                    // Get image statistics
                    var stats = epochImage.GetStatistics();
                    if (stats != null)
                    {
                        Console.WriteLine($"Image Statistics:");
                        Console.WriteLine($"  Mean Color: R={stats.Mean.R}, G={stats.Mean.G}, B={stats.Mean.B}");
                        Console.WriteLine($"  Pixel Count: {stats.PixelCount}");
                        Console.WriteLine($"  Brightness Average: {stats.BrightnessAverage:F2}");
                    }

                    // Get histogram
                    var histogram = epochImage.GetHistogram();
                    if (histogram != null)
                    {
                        Console.WriteLine($"Histogram generated with {histogram.Length} bins");
                        Console.WriteLine($"  Darkest pixels (0-50): {histogram.Take(51).Sum()}");
                        Console.WriteLine($"  Mid-tone pixels (51-200): {histogram.Skip(51).Take(150).Sum()}");
                        Console.WriteLine($"  Brightest pixels (201-255): {histogram.Skip(201).Sum()}");
                    }

                    // Generate thumbnail
                    var thumbnail = epochImage.GetThumbnail(150, 150);
                    if (thumbnail != null)
                    {
                        Console.WriteLine($"Thumbnail generated: {thumbnail.Width}x{thumbnail.Height}");
                        thumbnail.Dispose();
                    }

                    // Validate image
                    bool isValid = epochImage.Validate();
                    Console.WriteLine($"Image validation: {(isValid ? "PASSED" : "FAILED")}");
                }

                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        private static async Task Example5_ImageComparison()
        {
            Console.WriteLine("\n5. Image Comparison");
            Console.WriteLine("-------------------");

            try
            {
                var timestamp1 = DateTime.Now;
                var timestamp2 = DateTime.Now.AddMinutes(1);
                
                var image1 = new EpochImage(timestamp1, @"C:\Images\sample1.jpg");
                var image2 = new EpochImage(timestamp2, @"C:\Images\sample2.jpg");
                
                if (await image1.LoadAsync() && await image2.LoadAsync())
                {
                    // Compare the two images
                    var comparison = image1.Compare(image2);
                    
                    Console.WriteLine($"Image Comparison Results:");
                    Console.WriteLine($"  Similarity: {comparison.SimilarityPercentage:F2}%");
                    Console.WriteLine($"  Are Identical: {comparison.AreIdentical}");
                    Console.WriteLine($"  MSE: {comparison.MeanSquaredError:F2}");
                    Console.WriteLine($"  PSNR: {comparison.PeakSignalToNoiseRatio:F2}");
                }

                image1.Dispose();
                image2.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        private static async Task Example6_MetadataManagement()
        {
            Console.WriteLine("\n6. Metadata Management");
            Console.WriteLine("----------------------");

            try
            {
                var timestamp = DateTime.Now;
                var epochImage = new EpochImage(timestamp, @"C:\Images\sample.jpg");
                
                if (await epochImage.LoadAsync())
                {
                    // Set basic properties
                    epochImage.Name = "Sample Image";
                    epochImage.Description = "This is a sample image for testing";
                    epochImage.Quality = 85;

                    // Add metadata
                    epochImage.Metadata["Camera"] = "Canon EOS R5";
                    epochImage.Metadata["ISO"] = 400;
                    epochImage.Metadata["Aperture"] = "f/2.8";
                    epochImage.Metadata["ShutterSpeed"] = "1/250";
                    epochImage.Metadata["Location"] = "New York, NY";

                    // Add tags
                    epochImage.Tags.Add("landscape");
                    epochImage.Tags.Add("outdoor");
                    epochImage.Tags.Add("nature");

                    Console.WriteLine($"Image Properties:");
                    Console.WriteLine($"  Name: {epochImage.Name}");
                    Console.WriteLine($"  Description: {epochImage.Description}");
                    Console.WriteLine($"  Quality: {epochImage.Quality}");
                    Console.WriteLine($"  Creation Time: {epochImage.CreationTime}");
                    Console.WriteLine($"  Last Modified: {epochImage.LastModifiedTime}");
                    Console.WriteLine($"  Is Modified: {epochImage.IsModified}");

                    Console.WriteLine($"\nMetadata:");
                    foreach (var kvp in epochImage.Metadata)
                    {
                        Console.WriteLine($"  {kvp.Key}: {kvp.Value}");
                    }

                    Console.WriteLine($"\nTags: {string.Join(", ", epochImage.Tags)}");
                }

                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        private static async Task Example7_ExportAndSave()
        {
            Console.WriteLine("\n7. Export and Save Operations");
            Console.WriteLine("-----------------------------");

            try
            {
                var timestamp = DateTime.Now;
                var epochImage = new EpochImage(timestamp, @"C:\Images\sample.jpg");
                
                if (await epochImage.LoadAsync())
                {
                    // Export to different formats
                    var pngData = epochImage.Export(ImageFormat.Png);
                    if (pngData != null)
                    {
                        Console.WriteLine($"Exported to PNG: {pngData.Length} bytes");
                    }

                    var jpegData = epochImage.Export(ImageFormat.Jpeg);
                    if (jpegData != null)
                    {
                        Console.WriteLine($"Exported to JPEG: {jpegData.Length} bytes");
                    }

                    // Save to different locations
                    bool savedPng = await epochImage.SaveAsync(@"C:\Output\exported.png");
                    Console.WriteLine($"Saved as PNG: {savedPng}");

                    bool savedJpeg = await epochImage.SaveAsync(@"C:\Output\exported.jpg");
                    Console.WriteLine($"Saved as JPEG: {savedJpeg}");

                    // Clone the image
                    var clonedImage = epochImage.Clone();
                    if (clonedImage != null)
                    {
                        Console.WriteLine($"Image cloned successfully");
                        Console.WriteLine($"  Original ID: {epochImage.Id}");
                        Console.WriteLine($"  Clone ID: {clonedImage.Id}");
                        clonedImage.Dispose();
                    }

                    // Refresh from file
                    bool refreshed = await epochImage.RefreshAsync();
                    Console.WriteLine($"Image refreshed: {refreshed}");
                }

                epochImage.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    }
}
