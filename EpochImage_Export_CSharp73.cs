using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// EpochImage Export method implementation for C# 7.3
    /// </summary>
    public partial class EpochImage
    {
        #region Export Method - C# 7.3 Compatible

        /// <summary>
        /// Exports the image to the specified format
        /// C# 7.3 compatible implementation using traditional control structures
        /// </summary>
        /// <param name="format">The format to export to</param>
        /// <returns>The exported image data as byte array, or null if export failed</returns>
        public byte[] Export(ImageFormat format)
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    using (var stream = new MemoryStream())
                    {
                        System.Drawing.Imaging.ImageFormat drawingFormat = GetDrawingImageFormat(format);
                        
                        if (format == ImageFormat.Jpeg)
                        {
                            SaveWithQualityToStream(stream, drawingFormat, Quality);
                        }
                        else
                        {
                            _image.Save(stream, drawingFormat);
                        }
                        
                        return stream.ToArray();
                    }
                }
                catch (Exception ex)
                {
                    // Log error if logging is available
                    System.Diagnostics.Debug.WriteLine($"Export failed: {ex.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// Gets the System.Drawing.Imaging.ImageFormat from our custom ImageFormat enum
        /// C# 7.3 compatible using traditional switch statement
        /// </summary>
        /// <param name="format">Our custom ImageFormat enum value</param>
        /// <returns>Corresponding System.Drawing.Imaging.ImageFormat</returns>
        private System.Drawing.Imaging.ImageFormat GetDrawingImageFormat(ImageFormat format)
        {
            switch (format)
            {
                case ImageFormat.Jpeg:
                    return System.Drawing.Imaging.ImageFormat.Jpeg;
                case ImageFormat.Png:
                    return System.Drawing.Imaging.ImageFormat.Png;
                case ImageFormat.Bmp:
                    return System.Drawing.Imaging.ImageFormat.Bmp;
                case ImageFormat.Gif:
                    return System.Drawing.Imaging.ImageFormat.Gif;
                case ImageFormat.Tiff:
                    return System.Drawing.Imaging.ImageFormat.Tiff;
                case ImageFormat.Ico:
                    return System.Drawing.Imaging.ImageFormat.Icon;
                case ImageFormat.Emf:
                    return System.Drawing.Imaging.ImageFormat.Emf;
                case ImageFormat.Wmf:
                    return System.Drawing.Imaging.ImageFormat.Wmf;
                case ImageFormat.Exif:
                    return System.Drawing.Imaging.ImageFormat.Exif;
                case ImageFormat.MemoryBmp:
                    return System.Drawing.Imaging.ImageFormat.MemoryBmp;
                default:
                    return System.Drawing.Imaging.ImageFormat.Png; // Default fallback
            }
        }

        /// <summary>
        /// Alternative implementation using if-else statements (C# 7.3 compatible)
        /// </summary>
        /// <param name="format">The format to export to</param>
        /// <returns>The exported image data as byte array, or null if export failed</returns>
        public byte[] ExportAlternative(ImageFormat format)
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                try
                {
                    using (var stream = new MemoryStream())
                    {
                        System.Drawing.Imaging.ImageFormat drawingFormat;
                        
                        // C# 7.3 compatible if-else chain
                        if (format == ImageFormat.Jpeg)
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Jpeg;
                        else if (format == ImageFormat.Png)
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Png;
                        else if (format == ImageFormat.Bmp)
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Bmp;
                        else if (format == ImageFormat.Gif)
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Gif;
                        else if (format == ImageFormat.Tiff)
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Tiff;
                        else if (format == ImageFormat.Ico)
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Icon;
                        else if (format == ImageFormat.Emf)
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Emf;
                        else if (format == ImageFormat.Wmf)
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Wmf;
                        else if (format == ImageFormat.Exif)
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Exif;
                        else if (format == ImageFormat.MemoryBmp)
                            drawingFormat = System.Drawing.Imaging.ImageFormat.MemoryBmp;
                        else
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Png; // Default

                        // Handle JPEG quality separately
                        if (format == ImageFormat.Jpeg)
                        {
                            SaveWithQualityToStream(stream, drawingFormat, Quality);
                        }
                        else
                        {
                            _image.Save(stream, drawingFormat);
                        }
                        
                        return stream.ToArray();
                    }
                }
                catch (ArgumentException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Invalid format argument: {ex.Message}");
                    return null;
                }
                catch (ExternalException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"GDI+ error during export: {ex.Message}");
                    return null;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Unexpected error during export: {ex.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// Exports with custom quality settings (C# 7.3 compatible)
        /// </summary>
        /// <param name="format">The format to export to</param>
        /// <param name="quality">Quality setting (0-100, only applies to JPEG)</param>
        /// <returns>The exported image data as byte array, or null if export failed</returns>
        public byte[] ExportWithQuality(ImageFormat format, int quality)
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return null;

                // Validate quality parameter
                if (quality < 0 || quality > 100)
                {
                    quality = Math.Max(0, Math.Min(100, quality)); // Clamp to valid range
                }

                try
                {
                    using (var stream = new MemoryStream())
                    {
                        var drawingFormat = GetDrawingImageFormat(format);
                        
                        if (format == ImageFormat.Jpeg)
                        {
                            SaveWithQualityToStream(stream, drawingFormat, quality);
                        }
                        else
                        {
                            _image.Save(stream, drawingFormat);
                        }
                        
                        return stream.ToArray();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Export with quality failed: {ex.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// Exports to multiple formats simultaneously (C# 7.3 compatible)
        /// </summary>
        /// <param name="formats">Array of formats to export to</param>
        /// <returns>Dictionary with format as key and byte array as value</returns>
        public System.Collections.Generic.Dictionary<ImageFormat, byte[]> ExportMultiple(ImageFormat[] formats)
        {
            var results = new System.Collections.Generic.Dictionary<ImageFormat, byte[]>();
            
            if (formats == null || formats.Length == 0)
                return results;

            lock (_lockObject)
            {
                if (_image == null)
                    return results;

                foreach (var format in formats)
                {
                    try
                    {
                        var exportedData = Export(format);
                        if (exportedData != null)
                        {
                            results[format] = exportedData;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Failed to export format {format}: {ex.Message}");
                        // Continue with other formats
                    }
                }
            }

            return results;
        }

        /// <summary>
        /// Gets the recommended format based on image characteristics (C# 7.3 compatible)
        /// </summary>
        /// <returns>Recommended ImageFormat for this image</returns>
        public ImageFormat GetRecommendedExportFormat()
        {
            lock (_lockObject)
            {
                if (_image == null)
                    return ImageFormat.Png; // Safe default

                try
                {
                    // Check if image has transparency
                    var hasTransparency = HasTransparency();
                    
                    // Check if image is photographic (many colors)
                    var isPhotographic = IsPhotographicImage();
                    
                    // Check if image is simple (few colors)
                    var isSimple = IsSimpleImage();

                    // C# 7.3 compatible decision logic
                    if (hasTransparency)
                    {
                        return ImageFormat.Png; // PNG supports transparency
                    }
                    else if (isPhotographic)
                    {
                        return ImageFormat.Jpeg; // JPEG is good for photos
                    }
                    else if (isSimple)
                    {
                        return ImageFormat.Gif; // GIF is good for simple images
                    }
                    else
                    {
                        return ImageFormat.Png; // PNG is a good general-purpose format
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error determining recommended format: {ex.Message}");
                    return ImageFormat.Png; // Safe fallback
                }
            }
        }

        /// <summary>
        /// Validates if the format is supported for export (C# 7.3 compatible)
        /// </summary>
        /// <param name="format">The format to validate</param>
        /// <returns>True if format is supported, false otherwise</returns>
        public bool IsFormatSupported(ImageFormat format)
        {
            // C# 7.3 compatible validation using traditional approach
            var supportedFormats = new ImageFormat[]
            {
                ImageFormat.Jpeg,
                ImageFormat.Png,
                ImageFormat.Bmp,
                ImageFormat.Gif,
                ImageFormat.Tiff,
                ImageFormat.Ico
                // Note: EMF, WMF, EXIF, MemoryBmp may have limited support
            };

            foreach (var supportedFormat in supportedFormats)
            {
                if (format == supportedFormat)
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Gets export information for a specific format (C# 7.3 compatible)
        /// </summary>
        /// <param name="format">The format to get information for</param>
        /// <returns>Export information object</returns>
        public ExportInfo GetExportInfo(ImageFormat format)
        {
            var info = new ExportInfo();
            info.Format = format;
            info.IsSupported = IsFormatSupported(format);
            
            // Set format-specific information using C# 7.3 compatible approach
            switch (format)
            {
                case ImageFormat.Jpeg:
                    info.SupportsQuality = true;
                    info.SupportsTransparency = false;
                    info.IsLossy = true;
                    info.TypicalExtension = ".jpg";
                    info.MimeType = "image/jpeg";
                    break;
                    
                case ImageFormat.Png:
                    info.SupportsQuality = false;
                    info.SupportsTransparency = true;
                    info.IsLossy = false;
                    info.TypicalExtension = ".png";
                    info.MimeType = "image/png";
                    break;
                    
                case ImageFormat.Bmp:
                    info.SupportsQuality = false;
                    info.SupportsTransparency = false;
                    info.IsLossy = false;
                    info.TypicalExtension = ".bmp";
                    info.MimeType = "image/bmp";
                    break;
                    
                case ImageFormat.Gif:
                    info.SupportsQuality = false;
                    info.SupportsTransparency = true;
                    info.IsLossy = false;
                    info.TypicalExtension = ".gif";
                    info.MimeType = "image/gif";
                    break;
                    
                case ImageFormat.Tiff:
                    info.SupportsQuality = false;
                    info.SupportsTransparency = true;
                    info.IsLossy = false;
                    info.TypicalExtension = ".tiff";
                    info.MimeType = "image/tiff";
                    break;
                    
                case ImageFormat.Ico:
                    info.SupportsQuality = false;
                    info.SupportsTransparency = true;
                    info.IsLossy = false;
                    info.TypicalExtension = ".ico";
                    info.MimeType = "image/x-icon";
                    break;
                    
                default:
                    info.SupportsQuality = false;
                    info.SupportsTransparency = false;
                    info.IsLossy = false;
                    info.TypicalExtension = ".bin";
                    info.MimeType = "application/octet-stream";
                    break;
            }

            return info;
        }

        #endregion

        #region Helper Methods - C# 7.3 Compatible

        /// <summary>
        /// Checks if the image has transparency (C# 7.3 compatible)
        /// </summary>
        /// <returns>True if image has transparency</returns>
        private bool HasTransparency()
        {
            try
            {
                // Check pixel format for alpha channel
                var pixelFormat = _image.PixelFormat;
                
                if (pixelFormat == PixelFormat.Format32bppArgb ||
                    pixelFormat == PixelFormat.Format32bppPArgb ||
                    pixelFormat == PixelFormat.Format16bppArgb1555)
                {
                    return true;
                }

                // For indexed formats, check if palette has transparent colors
                if ((pixelFormat & PixelFormat.Indexed) == PixelFormat.Indexed)
                {
                    var palette = _image.Palette;
                    if (palette != null && palette.Entries != null)
                    {
                        foreach (var color in palette.Entries)
                        {
                            if (color.A < 255)
                                return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception)
            {
                return false; // Assume no transparency if we can't determine
            }
        }

        /// <summary>
        /// Determines if image is photographic (many colors) (C# 7.3 compatible)
        /// </summary>
        /// <returns>True if image appears to be photographic</returns>
        private bool IsPhotographicImage()
        {
            try
            {
                // Simple heuristic: if image is large and has many potential colors, it's likely photographic
                var totalPixels = Width * Height;
                var isLargeImage = totalPixels > 100000; // 100K pixels
                var hasHighColorDepth = _image.PixelFormat == PixelFormat.Format24bppRgb ||
                                       _image.PixelFormat == PixelFormat.Format32bppRgb ||
                                       _image.PixelFormat == PixelFormat.Format32bppArgb;

                return isLargeImage && hasHighColorDepth;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Determines if image is simple (few colors) (C# 7.3 compatible)
        /// </summary>
        /// <returns>True if image appears to be simple</returns>
        private bool IsSimpleImage()
        {
            try
            {
                // Simple heuristic: indexed color formats or small images are likely simple
                var pixelFormat = _image.PixelFormat;
                var isIndexed = (pixelFormat & PixelFormat.Indexed) == PixelFormat.Indexed;
                var isSmall = (Width * Height) < 10000; // 10K pixels

                return isIndexed || isSmall;
            }
            catch (Exception)
            {
                return false;
            }
        }

        #endregion
    }

    /// <summary>
    /// Information about export format capabilities (C# 7.3 compatible)
    /// </summary>
    public class ExportInfo
    {
        /// <summary>
        /// Gets or sets the image format
        /// </summary>
        public ImageFormat Format { get; set; }

        /// <summary>
        /// Gets or sets whether the format is supported
        /// </summary>
        public bool IsSupported { get; set; }

        /// <summary>
        /// Gets or sets whether the format supports quality settings
        /// </summary>
        public bool SupportsQuality { get; set; }

        /// <summary>
        /// Gets or sets whether the format supports transparency
        /// </summary>
        public bool SupportsTransparency { get; set; }

        /// <summary>
        /// Gets or sets whether the format is lossy
        /// </summary>
        public bool IsLossy { get; set; }

        /// <summary>
        /// Gets or sets the typical file extension
        /// </summary>
        public string TypicalExtension { get; set; }

        /// <summary>
        /// Gets or sets the MIME type
        /// </summary>
        public string MimeType { get; set; }

        /// <summary>
        /// Returns a string representation of the export info
        /// </summary>
        /// <returns>Formatted export information</returns>
        public override string ToString()
        {
            return $"{Format} ({TypicalExtension}) - Supported: {IsSupported}, " +
                   $"Quality: {SupportsQuality}, Transparency: {SupportsTransparency}, " +
                   $"Lossy: {IsLossy}";
        }
    }
}
