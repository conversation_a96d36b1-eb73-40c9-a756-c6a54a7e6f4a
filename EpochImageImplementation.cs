using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// Complete .NET implementation of the IEpochImage interface
    /// </summary>
    public class EpochImage : IEpochImage
    {
        #region Private Fields

        private readonly Guid _id;
        private readonly DateTime _timestamp;
        private Image _image;
        private byte[] _imageData;
        private string _filePath;
        private Dictionary<string, object> _metadata;
        private List<string> _tags;
        private bool _isLoaded;
        private bool _isModified;
        private bool _disposed;
        private readonly DateTime _creationTime;
        private DateTime _lastModifiedTime;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the EpochImage class
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        public EpochImage(DateTime timestamp)
        {
            _id = Guid.NewGuid();
            _timestamp = timestamp;
            _creationTime = DateTime.Now;
            _lastModifiedTime = _creationTime;
            _metadata = new Dictionary<string, object>();
            _tags = new List<string>();
            _isLoaded = false;
            _isModified = false;
            Quality = 95;
        }

        /// <summary>
        /// Initializes a new instance of the EpochImage class with image data
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        /// <param name="imageData">The image data as byte array</param>
        public EpochImage(DateTime timestamp, byte[] imageData) : this(timestamp)
        {
            if (imageData != null && imageData.Length > 0)
            {
                _imageData = new byte[imageData.Length];
                Array.Copy(imageData, _imageData, imageData.Length);
                LoadFromBytes(_imageData);
            }
        }

        /// <summary>
        /// Initializes a new instance of the EpochImage class with an image
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        /// <param name="image">The image object</param>
        public EpochImage(DateTime timestamp, Image image) : this(timestamp)
        {
            if (image != null)
            {
                _image = new Bitmap(image);
                _isLoaded = true;
                _imageData = ImageToByteArray(_image);
            }
        }

        /// <summary>
        /// Initializes a new instance of the EpochImage class with a file path
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        /// <param name="filePath">The file path to the image</param>
        public EpochImage(DateTime timestamp, string filePath) : this(timestamp)
        {
            _filePath = filePath;
        }

        #endregion

        #region Properties

        public Guid Id => _id;
        public DateTime Timestamp => _timestamp;
        public Image Image => _image;
        public byte[] ImageData => _imageData;

        public string Format
        {
            get
            {
                if (_image?.RawFormat != null)
                {
                    if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Jpeg))
                        return "JPEG";
                    if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Png))
                        return "PNG";
                    if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Bmp))
                        return "BMP";
                    if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Gif))
                        return "GIF";
                    if (_image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Tiff))
                        return "TIFF";

                    return _image.RawFormat.ToString();
                }
                return "Unknown";
            }
        }

        public int Width => _image?.Width ?? 0;
        public int Height => _image?.Height ?? 0;
        public long Size => _imageData?.Length ?? 0;

        public Dictionary<string, object> Metadata
        {
            get => _metadata;
            set
            {
                _metadata = value ?? new Dictionary<string, object>();
                MarkAsModified();
            }
        }

        public string FilePath
        {
            get => _filePath;
            set
            {
                _filePath = value;
                MarkAsModified();
            }
        }

        public bool IsLoaded => _isLoaded;
        public string Name { get; set; }
        public string Description { get; set; }
        public int Quality { get; set; }
        public DateTime CreationTime => _creationTime;
        public DateTime LastModifiedTime => _lastModifiedTime;
        public string CompressionType { get; set; }
        public string PixelFormat => _image?.PixelFormat.ToString() ?? "Unknown";
        public float HorizontalResolution => _image?.HorizontalResolution ?? 0f;
        public float VerticalResolution => _image?.VerticalResolution ?? 0f;

        public List<string> Tags
        {
            get => _tags;
            set
            {
                _tags = value ?? new List<string>();
                MarkAsModified();
            }
        }

        public bool IsModified => _isModified;

        #endregion

        #region Loading and Saving Methods

        public async Task<bool> LoadAsync()
        {
            if (!string.IsNullOrEmpty(_filePath))
            {
                return await LoadAsync(_filePath);
            }
            else if (_imageData != null && _imageData.Length > 0)
            {
                return await Task.Run(() => LoadFromBytes(_imageData));
            }
            return false;
        }

        public async Task<bool> LoadAsync(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                    return false;

                return await Task.Run(() =>
                {
                    try
                    {
                        _image?.Dispose();

                        using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                        {
                            _image = Image.FromStream(fileStream);
                        }

                        _imageData = ImageToByteArray(_image);
                        _filePath = filePath;
                        _isLoaded = true;
                        _lastModifiedTime = File.GetLastWriteTime(filePath);
                        _isModified = false;

                        return true;
                    }
                    catch
                    {
                        return false;
                    }
                });
            }
            catch
            {
                return false;
            }
        }

        public void Unload()
        {
            _image?.Dispose();
            _image = null;
            _isLoaded = false;
        }

        public async Task<bool> SaveAsync(string filePath)
        {
            try
            {
                if (_image == null || string.IsNullOrEmpty(filePath))
                    return false;

                return await Task.Run(() =>
                {
                    try
                    {
                        var directory = Path.GetDirectoryName(filePath);
                        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                        {
                            Directory.CreateDirectory(directory);
                        }

                        var extension = Path.GetExtension(filePath).ToLower();
                        System.Drawing.Imaging.ImageFormat format;

                        switch (extension)
                        {
                            case ".jpg":
                            case ".jpeg":
                                format = System.Drawing.Imaging.ImageFormat.Jpeg;
                                SaveWithQuality(filePath, format, Quality);
                                break;
                            case ".png":
                                format = System.Drawing.Imaging.ImageFormat.Png;
                                _image.Save(filePath, format);
                                break;
                            case ".bmp":
                                format = System.Drawing.Imaging.ImageFormat.Bmp;
                                _image.Save(filePath, format);
                                break;
                            case ".gif":
                                format = System.Drawing.Imaging.ImageFormat.Gif;
                                _image.Save(filePath, format);
                                break;
                            case ".tiff":
                            case ".tif":
                                format = System.Drawing.Imaging.ImageFormat.Tiff;
                                _image.Save(filePath, format);
                                break;
                            default:
                                format = System.Drawing.Imaging.ImageFormat.Png;
                                _image.Save(filePath, format);
                                break;
                        }

                        _filePath = filePath;
                        _isModified = false;
                        _lastModifiedTime = DateTime.Now;

                        return true;
                    }
                    catch
                    {
                        return false;
                    }
                });
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SaveAsync()
        {
            if (string.IsNullOrEmpty(_filePath))
                return false;

            return await SaveAsync(_filePath);
        }

        #endregion

        #region Transformation Methods

        public IEpochImage Clone()
        {
            var clone = new EpochImage(_timestamp);

            if (_image != null)
            {
                clone._image = new Bitmap(_image);
                clone._isLoaded = true;
            }

            if (_imageData != null)
            {
                clone._imageData = new byte[_imageData.Length];
                Array.Copy(_imageData, clone._imageData, _imageData.Length);
            }

            clone._filePath = _filePath;
            clone.Name = Name;
            clone.Description = Description;
            clone.Quality = Quality;
            clone.CompressionType = CompressionType;
            clone._metadata = new Dictionary<string, object>(_metadata);
            clone._tags = new List<string>(_tags);

            return clone;
        }

        public IEpochImage DeepClone()
        {
            return Clone();
        }

        public IEpochImage Resize(int width, int height)
        {
            if (_image == null || width <= 0 || height <= 0)
                return null;

            var resizedBitmap = new Bitmap(width, height);
            using (var graphics = Graphics.FromImage(resizedBitmap))
            {
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = SmoothingMode.HighQuality;
                graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                graphics.CompositingQuality = CompositingQuality.HighQuality;

                graphics.DrawImage(_image, 0, 0, width, height);
            }

            return new EpochImage(_timestamp, resizedBitmap);
        }

        public IEpochImage Crop(Rectangle cropRectangle)
        {
            if (_image == null || cropRectangle.IsEmpty)
                return null;

            cropRectangle = Rectangle.Intersect(cropRectangle, new Rectangle(0, 0, Width, Height));

            if (cropRectangle.IsEmpty)
                return null;

            var croppedBitmap = new Bitmap(cropRectangle.Width, cropRectangle.Height);
            using (var graphics = Graphics.FromImage(croppedBitmap))
            {
                graphics.DrawImage(_image, 0, 0, cropRectangle, GraphicsUnit.Pixel);
            }

            return new EpochImage(_timestamp, croppedBitmap);
        }

        public IEpochImage Rotate(float angle)
        {
            if (_image == null)
                return null;

            var rotatedBitmap = new Bitmap(_image);
            using (var graphics = Graphics.FromImage(rotatedBitmap))
            {
                graphics.TranslateTransform(Width / 2f, Height / 2f);
                graphics.RotateTransform(angle);
                graphics.TranslateTransform(-Width / 2f, -Height / 2f);
                graphics.DrawImage(_image, 0, 0);
            }

            return new EpochImage(_timestamp, rotatedBitmap);
        }

        public IEpochImage FlipHorizontal()
        {
            if (_image == null)
                return null;

            var flippedBitmap = new Bitmap(_image);
            flippedBitmap.RotateFlip(RotateFlipType.RotateNoneFlipX);

            return new EpochImage(_timestamp, flippedBitmap);
        }

        public IEpochImage FlipVertical()
        {
            if (_image == null)
                return null;

            var flippedBitmap = new Bitmap(_image);
            flippedBitmap.RotateFlip(RotateFlipType.RotateNoneFlipY);

            return new EpochImage(_timestamp, flippedBitmap);
        }

        public IEpochImage ToGrayscale()
        {
            if (_image == null)
                return null;

            var grayscaleBitmap = new Bitmap(Width, Height);

            for (int x = 0; x < Width; x++)
            {
                for (int y = 0; y < Height; y++)
                {
                    var pixel = ((Bitmap)_image).GetPixel(x, y);
                    var grayValue = (int)(pixel.R * 0.299 + pixel.G * 0.587 + pixel.B * 0.114);
                    var grayColor = Color.FromArgb(pixel.A, grayValue, grayValue, grayValue);
                    grayscaleBitmap.SetPixel(x, y, grayColor);
                }
            }

            return new EpochImage(_timestamp, grayscaleBitmap);
        }

        public IEpochImage AdjustBrightness(int brightness)
        {
            if (_image == null)
                return null;

            brightness = Math.Max(-100, Math.Min(100, brightness));
            var factor = brightness / 100f;

            var adjustedBitmap = new Bitmap(Width, Height);

            for (int x = 0; x < Width; x++)
            {
                for (int y = 0; y < Height; y++)
                {
                    var pixel = ((Bitmap)_image).GetPixel(x, y);

                    var r = Math.Max(0, Math.Min(255, pixel.R + (int)(255 * factor)));
                    var g = Math.Max(0, Math.Min(255, pixel.G + (int)(255 * factor)));
                    var b = Math.Max(0, Math.Min(255, pixel.B + (int)(255 * factor)));

                    var adjustedColor = Color.FromArgb(pixel.A, r, g, b);
                    adjustedBitmap.SetPixel(x, y, adjustedColor);
                }
            }

            return new EpochImage(_timestamp, adjustedBitmap);
        }

        public IEpochImage AdjustContrast(int contrast)
        {
            if (_image == null)
                return null;

            contrast = Math.Max(-100, Math.Min(100, contrast));
            var factor = (259f * (contrast + 255f)) / (255f * (259f - contrast));

            var adjustedBitmap = new Bitmap(Width, Height);

            for (int x = 0; x < Width; x++)
            {
                for (int y = 0; y < Height; y++)
                {
                    var pixel = ((Bitmap)_image).GetPixel(x, y);

                    var r = Math.Max(0, Math.Min(255, (int)(factor * (pixel.R - 128) + 128)));
                    var g = Math.Max(0, Math.Min(255, (int)(factor * (pixel.G - 128) + 128)));
                    var b = Math.Max(0, Math.Min(255, (int)(factor * (pixel.B - 128) + 128)));

                    var adjustedColor = Color.FromArgb(pixel.A, r, g, b);
                    adjustedBitmap.SetPixel(x, y, adjustedColor);
                }
            }

            return new EpochImage(_timestamp, adjustedBitmap);
        }

        #endregion

        #region Analysis Methods

        public Image GetThumbnail(int width, int height)
        {
            if (_image == null || width <= 0 || height <= 0)
                return null;

            return _image.GetThumbnailImage(width, height, null, IntPtr.Zero);
        }

        public int[] GetHistogram()
        {
            if (_image == null)
                return null;

            var histogram = new int[256];
            var bitmap = _image as Bitmap;

            if (bitmap == null)
                return histogram;

            for (int x = 0; x < Width; x++)
            {
                for (int y = 0; y < Height; y++)
                {
                    var pixel = bitmap.GetPixel(x, y);
                    var luminance = (int)(pixel.R * 0.299 + pixel.G * 0.587 + pixel.B * 0.114);
                    histogram[luminance]++;
                }
            }

            return histogram;
        }

        public ImageStatistics GetStatistics()
        {
            if (_image == null)
                return null;

            var bitmap = _image as Bitmap;
            if (bitmap == null)
                return null;

            var stats = new ImageStatistics();
            long totalR = 0, totalG = 0, totalB = 0;
            int minR = 255, minG = 255, minB = 255;
            int maxR = 0, maxG = 0, maxB = 0;
            long pixelCount = Width * Height;

            for (int x = 0; x < Width; x++)
            {
                for (int y = 0; y < Height; y++)
                {
                    var pixel = bitmap.GetPixel(x, y);

                    totalR += pixel.R;
                    totalG += pixel.G;
                    totalB += pixel.B;

                    minR = Math.Min(minR, pixel.R);
                    minG = Math.Min(minG, pixel.G);
                    minB = Math.Min(minB, pixel.B);

                    maxR = Math.Max(maxR, pixel.R);
                    maxG = Math.Max(maxG, pixel.G);
                    maxB = Math.Max(maxB, pixel.B);
                }
            }

            stats.Mean = Color.FromArgb((int)(totalR / pixelCount), (int)(totalG / pixelCount), (int)(totalB / pixelCount));
            stats.Minimum = Color.FromArgb(minR, minG, minB);
            stats.Maximum = Color.FromArgb(maxR, maxG, maxB);
            stats.PixelCount = pixelCount;
            stats.BrightnessAverage = (stats.Mean.R + stats.Mean.G + stats.Mean.B) / 3.0;

            return stats;
        }

        #endregion

        #region Validation and Comparison Methods

        public bool Validate()
        {
            try
            {
                return _image != null && Width > 0 && Height > 0;
            }
            catch
            {
                return false;
            }
        }

        public ImageComparisonResult Compare(IEpochImage other)
        {
            var result = new ImageComparisonResult();

            if (other == null || _image == null || other.Image == null)
            {
                result.SimilarityPercentage = 0;
                return result;
            }

            if (Width != other.Width || Height != other.Height)
            {
                result.SimilarityPercentage = 0;
                return result;
            }

            var bitmap1 = _image as Bitmap;
            var bitmap2 = other.Image as Bitmap;

            if (bitmap1 == null || bitmap2 == null)
            {
                result.SimilarityPercentage = 0;
                return result;
            }

            long totalPixels = Width * Height;
            long identicalPixels = 0;

            for (int x = 0; x < Width; x++)
            {
                for (int y = 0; y < Height; y++)
                {
                    var pixel1 = bitmap1.GetPixel(x, y);
                    var pixel2 = bitmap2.GetPixel(x, y);

                    if (pixel1.ToArgb() == pixel2.ToArgb())
                    {
                        identicalPixels++;
                    }
                }
            }

            result.SimilarityPercentage = (double)identicalPixels / totalPixels * 100;
            result.AreIdentical = result.SimilarityPercentage == 100;

            return result;
        }

        public byte[] Export(ImageFormat format)
        {
            if (_image == null)
                return null;

            try
            {
                using (var stream = new MemoryStream())
                {
                    System.Drawing.Imaging.ImageFormat drawingFormat;

                    switch (format)
                    {
                        case ImageFormat.Jpeg:
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Jpeg;
                            break;
                        case ImageFormat.Png:
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Png;
                            break;
                        case ImageFormat.Bmp:
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Bmp;
                            break;
                        case ImageFormat.Gif:
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Gif;
                            break;
                        case ImageFormat.Tiff:
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Tiff;
                            break;
                        default:
                            drawingFormat = System.Drawing.Imaging.ImageFormat.Png;
                            break;
                    }

                    _image.Save(stream, drawingFormat);
                    return stream.ToArray();
                }
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> RefreshAsync()
        {
            if (string.IsNullOrEmpty(_filePath) || !File.Exists(_filePath))
                return false;

            return await LoadAsync(_filePath);
        }

        #endregion

        #region Helper Methods

        private bool LoadFromBytes(byte[] data)
        {
            try
            {
                using (var stream = new MemoryStream(data))
                {
                    _image?.Dispose();
                    _image = Image.FromStream(stream);
                    _isLoaded = true;
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        private byte[] ImageToByteArray(Image image)
        {
            if (image == null)
                return null;

            try
            {
                using (var stream = new MemoryStream())
                {
                    image.Save(stream, System.Drawing.Imaging.ImageFormat.Png);
                    return stream.ToArray();
                }
            }
            catch
            {
                return null;
            }
        }

        private void SaveWithQuality(string filePath, System.Drawing.Imaging.ImageFormat format, int quality)
        {
            var encoderParameters = new EncoderParameters(1);
            encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, quality);

            var codec = ImageCodecInfo.GetImageEncoders()
                .FirstOrDefault(c => c.FormatID == format.Guid);

            if (codec != null)
            {
                _image.Save(filePath, codec, encoderParameters);
            }
            else
            {
                _image.Save(filePath, format);
            }
        }

        private void MarkAsModified()
        {
            _isModified = true;
            _lastModifiedTime = DateTime.Now;
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _image?.Dispose();
                _disposed = true;
            }
        }

        #endregion
    }
}