using System;
using System.Drawing;
using System.Runtime.InteropServices;
using FS.AS.Interfaces.Core;

namespace FS.AS.Core.Objects.ImageAnalysis
{
    /// <summary>
    /// Represents an image in Automation Studio with both main and thumbnail versions.
    /// </summary>
    public class EpochImage : IDisposable
    {
        private readonly IEpochImage _epochImage;
        private bool _disposed;

        /// <summary>
        /// Gets the name of the main image file.
        /// </summary>
        public string ImageFileName => _epochImage.ImageFileName;

        /// <summary>
        /// Gets the name of the thumbnail image file.
        /// </summary>
        public string ThumbnailFileName => _epochImage.ThumbnailFileName;

        /// <summary>
        /// Gets or sets the database ID for the main image.
        /// </summary>
        public long DatabaseID
        {
            get => _epochImage.DatabaseID;
            set => _epochImage.DatabaseID = value;
        }

        /// <summary>
        /// Gets or sets the database ID for the thumbnail image.
        /// </summary>
        public long ThumbnailDatabaseID
        {
            get => _epochImage.ThumbnailDatabaseID;
            set => _epochImage.ThumbnailDatabaseID = value;
        }

        /// <summary>
        /// Gets or sets the image data format.
        /// </summary>
        public EpochImageFormat ImageDataFormat
        {
            get => _epochImage.ImageDataFormat;
            set => _epochImage.ImageDataFormat = value;
        }

        /// <summary>
        /// Gets or sets the pixels per unit for spatial resolution.
        /// </summary>
        public long PixelsPerUnit
        {
            get => _epochImage.PixelsPerUnit;
            set => _epochImage.PixelsPerUnit = value;
        }

        /// <summary>
        /// Gets or sets the unit type for spatial resolution.
        /// </summary>
        public ASUnit Unit
        {
            get => _epochImage.Unit;
            set => _epochImage.Unit = value;
        }

        /// <summary>
        /// Initializes a new instance of the EpochImage class.
        /// </summary>
        public EpochImage()
        {
            IEpochImages images = new EpochImages();
            _epochImage = images.Add();
        }

        /// <summary>
        /// Initializes a new instance of the EpochImage class with existing database IDs.
        /// </summary>
        /// <param name="mainImageId">The database ID of the main image.</param>
        /// <param name="thumbnailId">The database ID of the thumbnail image.</param>
        /// <param name="loadMain">Whether to load the main image.</param>
        /// <param name="loadThumbnail">Whether to load the thumbnail image.</param>
        public EpochImage(long mainImageId, long thumbnailId, bool loadMain = true, bool loadThumbnail = true)
        {
            IEpochImages images = new EpochImages();
            _epochImage = images.Item(mainImageId, thumbnailId, loadMain, loadThumbnail);
        }

        /// <summary>
        /// Stores the image in the database.
        /// </summary>
        /// <param name="async">Whether to store asynchronously.</param>
        public void Store(bool async = false)
        {
            _epochImage.Store(async);
        }

        /// <summary>
        /// Stores the image in the database with specific dimensions and format.
        /// </summary>
        /// <param name="async">Whether to store asynchronously.</param>
        /// <param name="width">The width of the image.</param>
        /// <param name="height">The height of the image.</param>
        /// <param name="imageType">The type of the image (e.g., "JPEG", "BMP").</param>
        public void StoreEx(bool async, int width, int height, string imageType)
        {
            _epochImage.StoreEx(async, width, height, imageType);
        }

        /// <summary>
        /// Gets the main image as a System.Drawing.Image.
        /// </summary>
        /// <returns>The main image.</returns>
        public Image GetImage()
        {
            return Image.FromFile(_epochImage.ImageFileName);
        }

        /// <summary>
        /// Gets the thumbnail image as a System.Drawing.Image.
        /// </summary>
        /// <returns>The thumbnail image.</returns>
        public Image GetThumbnail()
        {
            return Image.FromFile(_epochImage.ThumbnailFileName);
        }

        /// <summary>
        /// Inverts the colors of both the main and thumbnail images.
        /// </summary>
        public void Invert()
        {
            _epochImage.Invert();
        }

        /// <summary>
        /// Applies automatic color balance to both the main and thumbnail images.
        /// </summary>
        public void AutomaticColorBalance()
        {
            _epochImage.AutomaticColorBalance();
        }

        /// <summary>
        /// Flips the image horizontally.
        /// </summary>
        public void FlipHorizontally()
        {
            _epochImage.FlipHoriz();
        }

        /// <summary>
        /// Flips the image vertically.
        /// </summary>
        public void FlipVertically()
        {
            _epochImage.FlipVert();
        }

        /// <summary>
        /// Copies the image to the clipboard.
        /// </summary>
        public void CopyToClipboard()
        {
            _epochImage.CopyToClipboard();
        }

        /// <summary>
        /// Disposes of the resources used by the EpochImage.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes of the resources used by the EpochImage.
        /// </summary>
        /// <param name="disposing">Whether the method is being called from Dispose.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    if (_epochImage != null)
                    {
                        Marshal.ReleaseComObject(_epochImage);
                    }
                }
                _disposed = true;
            }
        }

        /// <summary>
        /// Finalizes the EpochImage.
        /// </summary>
        ~EpochImage()
        {
            Dispose(false);
        }
    }
} 