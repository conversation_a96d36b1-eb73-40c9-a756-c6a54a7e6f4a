using System;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// Enumeration of supported export formats for epoch image data and metadata
    /// </summary>
    public enum EpochImageExportFormat
    {
        /// <summary>
        /// XML format - Structured markup language format
        /// Exports image metadata, properties, and optionally embedded image data
        /// Supports hierarchical data structure and is human-readable
        /// File extension: .xml
        /// </summary>
        Xml = 0,

        /// <summary>
        /// JSON format - JavaScript Object Notation
        /// Lightweight data interchange format
        /// Exports image metadata, properties, and optionally embedded image data as base64
        /// File extension: .json
        /// </summary>
        Json = 1,

        /// <summary>
        /// CSV format - Comma Separated Values
        /// Tabular data format suitable for spreadsheet applications
        /// Exports image metadata and properties in rows and columns
        /// Image data is typically referenced by file path or exported separately
        /// File extension: .csv
        /// </summary>
        Csv = 2,

        /// <summary>
        /// Binary format - Custom binary format
        /// Compact binary representation of image data and metadata
        /// Optimized for storage efficiency and fast loading
        /// File extension: .bin or .dat
        /// </summary>
        Binary = 3,

        /// <summary>
        /// YAML format - YAML Ain't Markup Language
        /// Human-readable data serialization standard
        /// Exports image metadata and properties in a clean, readable format
        /// File extension: .yaml or .yml
        /// </summary>
        Yaml = 4,

        /// <summary>
        /// SQLite format - Embedded database format
        /// Exports image data and metadata to a SQLite database file
        /// Supports complex queries and relationships
        /// File extension: .db or .sqlite
        /// </summary>
        Sqlite = 5,

        /// <summary>
        /// Excel format - Microsoft Excel workbook
        /// Exports image metadata to Excel spreadsheet with optional image thumbnails
        /// Supports multiple worksheets and rich formatting
        /// File extension: .xlsx
        /// </summary>
        Excel = 6,

        /// <summary>
        /// HDF5 format - Hierarchical Data Format version 5
        /// Scientific data format optimized for large datasets
        /// Supports compression and chunking for efficient storage
        /// File extension: .h5 or .hdf5
        /// </summary>
        Hdf5 = 7,

        /// <summary>
        /// Protocol Buffers format - Google's data interchange format
        /// Language-neutral, platform-neutral serialization format
        /// Compact binary format with schema evolution support
        /// File extension: .pb or .protobuf
        /// </summary>
        ProtocolBuffers = 8,

        /// <summary>
        /// MessagePack format - Efficient binary serialization format
        /// More compact than JSON while maintaining similar structure
        /// Fast serialization and deserialization
        /// File extension: .msgpack
        /// </summary>
        MessagePack = 9,

        /// <summary>
        /// BSON format - Binary JSON
        /// Binary representation of JSON-like documents
        /// Used by MongoDB and other document databases
        /// File extension: .bson
        /// </summary>
        Bson = 10,

        /// <summary>
        /// Parquet format - Columnar storage format
        /// Optimized for analytics workloads
        /// Supports compression and efficient querying
        /// File extension: .parquet
        /// </summary>
        Parquet = 11,

        /// <summary>
        /// Avro format - Data serialization system
        /// Provides rich data structures and compact binary format
        /// Schema evolution support
        /// File extension: .avro
        /// </summary>
        Avro = 12
    }

    /// <summary>
    /// Extension methods for EpochImageExportFormat enumeration
    /// </summary>
    public static class EpochImageExportFormatExtensions
    {
        /// <summary>
        /// Gets the default file extension for the specified export format
        /// </summary>
        /// <param name="format">The export format</param>
        /// <returns>The file extension including the dot (e.g., ".xml")</returns>
        public static string GetFileExtension(this EpochImageExportFormat format)
        {
            return format switch
            {
                EpochImageExportFormat.Xml => ".xml",
                EpochImageExportFormat.Json => ".json",
                EpochImageExportFormat.Csv => ".csv",
                EpochImageExportFormat.Binary => ".bin",
                EpochImageExportFormat.Yaml => ".yaml",
                EpochImageExportFormat.Sqlite => ".db",
                EpochImageExportFormat.Excel => ".xlsx",
                EpochImageExportFormat.Hdf5 => ".h5",
                EpochImageExportFormat.ProtocolBuffers => ".pb",
                EpochImageExportFormat.MessagePack => ".msgpack",
                EpochImageExportFormat.Bson => ".bson",
                EpochImageExportFormat.Parquet => ".parquet",
                EpochImageExportFormat.Avro => ".avro",
                _ => ".dat"
            };
        }

        /// <summary>
        /// Gets the MIME type for the specified export format
        /// </summary>
        /// <param name="format">The export format</param>
        /// <returns>The MIME type string</returns>
        public static string GetMimeType(this EpochImageExportFormat format)
        {
            return format switch
            {
                EpochImageExportFormat.Xml => "application/xml",
                EpochImageExportFormat.Json => "application/json",
                EpochImageExportFormat.Csv => "text/csv",
                EpochImageExportFormat.Binary => "application/octet-stream",
                EpochImageExportFormat.Yaml => "application/x-yaml",
                EpochImageExportFormat.Sqlite => "application/x-sqlite3",
                EpochImageExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                EpochImageExportFormat.Hdf5 => "application/x-hdf5",
                EpochImageExportFormat.ProtocolBuffers => "application/x-protobuf",
                EpochImageExportFormat.MessagePack => "application/x-msgpack",
                EpochImageExportFormat.Bson => "application/bson",
                EpochImageExportFormat.Parquet => "application/parquet",
                EpochImageExportFormat.Avro => "application/avro",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// Gets a human-readable description of the export format
        /// </summary>
        /// <param name="format">The export format</param>
        /// <returns>A descriptive string</returns>
        public static string GetDescription(this EpochImageExportFormat format)
        {
            return format switch
            {
                EpochImageExportFormat.Xml => "XML - Extensible Markup Language",
                EpochImageExportFormat.Json => "JSON - JavaScript Object Notation",
                EpochImageExportFormat.Csv => "CSV - Comma Separated Values",
                EpochImageExportFormat.Binary => "Binary - Custom binary format",
                EpochImageExportFormat.Yaml => "YAML - YAML Ain't Markup Language",
                EpochImageExportFormat.Sqlite => "SQLite - Embedded database",
                EpochImageExportFormat.Excel => "Excel - Microsoft Excel workbook",
                EpochImageExportFormat.Hdf5 => "HDF5 - Hierarchical Data Format",
                EpochImageExportFormat.ProtocolBuffers => "Protocol Buffers - Google's data format",
                EpochImageExportFormat.MessagePack => "MessagePack - Efficient binary format",
                EpochImageExportFormat.Bson => "BSON - Binary JSON",
                EpochImageExportFormat.Parquet => "Parquet - Columnar storage format",
                EpochImageExportFormat.Avro => "Avro - Data serialization system",
                _ => "Unknown format"
            };
        }

        /// <summary>
        /// Determines if the format supports binary image data embedding
        /// </summary>
        /// <param name="format">The export format</param>
        /// <returns>True if binary data can be embedded, false otherwise</returns>
        public static bool SupportsBinaryData(this EpochImageExportFormat format)
        {
            return format switch
            {
                EpochImageExportFormat.Xml => true,  // Base64 encoded
                EpochImageExportFormat.Json => true, // Base64 encoded
                EpochImageExportFormat.Csv => false, // References only
                EpochImageExportFormat.Binary => true,
                EpochImageExportFormat.Yaml => true, // Base64 encoded
                EpochImageExportFormat.Sqlite => true,
                EpochImageExportFormat.Excel => false, // Thumbnails only
                EpochImageExportFormat.Hdf5 => true,
                EpochImageExportFormat.ProtocolBuffers => true,
                EpochImageExportFormat.MessagePack => true,
                EpochImageExportFormat.Bson => true,
                EpochImageExportFormat.Parquet => true,
                EpochImageExportFormat.Avro => true,
                _ => false
            };
        }

        /// <summary>
        /// Determines if the format is human-readable
        /// </summary>
        /// <param name="format">The export format</param>
        /// <returns>True if the format is human-readable, false otherwise</returns>
        public static bool IsHumanReadable(this EpochImageExportFormat format)
        {
            return format switch
            {
                EpochImageExportFormat.Xml => true,
                EpochImageExportFormat.Json => true,
                EpochImageExportFormat.Csv => true,
                EpochImageExportFormat.Binary => false,
                EpochImageExportFormat.Yaml => true,
                EpochImageExportFormat.Sqlite => false,
                EpochImageExportFormat.Excel => true, // Via Excel application
                EpochImageExportFormat.Hdf5 => false,
                EpochImageExportFormat.ProtocolBuffers => false,
                EpochImageExportFormat.MessagePack => false,
                EpochImageExportFormat.Bson => false,
                EpochImageExportFormat.Parquet => false,
                EpochImageExportFormat.Avro => false,
                _ => false
            };
        }

        /// <summary>
        /// Gets the compression efficiency rating (1-5, where 5 is most efficient)
        /// </summary>
        /// <param name="format">The export format</param>
        /// <returns>Compression efficiency rating</returns>
        public static int GetCompressionEfficiency(this EpochImageExportFormat format)
        {
            return format switch
            {
                EpochImageExportFormat.Xml => 1,
                EpochImageExportFormat.Json => 2,
                EpochImageExportFormat.Csv => 1,
                EpochImageExportFormat.Binary => 4,
                EpochImageExportFormat.Yaml => 2,
                EpochImageExportFormat.Sqlite => 3,
                EpochImageExportFormat.Excel => 3,
                EpochImageExportFormat.Hdf5 => 5,
                EpochImageExportFormat.ProtocolBuffers => 4,
                EpochImageExportFormat.MessagePack => 4,
                EpochImageExportFormat.Bson => 3,
                EpochImageExportFormat.Parquet => 5,
                EpochImageExportFormat.Avro => 4,
                _ => 1
            };
        }
    }
}
