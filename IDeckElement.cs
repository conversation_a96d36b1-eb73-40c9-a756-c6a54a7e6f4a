using System.Collections.ObjectModel;
using System.Xml;

namespace FS.AS.Core.RobotDeck
{
    /// <summary>
    /// Interface for deck elements that can be placed on a robot deck.
    /// Defines the contract for deck element configuration and management.
    /// </summary>
    public interface IDeckElement
    {
        /// <summary>
        /// The name of the deck element
        /// </summary>
        string Name { get; set; }

        /// <summary>
        /// The width of the deck element in deck units
        /// </summary>
        int Width { get; set; }

        /// <summary>
        /// The starting unit position of the deck element (1-based)
        /// </summary>
        int StartUnit { get; set; }

        /// <summary>
        /// The filename of the image associated with this deck element
        /// </summary>
        string ImageFilename { get; set; }

        /// <summary>
        /// The collection of sub-elements associated with this deck element
        /// </summary>
        Collection<DeckSubElement> DeckSubElements { get; }

        /// <summary>
        /// Gets the XML representation of the deck element
        /// </summary>
        /// <returns>XML string representing the deck element</returns>
        string GetXML();

        /// <summary>
        /// Sets the state of the deck element from a string containing XML
        /// </summary>
        /// <param name="xml">XML string containing deck element configuration</param>
        void SetXML(string xml);

        /// <summary>
        /// Writes the XML of the deck element and its subobjects to an XML writer
        /// </summary>
        /// <param name="xmlWriter">XML writer to write the element configuration to</param>
        void WriteXML(XmlWriter xmlWriter);

        /// <summary>
        /// Reads the state of the deck element from an XML Reader
        /// </summary>
        /// <param name="xmlRd">XML reader to read the element configuration from</param>
        void ReadXML(XmlReader xmlRd);
    }
}
