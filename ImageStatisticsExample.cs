using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using FS.AS.Core.Imaging;

namespace FS.AS.Core.Imaging.Examples
{
    /// <summary>
    /// Example usage of ImageStatistics class
    /// </summary>
    public class ImageStatisticsExample
    {
        public static void RunExamples()
        {
            Console.WriteLine("ImageStatistics Examples");
            Console.WriteLine("========================");

            // Example 1: Basic statistics creation
            Example1_BasicStatistics();

            // Example 2: Detailed statistics analysis
            Example2_DetailedAnalysis();

            // Example 3: Channel statistics
            Example3_ChannelStatistics();

            // Example 4: Color distribution analysis
            Example4_ColorDistribution();

            // Example 5: Quality metrics
            Example5_QualityMetrics();

            // Example 6: Comparative analysis
            Example6_ComparativeAnalysis();
        }

        private static void Example1_BasicStatistics()
        {
            Console.WriteLine("\n1. Basic Statistics Creation");
            Console.WriteLine("----------------------------");

            // Create basic statistics
            var stats = new ImageStatistics();
            stats.ImageDimensions = new Size(800, 600);
            stats.PixelCount = 800 * 600;
            stats.Mean = Color.FromArgb(128, 140, 135);
            stats.BrightnessAverage = 134.3;
            stats.Contrast = 45.2;
            stats.StandardDeviation = 28.7;
            stats.UniqueColorCount = 15420;
            stats.AnalysisDuration = TimeSpan.FromMilliseconds(125);

            Console.WriteLine($"Image: {stats.ImageDimensions.Width}x{stats.ImageDimensions.Height}");
            Console.WriteLine($"Pixels: {stats.PixelCount:N0}");
            Console.WriteLine($"Aspect Ratio: {stats.AspectRatio:F2}");
            Console.WriteLine($"Mean Color: R={stats.Mean.R}, G={stats.Mean.G}, B={stats.Mean.B}");
            Console.WriteLine($"Brightness: {stats.BrightnessAverage:F1} ({stats.BrightnessClass})");
            Console.WriteLine($"Contrast: {stats.Contrast:F1} ({stats.ContrastClass})");
            Console.WriteLine($"Unique Colors: {stats.UniqueColorCount:N0} ({stats.ColorRichnessLevel})");
            Console.WriteLine($"Standard Deviation: {stats.StandardDeviation:F1}");
            Console.WriteLine($"Analysis Duration: {stats.AnalysisDuration.TotalMilliseconds}ms");
        }

        private static void Example2_DetailedAnalysis()
        {
            Console.WriteLine("\n2. Detailed Statistics Analysis");
            Console.WriteLine("-------------------------------");

            var stats = CreateDetailedStatistics();

            // Calculate derived statistics
            stats.CalculateDerivedStatistics();

            Console.WriteLine("Detailed Analysis Results:");
            Console.WriteLine($"  Variance: {stats.Variance:F2}");
            Console.WriteLine($"  Skewness: {stats.Skewness:F3}");
            Console.WriteLine($"  Kurtosis: {stats.Kurtosis:F3}");
            Console.WriteLine($"  Entropy: {stats.Entropy:F3}");
            Console.WriteLine($"  Dynamic Range: {stats.DynamicRange:F2}");
            Console.WriteLine($"  Is Grayscale: {stats.IsGrayscale}");
            Console.WriteLine($"  Dominant Channel: {stats.DominantChannel}");

            Console.WriteLine("\nRange Information:");
            Console.WriteLine($"  Red Range: {stats.Range.R}");
            Console.WriteLine($"  Green Range: {stats.Range.G}");
            Console.WriteLine($"  Blue Range: {stats.Range.B}");

            Console.WriteLine("\nMode Information:");
            Console.WriteLine($"  Mode Color: R={stats.Mode.R}, G={stats.Mode.G}, B={stats.Mode.B}");
            Console.WriteLine($"  Mode Frequency: {stats.ModeFrequency:N0} pixels");

            Console.WriteLine("\nClassifications:");
            Console.WriteLine($"  Brightness: {stats.BrightnessClass}");
            Console.WriteLine($"  Contrast: {stats.ContrastClass}");
            Console.WriteLine($"  Color Richness: {stats.ColorRichnessLevel}");
        }

        private static void Example3_ChannelStatistics()
        {
            Console.WriteLine("\n3. Channel Statistics");
            Console.WriteLine("--------------------");

            var stats = CreateDetailedStatistics();

            // Set up channel statistics
            stats.RedChannel = new ChannelStatistics
            {
                Mean = 142.5,
                StandardDeviation = 32.1,
                Minimum = 15,
                Maximum = 248,
                Median = 138.0,
                Mode = 145,
                Variance = 1030.41
            };

            stats.GreenChannel = new ChannelStatistics
            {
                Mean = 128.3,
                StandardDeviation = 28.7,
                Minimum = 8,
                Maximum = 255,
                Median = 125.0,
                Mode = 130,
                Variance = 823.69
            };

            stats.BlueChannel = new ChannelStatistics
            {
                Mean = 115.8,
                StandardDeviation = 35.2,
                Minimum = 0,
                Maximum = 240,
                Median = 112.0,
                Mode = 118,
                Variance = 1239.04
            };

            stats.AlphaChannel = new ChannelStatistics
            {
                Mean = 255.0,
                StandardDeviation = 0.0,
                Minimum = 255,
                Maximum = 255,
                Median = 255.0,
                Mode = 255,
                Variance = 0.0
            };

            Console.WriteLine("Channel Analysis:");
            Console.WriteLine("Channel | Mean  | StdDev | Min | Max | Range | Median | Mode");
            Console.WriteLine("--------|-------|--------|-----|-----|-------|--------|-----");
            Console.WriteLine($"Red     | {stats.RedChannel.Mean,5:F1} | {stats.RedChannel.StandardDeviation,6:F1} | {stats.RedChannel.Minimum,3} | {stats.RedChannel.Maximum,3} | {stats.RedChannel.Range,5} | {stats.RedChannel.Median,6:F1} | {stats.RedChannel.Mode,4}");
            Console.WriteLine($"Green   | {stats.GreenChannel.Mean,5:F1} | {stats.GreenChannel.StandardDeviation,6:F1} | {stats.GreenChannel.Minimum,3} | {stats.GreenChannel.Maximum,3} | {stats.GreenChannel.Range,5} | {stats.GreenChannel.Median,6:F1} | {stats.GreenChannel.Mode,4}");
            Console.WriteLine($"Blue    | {stats.BlueChannel.Mean,5:F1} | {stats.BlueChannel.StandardDeviation,6:F1} | {stats.BlueChannel.Minimum,3} | {stats.BlueChannel.Maximum,3} | {stats.BlueChannel.Range,5} | {stats.BlueChannel.Median,6:F1} | {stats.BlueChannel.Mode,4}");
            Console.WriteLine($"Alpha   | {stats.AlphaChannel.Mean,5:F1} | {stats.AlphaChannel.StandardDeviation,6:F1} | {stats.AlphaChannel.Minimum,3} | {stats.AlphaChannel.Maximum,3} | {stats.AlphaChannel.Range,5} | {stats.AlphaChannel.Median,6:F1} | {stats.AlphaChannel.Mode,4}");

            // Add histograms
            stats.SetChannelHistogram("Red", GenerateSampleHistogram(142.5, 32.1));
            stats.SetChannelHistogram("Green", GenerateSampleHistogram(128.3, 28.7));
            stats.SetChannelHistogram("Blue", GenerateSampleHistogram(115.8, 35.2));

            Console.WriteLine($"\nHistograms available for: {string.Join(", ", stats.ChannelHistograms.Keys)}");
        }

        private static void Example4_ColorDistribution()
        {
            Console.WriteLine("\n4. Color Distribution Analysis");
            Console.WriteLine("------------------------------");

            var stats = new ImageStatistics();
            stats.PixelCount = 10000;

            // Simulate color distribution
            var colors = new[]
            {
                (Color.FromArgb(255, 100, 100), 1500), // Red-ish
                (Color.FromArgb(100, 255, 100), 1200), // Green-ish
                (Color.FromArgb(100, 100, 255), 1000), // Blue-ish
                (Color.FromArgb(200, 200, 200), 800),  // Light gray
                (Color.FromArgb(50, 50, 50), 600),     // Dark gray
                (Color.FromArgb(255, 255, 255), 500),  // White
                (Color.FromArgb(0, 0, 0), 400),        // Black
                (Color.FromArgb(255, 255, 0), 300),    // Yellow
                (Color.FromArgb(255, 0, 255), 250),    // Magenta
                (Color.FromArgb(0, 255, 255), 200)     // Cyan
            };

            foreach (var (color, frequency) in colors)
            {
                stats.AddColorToDistribution(color, frequency);
            }

            // Add remaining colors with small frequencies
            var random = new Random(42);
            var remainingPixels = (int)(stats.PixelCount - colors.Sum(c => c.Item2));
            for (int i = 0; i < remainingPixels; i++)
            {
                var randomColor = Color.FromArgb(random.Next(256), random.Next(256), random.Next(256));
                stats.AddColorToDistribution(randomColor, 1);
            }

            stats.CalculateDerivedStatistics();

            Console.WriteLine($"Total Colors: {stats.ColorDistribution.Count:N0}");
            Console.WriteLine($"Unique Colors: {stats.UniqueColorCount:N0}");
            Console.WriteLine($"Color Richness: {stats.ColorRichnessLevel}");

            var topColors = stats.GetTopColors(10);
            Console.WriteLine("\nTop 10 Colors:");
            Console.WriteLine("Color (R,G,B)      | Frequency | Percentage");
            Console.WriteLine("-------------------|-----------|----------");
            
            foreach (var (color, frequency) in topColors)
            {
                var percentage = (double)frequency / stats.PixelCount * 100;
                Console.WriteLine($"({color.R,3},{color.G,3},{color.B,3})      | {frequency,9:N0} | {percentage,8:F2}%");
            }
        }

        private static void Example5_QualityMetrics()
        {
            Console.WriteLine("\n5. Quality Metrics");
            Console.WriteLine("-----------------");

            var stats = CreateDetailedStatistics();

            // Set quality metrics
            stats.QualityScore = 78.5;
            stats.NoiseLevel = 12.3;
            stats.Sharpness = 0.85;
            stats.BlurEstimate = 1.2;
            stats.EdgeDensity = 0.15;
            stats.TextureComplexity = 0.68;
            stats.ColorDiversity = 0.72;

            // Add custom quality metrics
            stats.QualityMetrics["BRISQUE"] = 25.4;
            stats.QualityMetrics["NIQE"] = 3.8;
            stats.QualityMetrics["PIQE"] = 42.1;
            stats.QualityMetrics["Blockiness"] = 0.08;
            stats.QualityMetrics["Blur"] = 0.15;

            Console.WriteLine("Image Quality Assessment:");
            Console.WriteLine($"  Overall Quality Score: {stats.QualityScore:F1}/100");
            Console.WriteLine($"  Sharpness: {stats.Sharpness:F2}");
            Console.WriteLine($"  Noise Level: {stats.NoiseLevel:F1}");
            Console.WriteLine($"  Blur Estimate: {stats.BlurEstimate:F2}");
            Console.WriteLine($"  Edge Density: {stats.EdgeDensity:F3}");
            Console.WriteLine($"  Texture Complexity: {stats.TextureComplexity:F2}");
            Console.WriteLine($"  Color Diversity: {stats.ColorDiversity:F2}");

            Console.WriteLine("\nAdvanced Quality Metrics:");
            foreach (var metric in stats.QualityMetrics)
            {
                Console.WriteLine($"  {metric.Key}: {metric.Value:F2}");
            }

            // Quality classification
            var qualityClass = stats.QualityScore switch
            {
                >= 90 => "Excellent",
                >= 80 => "Very Good",
                >= 70 => "Good",
                >= 60 => "Fair",
                >= 50 => "Poor",
                _ => "Very Poor"
            };

            Console.WriteLine($"\nQuality Classification: {qualityClass}");
        }

        private static void Example6_ComparativeAnalysis()
        {
            Console.WriteLine("\n6. Comparative Analysis");
            Console.WriteLine("----------------------");

            var scenarios = new[]
            {
                ("High Quality Photo", CreateHighQualityStats()),
                ("Low Quality Photo", CreateLowQualityStats()),
                ("Grayscale Image", CreateGrayscaleStats()),
                ("High Contrast Image", CreateHighContrastStats()),
                ("Low Contrast Image", CreateLowContrastStats())
            };

            Console.WriteLine("Comparative Image Analysis:");
            Console.WriteLine("Scenario           | Brightness | Contrast | Quality | Colors  | Grayscale");
            Console.WriteLine("-------------------|------------|----------|---------|---------|----------");

            foreach (var (name, stats) in scenarios)
            {
                Console.WriteLine($"{name,-18} | {stats.BrightnessAverage,8:F0} | {stats.Contrast,6:F0} | {stats.QualityScore,5:F0} | {stats.UniqueColorCount,7:N0} | {stats.IsGrayscale,9}");
            }

            Console.WriteLine("\nDetailed Classifications:");
            foreach (var (name, stats) in scenarios)
            {
                Console.WriteLine($"\n{name}:");
                Console.WriteLine($"  Brightness: {stats.BrightnessClass}");
                Console.WriteLine($"  Contrast: {stats.ContrastClass}");
                Console.WriteLine($"  Color Richness: {stats.ColorRichnessLevel}");
                Console.WriteLine($"  Dominant Channel: {stats.DominantChannel}");
                Console.WriteLine($"  Summary: {stats}");
            }
        }

        #region Helper Methods

        private static ImageStatistics CreateDetailedStatistics()
        {
            var stats = new ImageStatistics();
            stats.ImageDimensions = new Size(1920, 1080);
            stats.PixelCount = 1920 * 1080;
            stats.Mean = Color.FromArgb(128, 135, 142);
            stats.Minimum = Color.FromArgb(0, 5, 8);
            stats.Maximum = Color.FromArgb(255, 248, 240);
            stats.Median = Color.FromArgb(125, 132, 138);
            stats.BrightnessAverage = 135.0;
            stats.Contrast = 65.2;
            stats.StandardDeviation = 32.1;
            stats.Variance = 1030.41;
            stats.Skewness = 0.15;
            stats.Kurtosis = 2.8;
            stats.Entropy = 7.2;
            stats.UniqueColorCount = 45000;
            stats.AnalysisAlgorithm = "Comprehensive Analysis";
            stats.QualityLevel = AnalysisQuality.Detailed;
            stats.AnalysisDuration = TimeSpan.FromMilliseconds(350);
            stats.ColorSpace = "sRGB";

            return stats;
        }

        private static ImageStatistics CreateHighQualityStats()
        {
            var stats = new ImageStatistics();
            stats.ImageDimensions = new Size(2048, 1536);
            stats.PixelCount = 2048 * 1536;
            stats.BrightnessAverage = 145;
            stats.Contrast = 75;
            stats.QualityScore = 92;
            stats.UniqueColorCount = 85000;
            stats.NoiseLevel = 3.2;
            stats.Sharpness = 0.95;
            stats.RedChannel.Mean = 148;
            stats.GreenChannel.Mean = 142;
            stats.BlueChannel.Mean = 145;
            return stats;
        }

        private static ImageStatistics CreateLowQualityStats()
        {
            var stats = new ImageStatistics();
            stats.ImageDimensions = new Size(640, 480);
            stats.PixelCount = 640 * 480;
            stats.BrightnessAverage = 110;
            stats.Contrast = 25;
            stats.QualityScore = 35;
            stats.UniqueColorCount = 8500;
            stats.NoiseLevel = 25.8;
            stats.Sharpness = 0.35;
            stats.RedChannel.Mean = 112;
            stats.GreenChannel.Mean = 108;
            stats.BlueChannel.Mean = 110;
            return stats;
        }

        private static ImageStatistics CreateGrayscaleStats()
        {
            var stats = new ImageStatistics();
            stats.ImageDimensions = new Size(1024, 768);
            stats.PixelCount = 1024 * 768;
            stats.BrightnessAverage = 128;
            stats.Contrast = 55;
            stats.QualityScore = 78;
            stats.UniqueColorCount = 256;
            stats.RedChannel.Mean = 128;
            stats.GreenChannel.Mean = 128;
            stats.BlueChannel.Mean = 128;
            return stats;
        }

        private static ImageStatistics CreateHighContrastStats()
        {
            var stats = new ImageStatistics();
            stats.ImageDimensions = new Size(800, 600);
            stats.PixelCount = 800 * 600;
            stats.BrightnessAverage = 127;
            stats.Contrast = 95;
            stats.QualityScore = 82;
            stats.UniqueColorCount = 25000;
            stats.RedChannel.Mean = 130;
            stats.GreenChannel.Mean = 125;
            stats.BlueChannel.Mean = 127;
            return stats;
        }

        private static ImageStatistics CreateLowContrastStats()
        {
            var stats = new ImageStatistics();
            stats.ImageDimensions = new Size(800, 600);
            stats.PixelCount = 800 * 600;
            stats.BrightnessAverage = 135;
            stats.Contrast = 15;
            stats.QualityScore = 45;
            stats.UniqueColorCount = 5200;
            stats.RedChannel.Mean = 137;
            stats.GreenChannel.Mean = 134;
            stats.BlueChannel.Mean = 135;
            return stats;
        }

        private static int[] GenerateSampleHistogram(double mean, double stdDev)
        {
            var histogram = new int[256];
            var random = new Random(42);
            
            // Generate normal distribution around mean
            for (int i = 0; i < 10000; i++)
            {
                var value = (int)(mean + stdDev * (random.NextDouble() - 0.5) * 4);
                value = Math.Max(0, Math.Min(255, value));
                histogram[value]++;
            }
            
            return histogram;
        }

        #endregion
    }
}
