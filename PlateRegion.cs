using System;
using System.ComponentModel;
using System.Globalization;
using System.Xml;

namespace FS.AS.Core.RobotDeck
{
    /// <summary>
    /// Represents a plate region with position and dimensions
    /// Used to define the physical area occupied by a plate on a deck sub-element
    /// Compatible with LSDesignHelper.Net.Deck.PlateRegion
    /// </summary>
    [Serializable]
    public class PlateRegion
    {
        #region Private Fields

        private int bottomX_ = 0;
        private int bottomY_ = 0;
        private int height_ = 100;
        private int width_ = 100;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the PlateRegion class
        /// </summary>
        public PlateRegion()
        {
        }

        /// <summary>
        /// Initializes a new instance of the PlateRegion class with specified dimensions
        /// </summary>
        /// <param name="bottomX">X coordinate of the bottom-left corner</param>
        /// <param name="bottomY">Y coordinate of the bottom-left corner</param>
        /// <param name="width">Width of the region</param>
        /// <param name="height">Height of the region</param>
        public PlateRegion(int bottomX, int bottomY, int width, int height)
        {
            BottomX = bottomX;
            BottomY = bottomY;
            Width = width;
            Height = height;
        }

        #endregion

        #region Properties

        /// <summary>
        /// X coordinate of the bottom-left corner of the plate region
        /// Measured in millimeters from the origin
        /// </summary>
        [DisplayName("Bottom X")]
        [Description("X coordinate of the bottom-left corner of the plate region")]
        [Category("Position")]
        public int BottomX
        {
            get => bottomX_;
            set => bottomX_ = value;
        }

        /// <summary>
        /// Y coordinate of the bottom-left corner of the plate region
        /// Measured in millimeters from the origin
        /// </summary>
        [DisplayName("Bottom Y")]
        [Description("Y coordinate of the bottom-left corner of the plate region")]
        [Category("Position")]
        public int BottomY
        {
            get => bottomY_;
            set => bottomY_ = value;
        }

        /// <summary>
        /// Height of the plate region in millimeters
        /// Must be greater than 0
        /// </summary>
        [DisplayName("Height (mm)")]
        [Description("Height of the plate region in millimeters")]
        [Category("Dimensions")]
        public int Height
        {
            get => height_;
            set
            {
                if (value < 1)
                    height_ = 1;
                else if (value > 10000)
                    height_ = 10000;
                else
                    height_ = value;
            }
        }

        /// <summary>
        /// Width of the plate region in millimeters
        /// Must be greater than 0
        /// </summary>
        [DisplayName("Width (mm)")]
        [Description("Width of the plate region in millimeters")]
        [Category("Dimensions")]
        public int Width
        {
            get => width_;
            set
            {
                if (value < 1)
                    width_ = 1;
                else if (value > 10000)
                    width_ = 10000;
                else
                    width_ = value;
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets the center point of the plate region
        /// </summary>
        /// <returns>Tuple containing center X and Y coordinates</returns>
        public (int CenterX, int CenterY) GetCenterPoint()
        {
            var centerX = BottomX + Width / 2;
            var centerY = BottomY + Height / 2;
            return (centerX, centerY);
        }

        /// <summary>
        /// Gets the top-right corner coordinates
        /// </summary>
        /// <returns>Tuple containing top-right X and Y coordinates</returns>
        public (int TopRightX, int TopRightY) GetTopRightCorner()
        {
            return (BottomX + Width, BottomY + Height);
        }

        /// <summary>
        /// Checks if a point is within the plate region bounds
        /// </summary>
        /// <param name="x">X coordinate to test</param>
        /// <param name="y">Y coordinate to test</param>
        /// <returns>True if the point is within the region bounds, false otherwise</returns>
        public bool ContainsPoint(int x, int y)
        {
            return x >= BottomX && x <= BottomX + Width &&
                   y >= BottomY && y <= BottomY + Height;
        }

        /// <summary>
        /// Checks if this plate region overlaps with another plate region
        /// </summary>
        /// <param name="other">Other plate region to check</param>
        /// <returns>True if regions overlap, false otherwise</returns>
        public bool OverlapsWith(PlateRegion other)
        {
            if (other == null)
                return false;

            return BottomX < other.BottomX + other.Width &&
                   BottomX + Width > other.BottomX &&
                   BottomY < other.BottomY + other.Height &&
                   BottomY + Height > other.BottomY;
        }

        /// <summary>
        /// Gets the area of the plate region
        /// </summary>
        /// <returns>Area in square millimeters</returns>
        public int GetArea()
        {
            return Width * Height;
        }

        /// <summary>
        /// Creates a deep copy of the plate region
        /// </summary>
        /// <returns>Deep copy of the plate region</returns>
        public PlateRegion Clone()
        {
            return new PlateRegion(BottomX, BottomY, Width, Height);
        }

        #endregion

        #region XML Serialization

        /// <summary>
        /// Writes the plate region to an XML writer
        /// </summary>
        /// <param name="xmlWriter">XML writer to write to</param>
        /// <exception cref="ArgumentNullException">Thrown when xmlWriter is null</exception>
        public void WriteXML(XmlWriter xmlWriter)
        {
            if (xmlWriter == null)
                throw new ArgumentNullException(nameof(xmlWriter));

            xmlWriter.WriteStartElement("PlateRegion");
            xmlWriter.WriteElementString("BottomX", BottomX.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("BottomY", BottomY.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("Width", Width.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("Height", Height.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteEndElement();
        }

        /// <summary>
        /// Reads the plate region from an XML reader
        /// </summary>
        /// <param name="xmlRd">XML reader to read from</param>
        /// <exception cref="ArgumentNullException">Thrown when xmlRd is null</exception>
        public void ReadXML(XmlReader xmlRd)
        {
            if (xmlRd == null)
                throw new ArgumentNullException(nameof(xmlRd));

            while (xmlRd.Read())
            {
                if (xmlRd.IsStartElement())
                {
                    switch (xmlRd.Name)
                    {
                        case "BottomX":
                            if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int bottomX))
                                BottomX = bottomX;
                            break;

                        case "BottomY":
                            if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int bottomY))
                                BottomY = bottomY;
                            break;

                        case "Width":
                            if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int width))
                                Width = width;
                            break;

                        case "Height":
                            if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int height))
                                Height = height;
                            break;
                    }
                }
                else if (xmlRd.NodeType == XmlNodeType.EndElement && xmlRd.Name == "PlateRegion")
                {
                    break;
                }
            }
        }

        #endregion

        #region Object Overrides

        /// <summary>
        /// Returns a string representation of the plate region
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PlateRegion: ({BottomX},{BottomY}) {Width}x{Height}mm";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">Object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object obj)
        {
            if (obj is PlateRegion other)
            {
                return BottomX == other.BottomX &&
                       BottomY == other.BottomY &&
                       Width == other.Width &&
                       Height == other.Height;
            }
            return false;
        }

        /// <summary>
        /// Serves as the default hash function (.NET Framework compatible)
        /// </summary>
        /// <returns>Hash code for the current object</returns>
        public override int GetHashCode()
        {
            unchecked
            {
                int hash = 17;
                hash = hash * 23 + BottomX.GetHashCode();
                hash = hash * 23 + BottomY.GetHashCode();
                hash = hash * 23 + Width.GetHashCode();
                hash = hash * 23 + Height.GetHashCode();
                return hash;
            }
        }

        #endregion
    }
}
