using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing.Design;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Xml;

namespace FS.AS.Core.RobotDeck
{
    /// <summary>
    /// Complete .NET implementation of AvailableXCMElements class
    /// Represents a collection of available deck elements that can be placed on a robot deck
    /// Uses C# 7.3 features and compatible with .NET Framework
    /// </summary>
    [Serializable]
    public class AvailableXCMElementsComplete
    {
        #region Private Fields

        private Collection<IDeckElement> deckElements_;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the AvailableXCMElementsComplete class
        /// Creates default elements if no configuration is loaded
        /// </summary>
        public AvailableXCMElementsComplete()
        {
            deckElements_ = new Collection<IDeckElement>();
            InitializeDefaultElements();
        }

        #endregion

        #region Properties

        /// <summary>
        /// The collection of available deck elements
        /// </summary>
        [DisplayName("Available Deck Elements")]
        [Description("Collection of deck elements available for placement")]
        [Category("Available Elements")]
        [Editor(typeof(CollectionEditor), typeof(UITypeEditor))]
        public Collection<IDeckElement> DeckElements => deckElements_;

        /// <summary>
        /// Gets the number of available elements
        /// </summary>
        [DisplayName("Element Count")]
        [Description("Number of available deck elements")]
        [Category("Statistics")]
        public int ElementCount => deckElements_.Count;

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes default deck elements
        /// </summary>
        private void InitializeDefaultElements()
        {
            try
            {
                // Create default heated three position element
                var heatedElement = new XCMElement
                {
                    Width = 2,
                    ImageFilename = "Heated3Position.bmp",
                    Name = "Heated Three Position",
                    StartUnit = 1
                };
                deckElements_.Add(heatedElement);

                // Create default balance element
                var balanceElement = new XCMElement
                {
                    Width = 4,
                    ImageFilename = "Balance.bmp",
                    Name = "Balance",
                    StartUnit = 1
                };
                deckElements_.Add(balanceElement);

                // Create default ball pickup module
                var ballPickupElement = new XCMElement
                {
                    Width = 2,
                    ImageFilename = "Ball Pickup Module.bmp",
                    Name = "Ball Pickup Module",
                    StartUnit = 1
                };
                deckElements_.Add(ballPickupElement);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to initialize default elements: {ex.Message}", ex);
            }
        }

        #endregion

        #region XML Serialization

        /// <summary>
        /// Gets the XML representation of the available elements
        /// </summary>
        /// <returns>XML string representing the available elements configuration</returns>
        public string GetXML()
        {
            try
            {
                using (var sw = new StringWriter())
                {
                    using (var xmlWriter = new XmlTextWriter(sw))
                    {
                        xmlWriter.Formatting = Formatting.None;
                        xmlWriter.WriteStartDocument();
                        WriteXML(xmlWriter);
                        xmlWriter.WriteEndDocument();
                    }
                    return sw.ToString();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to generate XML: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Sets the available elements configuration from XML
        /// </summary>
        /// <param name="xml">XML string containing available elements configuration</param>
        /// <exception cref="ArgumentException">Thrown when XML is null or empty</exception>
        /// <exception cref="InvalidOperationException">Thrown when XML parsing fails</exception>
        public void SetXML(string xml)
        {
            if (string.IsNullOrEmpty(xml))
                throw new ArgumentException("XML cannot be null or empty", nameof(xml));

            try
            {
                using (var sr = new StringReader(xml))
                {
                    using (var xmlReader = new XmlTextReader(sr))
                    {
                        xmlReader.WhitespaceHandling = WhitespaceHandling.None;
                        ReadXML(xmlReader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to parse XML: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Writes the available elements configuration to an XML writer
        /// </summary>
        /// <param name="xmlWriter">XML writer to write to</param>
        /// <exception cref="ArgumentNullException">Thrown when xmlWriter is null</exception>
        public void WriteXML(XmlWriter xmlWriter)
        {
            if (xmlWriter == null)
                throw new ArgumentNullException(nameof(xmlWriter));

            try
            {
                xmlWriter.WriteStartElement("Symyx.AutomationStudio.RobotDeck.AvailableXCMElements");
                xmlWriter.WriteStartElement("Symyx.AutomationStudio.RobotDeck.DeckElements");

                foreach (var deckElement in deckElements_)
                {
                    deckElement.WriteXML(xmlWriter);
                }

                xmlWriter.WriteEndElement(); // DeckElements
                xmlWriter.WriteEndElement(); // AvailableXCMElements
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to write XML: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Reads the available elements configuration from an XML reader
        /// </summary>
        /// <param name="xmlRd">XML reader to read from</param>
        /// <exception cref="ArgumentNullException">Thrown when xmlRd is null</exception>
        /// <exception cref="InvalidOperationException">Thrown when XML reading fails</exception>
        public void ReadXML(XmlReader xmlRd)
        {
            if (xmlRd == null)
                throw new ArgumentNullException(nameof(xmlRd));

            try
            {
                // Clear existing elements
                DeckElements.Clear();

                while (xmlRd.Read())
                {
                    if (xmlRd.Name == "Symyx.AutomationStudio.RobotDeck.AvailableXCMElements" && xmlRd.NodeType == XmlNodeType.Element)
                    {
                        while (xmlRd.Read() && !(xmlRd.Name == "Symyx.AutomationStudio.RobotDeck.DeckElements" && xmlRd.NodeType == XmlNodeType.EndElement))
                        {
                            if (xmlRd.IsStartElement())
                            {
                                var element = new XCMElement();
                                element.ReadXML(xmlRd);
                                DeckElements.Add(element);
                            }
                        }
                    }
                    else if (xmlRd.Name == "Symyx.AutomationStudio.RobotDeck.AvailableXCMElements" && xmlRd.NodeType == XmlNodeType.EndElement)
                    {
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to read XML: {ex.Message}", ex);
            }
        }

        #endregion

        #region File Operations

        /// <summary>
        /// Loads available elements from the default AvailableModules.xml file
        /// </summary>
        /// <returns>True if loaded successfully, false if file not found</returns>
        public bool LoadFromDefaultFile()
        {
            try
            {
                var fileName = SettingsHelper.GetConfigurationDirectory().Combine("Deck\\AvailableModules.xml");
                return LoadFromFile(fileName);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to load from default file: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Loads available elements from the specified XML file
        /// </summary>
        /// <param name="fileName">Path to the XML file</param>
        /// <returns>True if loaded successfully, false if file not found</returns>
        /// <exception cref="ArgumentException">Thrown when fileName is null or empty</exception>
        /// <exception cref="InvalidOperationException">Thrown when file loading fails</exception>
        public bool LoadFromFile(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                throw new ArgumentException("File name cannot be null or empty", nameof(fileName));

            try
            {
                if (!File.Exists(fileName))
                    return false;

                using (var stream = new StreamReader(fileName))
                {
                    var fileXML = stream.ReadToEnd();
                    SetXML(fileXML);
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to load from file '{fileName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Saves available elements to the specified XML file
        /// </summary>
        /// <param name="fileName">Path to the XML file</param>
        /// <exception cref="ArgumentException">Thrown when fileName is null or empty</exception>
        /// <exception cref="InvalidOperationException">Thrown when file saving fails</exception>
        public void SaveToFile(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                throw new ArgumentException("File name cannot be null or empty", nameof(fileName));

            try
            {
                var directoryName = Path.GetDirectoryName(fileName);
                if (!string.IsNullOrEmpty(directoryName) && !Directory.Exists(directoryName))
                {
                    Directory.CreateDirectory(directoryName);
                }

                var xml = GetXML();
                File.WriteAllText(fileName, xml);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to save to file '{fileName}': {ex.Message}", ex);
            }
        }

        #endregion

        #region Element Management

        /// <summary>
        /// Adds a new deck element to the available elements collection
        /// </summary>
        /// <param name="element">Deck element to add</param>
        /// <exception cref="ArgumentNullException">Thrown when element is null</exception>
        /// <exception cref="ArgumentException">Thrown when element with same name already exists</exception>
        public void AddElement(IDeckElement element)
        {
            if (element == null)
                throw new ArgumentNullException(nameof(element));

            if (string.IsNullOrEmpty(element.Name))
                throw new ArgumentException("Element name cannot be null or empty", nameof(element));

            if (DeckElements.Any(e => string.Equals(e.Name, element.Name, StringComparison.OrdinalIgnoreCase)))
                throw new ArgumentException($"Element with name '{element.Name}' already exists", nameof(element));

            DeckElements.Add(element);
        }

        /// <summary>
        /// Removes a deck element from the available elements collection
        /// </summary>
        /// <param name="element">Deck element to remove</param>
        /// <returns>True if element was found and removed, false otherwise</returns>
        public bool RemoveElement(IDeckElement element)
        {
            if (element == null)
                return false;

            return DeckElements.Remove(element);
        }

        /// <summary>
        /// Removes a deck element by name from the available elements collection
        /// </summary>
        /// <param name="elementName">Name of the deck element to remove</param>
        /// <returns>True if element was found and removed, false otherwise</returns>
        public bool RemoveElementByName(string elementName)
        {
            if (string.IsNullOrEmpty(elementName))
                return false;

            var element = FindElementByName(elementName);
            if (element != null)
            {
                return DeckElements.Remove(element);
            }

            return false;
        }

        /// <summary>
        /// Finds a deck element by name
        /// </summary>
        /// <param name="elementName">Name of the deck element to find</param>
        /// <returns>Deck element if found, null otherwise</returns>
        public IDeckElement FindElementByName(string elementName)
        {
            if (string.IsNullOrEmpty(elementName))
                return null;

            return DeckElements.FirstOrDefault(e => string.Equals(e.Name, elementName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Gets all deck elements with the specified width
        /// </summary>
        /// <param name="width">Width in deck units</param>
        /// <returns>Collection of deck elements with the specified width</returns>
        public Collection<IDeckElement> GetElementsByWidth(int width)
        {
            var elements = new Collection<IDeckElement>();

            foreach (var element in DeckElements.Where(e => e.Width == width))
            {
                elements.Add(element);
            }

            return elements;
        }

        /// <summary>
        /// Gets all deck elements that contain the specified text in their name
        /// </summary>
        /// <param name="searchText">Text to search for (case-insensitive)</param>
        /// <returns>Collection of matching deck elements</returns>
        public Collection<IDeckElement> SearchElementsByName(string searchText)
        {
            var elements = new Collection<IDeckElement>();

            if (string.IsNullOrEmpty(searchText))
                return elements;

            foreach (var element in DeckElements.Where(e => e.Name.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0))
            {
                elements.Add(element);
            }

            return elements;
        }

        /// <summary>
        /// Clears all available elements and resets to defaults
        /// </summary>
        public void ResetToDefaults()
        {
            DeckElements.Clear();
            InitializeDefaultElements();
        }

        /// <summary>
        /// Creates a deep copy of the specified element
        /// </summary>
        /// <param name="element">Element to copy</param>
        /// <returns>Deep copy of the element</returns>
        /// <exception cref="ArgumentNullException">Thrown when element is null</exception>
        public IDeckElement CloneElement(IDeckElement element)
        {
            if (element == null)
                throw new ArgumentNullException(nameof(element));

            try
            {
                var xml = element.GetXML();
                var clone = new XCMElement();
                clone.SetXML(xml);
                return clone;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to clone element '{element.Name}': {ex.Message}", ex);
            }
        }

        #endregion

        #region Validation and Utilities

        /// <summary>
        /// Validates all available elements
        /// </summary>
        /// <returns>True if all elements are valid, false otherwise</returns>
        public bool IsValid()
        {
            try
            {
                foreach (var element in DeckElements)
                {
                    if (string.IsNullOrEmpty(element.Name))
                        return false;

                    if (element.Width <= 0)
                        return false;

                    if (string.IsNullOrEmpty(element.ImageFilename))
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets statistics about the available elements
        /// </summary>
        /// <returns>Tuple containing element statistics</returns>
        public (int TotalElements, int MinWidth, int MaxWidth, double AverageWidth) GetStatistics()
        {
            if (DeckElements.Count == 0)
                return (0, 0, 0, 0);

            var totalElements = DeckElements.Count;
            var minWidth = DeckElements.Min(e => e.Width);
            var maxWidth = DeckElements.Max(e => e.Width);
            var averageWidth = DeckElements.Average(e => e.Width);

            return (totalElements, minWidth, maxWidth, averageWidth);
        }

        /// <summary>
        /// Gets all unique element widths
        /// </summary>
        /// <returns>Collection of unique widths</returns>
        public Collection<int> GetUniqueWidths()
        {
            var widths = new Collection<int>();
            var uniqueWidths = DeckElements.Select(e => e.Width).Distinct().OrderBy(w => w);

            foreach (var width in uniqueWidths)
            {
                widths.Add(width);
            }

            return widths;
        }

        /// <summary>
        /// Checks if an element with the specified name exists
        /// </summary>
        /// <param name="elementName">Name to check</param>
        /// <returns>True if element exists, false otherwise</returns>
        public bool ContainsElement(string elementName)
        {
            return FindElementByName(elementName) != null;
        }

        #endregion

        #region Object Overrides

        /// <summary>
        /// Returns a string representation of the available elements
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AvailableXCMElementsComplete: {DeckElements.Count} elements";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">Object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object obj)
        {
            if (obj is AvailableXCMElementsComplete other)
            {
                return DeckElements.Count == other.DeckElements.Count;
            }
            return false;
        }

        /// <summary>
        /// Serves as the default hash function (.NET Framework compatible)
        /// </summary>
        /// <returns>Hash code for the current object</returns>
        public override int GetHashCode()
        {
            unchecked
            {
                int hash = 17;
                hash = hash * 23 + DeckElements.Count.GetHashCode();
                return hash;
            }
        }

        #endregion
    }
}
