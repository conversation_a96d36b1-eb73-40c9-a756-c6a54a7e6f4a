using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using FS.AS.Core.Helpers;
using FS.AS.Core.RobotDeck;

namespace FS.AS.Core.Internal.Net.Helpers
{
    /// <summary>
    /// Complete .NET implementation of TrainingHelper for deck configuration and image management
    /// Supports both database-enabled and BenchTop (file system only) environments
    /// Uses C# 7.3 features
    /// </summary>
    public static class TrainingHelper
    {
        #region Private Fields

        private static IDeck _deck;
        private static AvailableXCMElements _availableXcmElements;
        private static readonly object _lockObject = new object();
        private static IAutomationStudioCore _automationStudioCore;
        private const string TrainingSubstrateName = "Training";
        private const double ImagingStationTrainingToolHeight = 10.0;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the current deck configuration
        /// </summary>
        public static IDeck CurrentDeck => GetDeck();

        /// <summary>
        /// Gets the available XCM elements
        /// </summary>
        public static AvailableXCMElements AvailableElements => _availableXcmElements;

        #endregion

        #region Deck Management

        /// <summary>
        /// Gets the current deck configuration, loading from XML if necessary
        /// </summary>
        /// <returns>The current deck instance</returns>
        public static IDeck GetDeck()
        {
            lock (_lockObject)
            {
                if (_deck == null)
                {
                    _deck = new XCMDeck();
                    LoadDeckFromConfiguration();
                }
                return _deck;
            }
        }

        /// <summary>
        /// Refreshes the deck configuration from the file system
        /// </summary>
        public static void RefreshDeck()
        {
            lock (_lockObject)
            {
                _deck = null;
                GetDeck();
            }
        }

        /// <summary>
        /// Refreshes substrate configurations
        /// </summary>
        public static void RefreshSubtrates()
        {
            InitASService();
            // Refresh substrate data from automation studio core
            _automationStudioCore?.RefreshSubstrates();
        }

        /// <summary>
        /// Loads deck configuration from CurrentDeck.xml
        /// </summary>
        private static void LoadDeckFromConfiguration()
        {
            try
            {
                var fileName = SettingsHelper.GetConfigurationDirectory().Combine("CurrentDeck.xml");
                if (File.Exists(fileName))
                {
                    using (var stream = new StreamReader(fileName))
                    {
                        var fileXML = stream.ReadToEnd();
                        // CM3 images folder changed from 8.0.2 to 8.3
                        fileXML = fileXML.Replace("CM3 Images\\", "CM3Images\\");
                        _deck.SetXML(fileXML);
                    }

                    LoadAvailableModules();
                }
                else
                {
                    // Create default deck if no configuration exists
                    CreateDefaultDeck();
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to load deck configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Loads available modules from AvailableModules.xml
        /// </summary>
        private static void LoadAvailableModules()
        {
            try
            {
                _availableXcmElements = new AvailableXCMElements();
                var fileName = SettingsHelper.GetConfigurationDirectory().Combine("Deck\\AvailableModules.xml");
                if (File.Exists(fileName))
                {
                    using (var stream = new StreamReader(fileName))
                    {
                        var fileXML = stream.ReadToEnd();
                        _availableXcmElements.SetXML(fileXML);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to load available modules: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates a default deck configuration
        /// </summary>
        private static void CreateDefaultDeck()
        {
            _deck.Width = 840;
            _deck.Depth = 540;
            _deck.UnitsWide = 12;
            _deck.LeftBorder = 105;
            _deck.RightBorder = 105;
            _deck.FrontBorder = 67.5;
            _deck.BackBorder = 67.5;
            _deck.TopShelfWidth = 15;
            _deck.BottomShelfWidth = 15;
            _deck.ReferenceX = 420;
            _deck.ReferenceY = 270;
            _deck.StartingDeckElementPositionOffset = 0;
            _deck.ExtendedDeckElementPositionOffset = 0;
        }

        #endregion

        #region Image Generation

        /// <summary>
        /// Returns deck image path with the requested position highlighted
        /// </summary>
        /// <param name="positionName">Position name to highlight</param>
        /// <returns>Path to generated deck image</returns>
        public static string GetDeckImagePath(string positionName)
        {
            if (string.IsNullOrEmpty(positionName))
                throw new ArgumentException("Position name cannot be null or empty", nameof(positionName));

            var deckImagesFolder = SettingsHelper.GetTempDirectory().Combine("deckImages");
            var filename = Path.Combine(deckImagesFolder, $"{positionName}.jpg");

            if (!Directory.Exists(deckImagesFolder))
                Directory.CreateDirectory(deckImagesFolder);

            using (var deckImage = GetDeckImage(positionName))
            {
                deckImage.Save(filename, ImageFormat.Jpeg);
            }

            return filename;
        }

        /// <summary>
        /// Returns entire deck image with the requested position highlighted
        /// </summary>
        /// <param name="positionName">Position name to highlight</param>
        /// <returns>Bitmap deck image</returns>
        public static Bitmap GetDeckImage(string positionName)
        {
            if (string.IsNullOrEmpty(positionName))
                throw new ArgumentException("Position name cannot be null or empty", nameof(positionName));

            var deck = GetDeck();
            var pictureBoxes = new Collection<Bitmap>();

            if (!GetSubElementPosition(positionName, deck, out int elementPosition, out int subElementPosition))
            {
                throw new ArgumentException($"Unable to parse position name: {positionName}");
            }

            foreach (var deckElement in deck.DeckElements)
            {
                try
                {
                    var subElementFound = false;
                    if (deckElement.StartUnit == elementPosition && deckElement.DeckSubElements.Count > 0)
                    {
                        foreach (var subElement in deckElement.DeckSubElements)
                        {
                            if (subElement.Position == subElementPosition)
                            {
                                var imagePath = SettingsHelper.GetDeckImagesDirectory().Combine(subElement.ImageFilename);
                                if (File.Exists(imagePath))
                                {
                                    using (var newElementUI = new Bitmap(imagePath))
                                    {
                                        var bitmap = new Bitmap(newElementUI);
                                        bitmap.Tag = deckElement;
                                        pictureBoxes.Add(bitmap);
                                        subElementFound = true;
                                    }
                                }
                            }
                        }
                    }

                    if (!subElementFound)
                    {
                        var imagePath = SettingsHelper.GetDeckImagesDirectory().Combine(deckElement.ImageFilename);
                        if (File.Exists(imagePath))
                        {
                            using (var newElementUI = new Bitmap(imagePath))
                            {
                                var bitmap = new Bitmap(newElementUI);
                                bitmap.Tag = deckElement;
                                pictureBoxes.Add(bitmap);
                            }
                        }
                        else
                        {
                            // Create placeholder image
                            var bitmap = CreatePlaceholderImage(deckElement, deck);
                            pictureBoxes.Add(bitmap);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Create error placeholder
                    var bitmap = CreateErrorPlaceholderImage(deckElement, deck, ex.Message);
                    pictureBoxes.Add(bitmap);
                }
            }

            return CreateBitmap(deck, pictureBoxes);
        }

        /// <summary>
        /// Returns entire deck image with the reference point drawn
        /// </summary>
        /// <param name="scale">Scale factor for the image</param>
        /// <returns>Bitmap deck image</returns>
        public static Bitmap GetDeckImage(double scale = 1.0)
        {
            var deck = GetDeck();
            var pictureBoxes = new Collection<Bitmap>();

            foreach (var deckElement in deck.DeckElements)
            {
                try
                {
                    var imageFilename = deckElement.ImageFilename;
                    var imagePath = SettingsHelper.GetDeckImagesDirectory().Combine(imageFilename);

                    if (File.Exists(imagePath))
                    {
                        using (var newElementUI = new Bitmap(imagePath))
                        {
                            var bitmap = new Bitmap(newElementUI);
                            bitmap.Tag = deckElement;
                            pictureBoxes.Add(bitmap);
                        }
                    }
                    else
                    {
                        // Create placeholder image
                        var bitmap = CreatePlaceholderImage(deckElement, deck);
                        pictureBoxes.Add(bitmap);
                    }
                }
                catch (ArgumentException)
                {
                    // Create blank bitmap for missing images
                    var bitmap = CreatePlaceholderImage(deckElement, deck);
                    pictureBoxes.Add(bitmap);
                }
            }

            return CreateBitmap(deck, pictureBoxes, scale);
        }

        /// <summary>
        /// Returns entire deck image with the reference point drawn
        /// </summary>
        /// <returns>Bitmap deck image</returns>
        public static Bitmap GetDeckImageWithReference()
        {
            var deck = GetDeck();
            var pictureBoxes = new Collection<Bitmap>();
            var subElement = FindSubElementFromReference(deck);

            foreach (var deckElement in deck.DeckElements)
            {
                try
                {
                    var imageFilename = deckElement.ImageFilename;
                    if (subElement != null && deckElement.DeckSubElements.Contains(subElement))
                        imageFilename = subElement.ImageFilename;

                    var imagePath = SettingsHelper.GetDeckImagesDirectory().Combine(imageFilename);
                    if (File.Exists(imagePath))
                    {
                        using (var newElementUI = new Bitmap(imagePath))
                        {
                            var bitmap = new Bitmap(newElementUI);
                            bitmap.Tag = deckElement;
                            pictureBoxes.Add(bitmap);
                        }
                    }
                    else
                    {
                        var bitmap = CreatePlaceholderImage(deckElement, deck);
                        pictureBoxes.Add(bitmap);
                    }
                }
                catch (ArgumentException)
                {
                    var bitmap = CreatePlaceholderImage(deckElement, deck);
                    pictureBoxes.Add(bitmap);
                }
            }

            return CreateBitmap(deck, pictureBoxes);
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Creates a placeholder image for missing deck elements
        /// </summary>
        private static Bitmap CreatePlaceholderImage(IDeckElement deckElement, IDeck deck)
        {
            var width = (int)((deck.Width / deck.UnitsWide) * deckElement.Width);
            var height = (int)deck.Depth;

            var bitmap = new Bitmap(width, height);
            using (var g = Graphics.FromImage(bitmap))
            using (var drawFont = SystemFonts.DefaultFont)
            using (var drawBrush = new SolidBrush(Color.Black))
            using (var brush = new SolidBrush(Color.White))
            using (var drawFormat = new StringFormat(StringFormatFlags.DirectionVertical))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                var rect = new Rectangle(new Point(0, 0), new Size(width, height));

                g.FillRectangle(brush, rect);
                g.DrawString($"Image File Not Found For {deckElement.Name}", drawFont, drawBrush, width / 2, 5, drawFormat);
            }

            bitmap.Tag = deckElement;
            return bitmap;
        }

        /// <summary>
        /// Creates an error placeholder image
        /// </summary>
        private static Bitmap CreateErrorPlaceholderImage(IDeckElement deckElement, IDeck deck, string errorMessage)
        {
            var width = (int)((deck.Width / deck.UnitsWide) * deckElement.Width);
            var height = (int)deck.Depth;

            var bitmap = new Bitmap(width, height);
            using (var g = Graphics.FromImage(bitmap))
            using (var drawFont = SystemFonts.DefaultFont)
            using (var drawBrush = new SolidBrush(Color.Red))
            using (var brush = new SolidBrush(Color.LightYellow))
            using (var drawFormat = new StringFormat(StringFormatFlags.DirectionVertical))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                var rect = new Rectangle(new Point(0, 0), new Size(width, height));

                g.FillRectangle(brush, rect);
                g.DrawString($"Error: {deckElement.Name}\n{errorMessage}", drawFont, drawBrush, width / 2, 5, drawFormat);
            }

            bitmap.Tag = deckElement;
            return bitmap;
        }

        /// <summary>
        /// Creates a composite bitmap from individual deck element images
        /// </summary>
        private static Bitmap CreateBitmap(IDeck deck, Collection<Bitmap> pictureBoxes, double scale = 1.0)
        {
            var deckWidth = (int)(deck.Width * scale);
            var deckHeight = (int)(deck.Depth * scale);
            var compositeBitmap = new Bitmap(deckWidth, deckHeight);

            using (var g = Graphics.FromImage(compositeBitmap))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.Clear(Color.White);

                // Draw deck background
                using (var deckBrush = new SolidBrush(Color.LightGray))
                {
                    g.FillRectangle(deckBrush, 0, 0, deckWidth, deckHeight);
                }

                // Draw deck elements
                foreach (var bitmap in pictureBoxes)
                {
                    if (bitmap.Tag is IDeckElement deckElement)
                    {
                        var elementWidth = (int)((deck.Width / deck.UnitsWide) * deckElement.Width * scale);
                        var elementHeight = (int)(deck.Depth * scale);
                        var elementX = (int)((deck.Width / deck.UnitsWide) * (deckElement.StartUnit - 1) * scale);
                        var elementY = 0;

                        var destRect = new Rectangle(elementX, elementY, elementWidth, elementHeight);
                        g.DrawImage(bitmap, destRect);
                    }
                }

                // Draw reference point if available
                DrawReferencePoint(g, deck, scale);
            }

            return compositeBitmap;
        }

        /// <summary>
        /// Draws the reference point on the deck image
        /// </summary>
        private static void DrawReferencePoint(Graphics g, IDeck deck, double scale)
        {
            var refX = (int)(deck.ReferenceX * scale);
            var refY = (int)(deck.ReferenceY * scale);
            var crossSize = (int)(10 * scale);

            using (var pen = new Pen(Color.Red, 2))
            {
                // Draw crosshair
                g.DrawLine(pen, refX - crossSize, refY, refX + crossSize, refY);
                g.DrawLine(pen, refX, refY - crossSize, refX, refY + crossSize);
            }

            using (var brush = new SolidBrush(Color.Red))
            using (var font = new Font("Arial", (float)(8 * scale)))
            {
                g.DrawString("REF", font, brush, refX + crossSize + 2, refY - crossSize);
            }
        }

        /// <summary>
        /// Parses position name to extract element and sub-element positions
        /// </summary>
        private static bool GetSubElementPosition(string positionName, IDeck deck, out int elementPosition, out int subElementPosition)
        {
            elementPosition = 0;
            subElementPosition = 0;

            if (string.IsNullOrEmpty(positionName))
                return false;

            try
            {
                // Parse position name format: "Deck X-Y PositionName Z"
                var parts = positionName.Split(' ');
                if (parts.Length >= 2 && parts[0].Equals("Deck", StringComparison.OrdinalIgnoreCase))
                {
                    var positionPart = parts[1];
                    var dashIndex = positionPart.IndexOf('-');

                    if (dashIndex > 0)
                    {
                        if (int.TryParse(positionPart.Substring(0, dashIndex), out elementPosition) &&
                            parts.Length > 3 && int.TryParse(parts[parts.Length - 1], out subElementPosition))
                        {
                            elementPosition += deck.StartingDeckElementPositionOffset;
                            return true;
                        }
                    }
                }

                // Alternative parsing for simple numeric positions
                if (int.TryParse(positionName, out elementPosition))
                {
                    subElementPosition = 1;
                    elementPosition += deck.StartingDeckElementPositionOffset;
                    return true;
                }
            }
            catch
            {
                // Parsing failed
            }

            return false;
        }

        /// <summary>
        /// Finds sub-element from reference point
        /// </summary>
        private static IDeckSubElement FindSubElementFromReference(IDeck deck)
        {
            foreach (var deckElement in deck.DeckElements)
            {
                foreach (var subElement in deckElement.DeckSubElements)
                {
                    // Check if this sub-element is at the reference position
                    var elementX = (deck.Width / deck.UnitsWide) * (deckElement.StartUnit - 1);
                    var elementY = 0;

                    if (Math.Abs(elementX + subElement.BottomLeftX - deck.ReferenceX) < 50 &&
                        Math.Abs(elementY + subElement.BottomLeftY - deck.ReferenceY) < 50)
                    {
                        return subElement;
                    }
                }
            }

            return null;
        }

        #endregion

        #region Training and Position Management

        /// <summary>
        /// Gets the deck training position name
        /// </summary>
        public static string GetDeckTrainingPositionName(int elementPosition, int subElementPosition)
        {
            var deck = GetDeck();
            var adjustedPosition = elementPosition - deck.StartingDeckElementPositionOffset;

            foreach (var deckElement in deck.DeckElements)
            {
                if (deckElement.StartUnit == elementPosition)
                {
                    var subElement = deckElement.DeckSubElements.FirstOrDefault(se => se.Position == subElementPosition);
                    if (subElement != null)
                    {
                        return $"Deck {adjustedPosition}-{adjustedPosition + deckElement.Width - 1} {subElement.Name} {subElement.Position}";
                    }
                }
            }

            return $"Deck {adjustedPosition}";
        }

        /// <summary>
        /// Gets training arm types for a position
        /// </summary>
        public static ICollection<string> GetTrainingArmTypes(string positionName)
        {
            var armTypes = new List<string>();

            if (string.IsNullOrEmpty(positionName))
                return armTypes;

            var deck = GetDeck();
            if (GetSubElementPosition(positionName, deck, out int elementPosition, out int subElementPosition))
            {
                foreach (var deckElement in deck.DeckElements)
                {
                    if (deckElement.StartUnit == elementPosition)
                    {
                        var subElement = deckElement.DeckSubElements.FirstOrDefault(se => se.Position == subElementPosition);
                        if (subElement != null)
                        {
                            armTypes.AddRange(subElement.SupportedArmTypes);
                        }
                    }
                }
            }

            return armTypes;
        }

        /// <summary>
        /// Gets deck training positions
        /// </summary>
        public static ICollection<IPlatePosition> GetDeckTrainingPositions()
        {
            var positions = new List<IPlatePosition>();

            InitASService();
            if (_automationStudioCore?.Substrates is INode substrates)
            {
                foreach (INode substrate in substrates.Children)
                {
                    if (substrate.Name.Equals(TrainingSubstrateName, StringComparison.OrdinalIgnoreCase))
                    {
                        foreach (INode child in substrate.Children)
                        {
                            if (child is IPlatePosition platePosition)
                            {
                                positions.Add(platePosition);
                            }
                        }
                    }
                }
            }

            return positions;
        }

        /// <summary>
        /// Gets gripper initialization position
        /// </summary>
        public static IVialPosition GetGripperInitializationPosition()
        {
            InitASService();
            if (_automationStudioCore?.Substrates is INode substrates)
            {
                foreach (INode substrate in substrates.Children)
                {
                    if (substrate.Name.Contains("Gripper", StringComparison.OrdinalIgnoreCase))
                    {
                        foreach (INode child in substrate.Children)
                        {
                            if (child is IVialPosition vialPosition)
                            {
                                return vialPosition;
                            }
                        }
                    }
                }
            }

            return null;
        }

        #endregion

        #region Substrate Management

        /// <summary>
        /// Updates substrate positions with plate information
        /// </summary>
        public static void UpdateSubstratePositions(IPlatePosition platePosition, bool includePlates, bool includeVials)
        {
            if (platePosition == null)
                throw new ArgumentNullException(nameof(platePosition));

            InitASService();

            try
            {
                // Implementation for updating substrate positions
                // This would interact with the automation studio core to update positions
                if (_automationStudioCore != null)
                {
                    // Update the substrate position in the automation studio
                    // The actual implementation would depend on the specific automation studio API
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to update substrate positions: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Initializes the automation studio service
        /// </summary>
        private static void InitASService()
        {
            if (_automationStudioCore == null)
            {
                try
                {
                    _automationStudioCore = new AutomationStudioCore();
                }
                catch (Exception ex)
                {
                    throw new ApplicationException($"Failed to initialize Automation Studio Core: {ex.Message}", ex);
                }
            }
        }

        #endregion

        #region Sub-Element Management

        /// <summary>
        /// Returns path to the sub-element image
        /// </summary>
        public static string GetSubElementImagePath(string positionName)
        {
            if (string.IsNullOrEmpty(positionName))
                throw new ArgumentException("Position name cannot be null or empty", nameof(positionName));

            var deck = GetDeck();

            if (!GetSubElementPosition(positionName, deck, out int elementPosition, out int subElementPosition))
            {
                throw new ArgumentException($"Unable to parse position name: {positionName}");
            }

            var subElement = FindSubElement(deck, elementPosition, subElementPosition);
            return subElement != null ? SettingsHelper.GetDeckImagesDirectory().Combine(subElement.ImageFilename) : "";
        }

        /// <summary>
        /// Returns sub-element image with reference highlighting
        /// </summary>
        public static Bitmap GetSubElementImageWithReference()
        {
            var deck = GetDeck();
            var subElement = FindSubElementFromReference(deck);

            if (subElement != null)
            {
                var imagePath = SettingsHelper.GetDeckImagesDirectory().Combine(subElement.ImageFilename);
                if (File.Exists(imagePath))
                {
                    var bitmap = new Bitmap(imagePath);

                    // Add reference highlighting
                    using (var g = Graphics.FromImage(bitmap))
                    {
                        DrawReferencePoint(g, deck, 1.0);
                    }

                    return bitmap;
                }
            }

            return null;
        }

        /// <summary>
        /// Finds a specific sub-element
        /// </summary>
        private static IDeckSubElement FindSubElement(IDeck deck, int elementPosition, int subElementPosition)
        {
            foreach (var deckElement in deck.DeckElements)
            {
                if (deckElement.StartUnit == elementPosition)
                {
                    return deckElement.DeckSubElements.FirstOrDefault(se => se.Position == subElementPosition);
                }
            }

            return null;
        }

        #endregion

        #region Async Operations

        /// <summary>
        /// Asynchronously loads deck configuration
        /// </summary>
        public static async Task<IDeck> GetDeckAsync()
        {
            return await Task.Run(() => GetDeck());
        }

        /// <summary>
        /// Asynchronously generates deck image
        /// </summary>
        public static async Task<Bitmap> GetDeckImageAsync(string positionName)
        {
            if (string.IsNullOrEmpty(positionName))
                throw new ArgumentException("Position name cannot be null or empty", nameof(positionName));

            return await Task.Run(() => GetDeckImage(positionName));
        }

        /// <summary>
        /// Asynchronously generates deck image with scale
        /// </summary>
        public static async Task<Bitmap> GetDeckImageAsync(double scale = 1.0)
        {
            return await Task.Run(() => GetDeckImage(scale));
        }

        /// <summary>
        /// Asynchronously saves deck image to path
        /// </summary>
        public static async Task<string> SaveDeckImageAsync(string positionName, string outputPath = null)
        {
            if (string.IsNullOrEmpty(positionName))
                throw new ArgumentException("Position name cannot be null or empty", nameof(positionName));

            return await Task.Run(() =>
            {
                if (string.IsNullOrEmpty(outputPath))
                {
                    return GetDeckImagePath(positionName);
                }
                else
                {
                    using (var deckImage = GetDeckImage(positionName))
                    {
                        var directory = Path.GetDirectoryName(outputPath);
                        if (!Directory.Exists(directory))
                            Directory.CreateDirectory(directory);

                        deckImage.Save(outputPath, ImageFormat.Jpeg);
                        return outputPath;
                    }
                }
            });
        }

        #endregion

        #region Validation and Utilities

        /// <summary>
        /// Validates deck configuration
        /// </summary>
        public static bool ValidateDeckConfiguration()
        {
            try
            {
                var deck = GetDeck();

                // Basic validation checks
                if (deck.Width <= 0 || deck.Depth <= 0)
                    return false;

                if (deck.UnitsWide <= 0)
                    return false;

                // Validate deck elements
                foreach (var element in deck.DeckElements)
                {
                    if (element.Width <= 0 || element.StartUnit <= 0)
                        return false;

                    if (string.IsNullOrEmpty(element.ImageFilename))
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets deck statistics
        /// </summary>
        public static (int ElementCount, int SubElementCount, double TotalWidth, double TotalDepth) GetDeckStatistics()
        {
            var deck = GetDeck();
            var elementCount = deck.DeckElements.Count;
            var subElementCount = deck.DeckElements.Sum(e => e.DeckSubElements.Count);

            return (elementCount, subElementCount, deck.Width, deck.Depth);
        }

        /// <summary>
        /// Checks if BenchTop mode is active
        /// </summary>
        public static bool IsBenchTopMode()
        {
            const string rasLitePath = @"c:\Program Files (x86)\Renaissance\Shared\RasLiteService.exe";
            return File.Exists(rasLitePath);
        }

        /// <summary>
        /// Gets available image formats
        /// </summary>
        public static IEnumerable<ImageFormat> GetSupportedImageFormats()
        {
            yield return ImageFormat.Jpeg;
            yield return ImageFormat.Png;
            yield return ImageFormat.Bmp;
            yield return ImageFormat.Gif;
            yield return ImageFormat.Tiff;
        }

        #endregion

        #region Cleanup

        /// <summary>
        /// Cleans up temporary files and resources
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                var tempDir = SettingsHelper.GetTempDirectory().Combine("deckImages");
                if (Directory.Exists(tempDir))
                {
                    var files = Directory.GetFiles(tempDir, "*.jpg");
                    foreach (var file in files)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // Ignore individual file deletion errors
                        }
                    }
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        #endregion
    }
}