using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using FS.AS.Core.Imaging;

namespace FS.AS.Core.Imaging.Examples
{
    /// <summary>
    /// Example usage of EpochImageValidationResult and validation operations
    /// </summary>
    public class EpochImageValidationExample
    {
        public static async Task RunExamples()
        {
            Console.WriteLine("EpochImageValidationResult Examples");
            Console.WriteLine("===================================");

            // Example 1: Basic validation result usage
            await Example1_BasicValidation();

            // Example 2: Detailed validation with multiple checks
            await Example2_DetailedValidation();

            // Example 3: Validation result merging
            await Example3_ValidationMerging();

            // Example 4: Custom validation implementation
            await Example4_CustomValidation();

            // Example 5: Validation reporting
            await Example5_ValidationReporting();
        }

        private static async Task Example1_BasicValidation()
        {
            Console.WriteLine("\n1. Basic Validation Result Usage");
            Console.WriteLine("--------------------------------");

            // Create a validation result
            var validationResult = new EpochImageValidationResult();

            // Simulate validation of some images
            var images = CreateSampleImages();
            validationResult.TotalImagesValidated = images.Count;

            // Add some validation issues
            validationResult.AddError("Image file not found: sample1.jpg");
            validationResult.AddWarning("Image quality is below recommended threshold");
            validationResult.AddDuplicateTimestamp(DateTime.Now);

            // Add an invalid image
            if (images.Count > 0)
            {
                validationResult.AddInvalidImage(images[0], "Corrupted image data");
            }

            // Display results
            Console.WriteLine($"Validation Status: {(validationResult.IsValid ? "PASSED" : "FAILED")}");
            Console.WriteLine($"Success Rate: {validationResult.SuccessRate:F1}%");
            Console.WriteLine($"Errors: {validationResult.ErrorCount}");
            Console.WriteLine($"Warnings: {validationResult.WarningCount}");
            Console.WriteLine($"Invalid Images: {validationResult.InvalidImageCount}");
            Console.WriteLine($"Duplicate Timestamps: {validationResult.DuplicateTimestampCount}");

            // Clean up
            foreach (var image in images)
            {
                image.Dispose();
            }
        }

        private static async Task Example2_DetailedValidation()
        {
            Console.WriteLine("\n2. Detailed Validation with Multiple Checks");
            Console.WriteLine("-------------------------------------------");

            var validationResult = new EpochImageValidationResult();
            var stopwatch = Stopwatch.StartNew();

            // Simulate detailed validation
            var images = CreateSampleImages();
            validationResult.TotalImagesValidated = images.Count;
            validationResult.ValidationContext = "Batch Image Processing";

            foreach (var image in images)
            {
                // Simulate various validation checks
                await PerformImageValidationChecks(image, validationResult);
            }

            stopwatch.Stop();
            validationResult.ValidationDuration = stopwatch.Elapsed;
            validationResult.TotalChecksPerformed = validationResult.ValidationDetailCount;

            // Add metadata
            validationResult.ValidationMetadata["ValidatorVersion"] = "1.0.0";
            validationResult.ValidationMetadata["Environment"] = "Production";
            validationResult.ValidationMetadata["BatchId"] = Guid.NewGuid().ToString();

            // Display detailed results
            Console.WriteLine($"Validation completed in {validationResult.ValidationDuration.TotalMilliseconds:F0}ms");
            Console.WriteLine($"Total checks performed: {validationResult.TotalChecksPerformed}");
            Console.WriteLine($"Validation context: {validationResult.ValidationContext}");
            Console.WriteLine();

            // Show summary
            Console.WriteLine(validationResult.GetSummary());

            // Clean up
            foreach (var image in images)
            {
                image.Dispose();
            }
        }

        private static async Task Example3_ValidationMerging()
        {
            Console.WriteLine("\n3. Validation Result Merging");
            Console.WriteLine("----------------------------");

            // Create multiple validation results (e.g., from parallel processing)
            var result1 = new EpochImageValidationResult();
            var result2 = new EpochImageValidationResult();
            var result3 = new EpochImageValidationResult();

            // Simulate validation results from different batches
            result1.TotalImagesValidated = 10;
            result1.AddError("Batch 1: Missing metadata");
            result1.AddWarning("Batch 1: Low resolution images detected");
            result1.ValidationDuration = TimeSpan.FromMilliseconds(150);

            result2.TotalImagesValidated = 15;
            result2.AddError("Batch 2: Corrupted image file");
            result2.AddDuplicateTimestamp(DateTime.Now.AddMinutes(-1));
            result2.ValidationDuration = TimeSpan.FromMilliseconds(200);

            result3.TotalImagesValidated = 8;
            result3.AddWarning("Batch 3: Unusual timestamp pattern");
            result3.ValidationDuration = TimeSpan.FromMilliseconds(120);

            // Merge all results
            var combinedResult = new EpochImageValidationResult();
            combinedResult.Merge(result1);
            combinedResult.Merge(result2);
            combinedResult.Merge(result3);

            Console.WriteLine("Combined Validation Results:");
            Console.WriteLine($"Total Images: {combinedResult.TotalImagesValidated}");
            Console.WriteLine($"Total Duration: {combinedResult.ValidationDuration.TotalMilliseconds:F0}ms");
            Console.WriteLine($"Overall Status: {(combinedResult.IsValid ? "PASSED" : "FAILED")}");
            Console.WriteLine($"Success Rate: {combinedResult.SuccessRate:F1}%");
            Console.WriteLine();
            Console.WriteLine(combinedResult.ToString());
        }

        private static async Task Example4_CustomValidation()
        {
            Console.WriteLine("\n4. Custom Validation Implementation");
            Console.WriteLine("-----------------------------------");

            var images = CreateSampleImages();
            var validator = new EpochImageValidator();

            // Perform comprehensive validation
            var result = await validator.ValidateAsync(images);

            Console.WriteLine("Custom Validation Results:");
            Console.WriteLine(result.GetDetailedReport());

            // Clean up
            foreach (var image in images)
            {
                image.Dispose();
            }
        }

        private static async Task Example5_ValidationReporting()
        {
            Console.WriteLine("\n5. Validation Reporting");
            Console.WriteLine("-----------------------");

            var validationResult = new EpochImageValidationResult();
            
            // Simulate a complex validation scenario
            validationResult.TotalImagesValidated = 100;
            validationResult.ValidationContext = "Quality Assurance Check";
            
            // Add various types of issues
            validationResult.AddError("Critical: Database connection failed");
            validationResult.AddError("Image validation failed for 3 files");
            validationResult.AddWarning("Performance: Validation took longer than expected");
            validationResult.AddWarning("Quality: 15 images below quality threshold");
            
            // Add detailed validation checks
            for (int i = 0; i < 10; i++)
            {
                var passed = i % 3 != 0; // Simulate some failures
                validationResult.AddValidationDetail(
                    $"Check_{i:D2}",
                    passed,
                    passed ? "Validation passed" : "Validation failed",
                    Guid.NewGuid()
                );
            }

            validationResult.ValidationDuration = TimeSpan.FromSeconds(2.5);

            // Generate different types of reports
            Console.WriteLine("=== SUMMARY REPORT ===");
            Console.WriteLine(validationResult.GetSummary());

            Console.WriteLine("\n=== DETAILED REPORT ===");
            Console.WriteLine(validationResult.GetDetailedReport());

            Console.WriteLine("\n=== QUICK STATUS ===");
            Console.WriteLine(validationResult.ToString());
        }

        private static List<IEpochImage> CreateSampleImages()
        {
            var images = new List<IEpochImage>();
            
            for (int i = 0; i < 5; i++)
            {
                var timestamp = DateTime.Now.AddMinutes(-i);
                var image = new EpochImage(timestamp);
                image.Name = $"Sample Image {i + 1}";
                images.Add(image);
            }

            return images;
        }

        private static async Task PerformImageValidationChecks(IEpochImage image, EpochImageValidationResult result)
        {
            // Simulate various validation checks
            await Task.Delay(10); // Simulate processing time

            // Check 1: Image exists and is loaded
            var isLoaded = image.IsLoaded;
            result.AddValidationDetail("Image_Loaded", isLoaded, 
                isLoaded ? "Image loaded successfully" : "Image not loaded", image.Id);

            // Check 2: Valid timestamp
            var hasValidTimestamp = image.Timestamp > DateTime.MinValue && image.Timestamp <= DateTime.Now;
            result.AddValidationDetail("Valid_Timestamp", hasValidTimestamp,
                hasValidTimestamp ? "Timestamp is valid" : "Invalid timestamp", image.Id);

            // Check 3: Has name
            var hasName = !string.IsNullOrEmpty(image.Name);
            result.AddValidationDetail("Has_Name", hasName,
                hasName ? "Image has name" : "Image name is missing", image.Id);

            // Simulate some random failures
            var random = new Random();
            if (random.Next(0, 4) == 0) // 25% chance of failure
            {
                result.AddValidationDetail("Random_Check", false, "Random validation failure", image.Id);
                result.AddInvalidImage(image, "Failed random validation check");
            }
        }
    }

    /// <summary>
    /// Example custom validator implementation
    /// </summary>
    public class EpochImageValidator
    {
        /// <summary>
        /// Validates a collection of epoch images
        /// </summary>
        /// <param name="images">The images to validate</param>
        /// <returns>Validation result</returns>
        public async Task<EpochImageValidationResult> ValidateAsync(IEnumerable<IEpochImage> images)
        {
            var result = new EpochImageValidationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                var imageList = images.ToList();
                result.TotalImagesValidated = imageList.Count;
                result.ValidationContext = "Comprehensive Image Validation";

                // Validate each image
                foreach (var image in imageList)
                {
                    await ValidateIndividualImage(image, result);
                }

                // Check for duplicates
                await CheckForDuplicateTimestamps(imageList, result);

                // Validate collection integrity
                await ValidateCollectionIntegrity(imageList, result);

                stopwatch.Stop();
                result.ValidationDuration = stopwatch.Elapsed;
                result.TotalChecksPerformed = result.ValidationDetailCount;

                // Final validation status
                if (result.ErrorCount == 0 && result.InvalidImageCount == 0 && result.DuplicateTimestampCount == 0)
                {
                    result.IsValid = true;
                }
                else
                {
                    result.IsValid = false;
                }
            }
            catch (Exception ex)
            {
                result.AddError($"Validation failed with exception: {ex.Message}");
                stopwatch.Stop();
                result.ValidationDuration = stopwatch.Elapsed;
            }

            return result;
        }

        private async Task ValidateIndividualImage(IEpochImage image, EpochImageValidationResult result)
        {
            // Basic null check
            if (image == null)
            {
                result.AddError("Null image found in collection");
                return;
            }

            // Validate ID
            if (image.Id == Guid.Empty)
            {
                result.AddValidationDetail("Valid_ID", false, "Image has empty GUID", image.Id);
                result.AddInvalidImage(image, "Empty GUID");
            }
            else
            {
                result.AddValidationDetail("Valid_ID", true, "Image has valid GUID", image.Id);
            }

            // Validate timestamp
            if (image.Timestamp == DateTime.MinValue || image.Timestamp > DateTime.Now.AddDays(1))
            {
                result.AddValidationDetail("Valid_Timestamp", false, "Invalid timestamp", image.Id);
                result.AddInvalidImage(image, "Invalid timestamp");
            }
            else
            {
                result.AddValidationDetail("Valid_Timestamp", true, "Valid timestamp", image.Id);
            }

            // Validate image data if loaded
            if (image.IsLoaded)
            {
                if (image.Width <= 0 || image.Height <= 0)
                {
                    result.AddValidationDetail("Valid_Dimensions", false, "Invalid image dimensions", image.Id);
                    result.AddInvalidImage(image, "Invalid dimensions");
                }
                else
                {
                    result.AddValidationDetail("Valid_Dimensions", true, $"Valid dimensions: {image.Width}x{image.Height}", image.Id);
                }
            }

            await Task.CompletedTask;
        }

        private async Task CheckForDuplicateTimestamps(List<IEpochImage> images, EpochImageValidationResult result)
        {
            var timestampGroups = images.GroupBy(img => img.Timestamp).Where(g => g.Count() > 1);

            foreach (var group in timestampGroups)
            {
                result.AddDuplicateTimestamp(group.Key);
                result.AddValidationDetail("Unique_Timestamp", false, 
                    $"Duplicate timestamp found: {group.Key:yyyy-MM-dd HH:mm:ss.fff} ({group.Count()} images)");
            }

            await Task.CompletedTask;
        }

        private async Task ValidateCollectionIntegrity(List<IEpochImage> images, EpochImageValidationResult result)
        {
            // Check collection size
            if (images.Count == 0)
            {
                result.AddWarning("Empty image collection");
                result.AddValidationDetail("Non_Empty_Collection", false, "Collection is empty");
            }
            else
            {
                result.AddValidationDetail("Non_Empty_Collection", true, $"Collection contains {images.Count} images");
            }

            // Check timestamp ordering
            var orderedImages = images.OrderBy(img => img.Timestamp).ToList();
            bool isOrdered = images.SequenceEqual(orderedImages);
            
            result.AddValidationDetail("Timestamp_Ordering", isOrdered, 
                isOrdered ? "Images are ordered by timestamp" : "Images are not ordered by timestamp");

            if (!isOrdered)
            {
                result.AddWarning("Images are not ordered by timestamp");
            }

            await Task.CompletedTask;
        }
    }
}
