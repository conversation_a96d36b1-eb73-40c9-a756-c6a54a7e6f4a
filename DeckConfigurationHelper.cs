using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;
using FS.AS.Core.Helpers;
using FS.AS.Core.Internal.Net.Helpers;
using FS.AS.Core.RobotDeck;

namespace FS.AS.Utilities
{
    /// <summary>
    /// Complete .NET implementation of DeckConfigurationHelper for deck configuration management
    /// Supports both database-enabled and BenchTop (file system only) environments
    /// Uses C# 7.3 features
    /// </summary>
    public static class DeckConfigurationHelper
    {
        #region Private Fields and Constants

        private struct SubstrateLocation
        {
            public const string PlateBackLeft = "Back Left";
            public const string PlateFrontLeft = "Front Left";
            public const string PlateFrontRight = "Front Right";
            public const string CustomFront = "Front";
            public const string CustomBack = "Back";
            public const string CustomBackLeft = "Back left";
            public const string CustomBackRight = "Back Right";
            public const string SinglePoint = "Single Point";
        }

        private static Dictionary<string, IPlatePosition> platePositions_;
        private static IDeck deck_;
        private static readonly object _lockObject = new object();

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the current deck configuration
        /// </summary>
        [CLSCompliant(false)]
        public static IDeck Deck => GetDeck();

        /// <summary>
        /// Gets the deck center point
        /// </summary>
        public static Point DeckCenterPoint
        {
            get
            {
                var deck = GetDeck();
                return new Point((int)deck.Width / 2, (int)deck.Depth / 2);
            }
        }

        /// <summary>
        /// Gets the deck reference points
        /// </summary>
        public static Point DeckReferencePoints
        {
            get
            {
                var deck = GetDeck();
                return new Point((int)deck.ReferenceX, (int)deck.ReferenceY);
            }
        }

        #endregion

        #region Deck Management

        /// <summary>
        /// Gets the current deck configuration
        /// </summary>
        /// <returns>The current deck instance</returns>
        private static IDeck GetDeck()
        {
            lock (_lockObject)
            {
                if (deck_ == null)
                {
                    deck_ = TrainingHelper.GetDeck();
                }
                return deck_;
            }
        }

        /// <summary>
        /// Refreshes the deck configuration
        /// </summary>
        public static void RefreshDeck()
        {
            lock (_lockObject)
            {
                deck_ = null;
                TrainingHelper.RefreshDeck();
            }
        }

        #endregion

        #region Training Arm Types and Gripper Management

        /// <summary>
        /// Gets training arm gripper types for a position
        /// </summary>
        /// <param name="positionName">Position name</param>
        /// <returns>Collection of gripper types</returns>
        public static ICollection<Model.GripperType> GetTrainingArmGripperTypes(string positionName)
        {
            if (string.IsNullOrEmpty(positionName))
                throw new ArgumentNullException(nameof(positionName));

            try
            {
                var gripperTypes = new List<Model.GripperType>();
                var types = TrainingHelper.GetTrainingArmTypes(positionName);

                foreach (var type in types)
                {
                    switch (type)
                    {
                        case "DispenseHead":
                            gripperTypes.Add(Model.GripperType.DispenseHead);
                            break;
                        case "VPG":
                            gripperTypes.Add(Model.GripperType.VPG);
                            break;
                        default:
                            gripperTypes.Add(Model.GripperType.None);
                            break;
                    }
                }

                return gripperTypes;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to get training arm gripper types: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets training arm tip types for a position
        /// </summary>
        /// <param name="positionName">Position name</param>
        /// <returns>Collection of tip types with names</returns>
        public static ICollection<(string Name, Model.TipType Type)> GetTrainingArmTipTypes(string positionName)
        {
            if (string.IsNullOrEmpty(positionName))
                throw new ArgumentNullException(nameof(positionName));

            try
            {
                var tipTypes = new List<(string Name, Model.TipType Type)>();
                var types = TrainingHelper.GetTrainingArmTypes(positionName);

                foreach (var type in types)
                {
                    switch (type)
                    {
                        case "Extendable":
                            tipTypes.Add((type, Model.TipType.Extendable));
                            break;
                        case "SingleTip":
                        case "SingleTipZ1":
                        case "SingleTipZ2":
                            tipTypes.Add((type, Model.TipType.FixedTip));
                            break;
                        case "OSRInjection":
                        case "OSRInjectionZ1":
                        case "OSRInjectionZ2":
                            tipTypes.Add((type, Model.TipType.FixedTip));
                            break;
                        default:
                            tipTypes.Add((type, Model.TipType.None));
                            break;
                    }
                }

                return tipTypes;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to get training arm tip types: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets gripper safe position
        /// </summary>
        /// <param name="position">Position to update with safe coordinates</param>
        public static void GetGripperSafePosition(ref Point3D position)
        {
            var pos = TrainingHelper.GetGripperInitializationPosition();
            if (pos != null)
            {
                position.X = pos.X;
                position.Y = pos.Y;
                position.Z = pos.Z;
            }
        }

        #endregion

        #region Substrate and Plate Management

        /// <summary>
        /// Gets substrate type for a position
        /// </summary>
        /// <param name="positionName">Position name</param>
        /// <returns>Substrate type</returns>
        public static Model.SubstrateType GetSubstrateType(string positionName)
        {
            if (string.IsNullOrEmpty(positionName))
                throw new ArgumentNullException(nameof(positionName));

            try
            {
                var plateTypes = new List<Model.SubstrateType>();
                var types = TrainingHelper.GetTrainingArmTypes(positionName);

                foreach (var type in types)
                {
                    switch (type.ToLowerInvariant())
                    {
                        case "plate":
                        case "microplate":
                            plateTypes.Add(Model.SubstrateType.Plate);
                            break;
                        case "vial":
                        case "tube":
                            plateTypes.Add(Model.SubstrateType.Vial);
                            break;
                        case "rack":
                            plateTypes.Add(Model.SubstrateType.Rack);
                            break;
                        default:
                            throw new NotSupportedException($"Unknown substrate type {type}.");
                    }
                }

                return plateTypes.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to get substrate type: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets plate trained points for a position
        /// </summary>
        /// <param name="positionName">Position name</param>
        /// <param name="substrateType">Substrate type</param>
        /// <returns>Dictionary of trained points</returns>
        public static Dictionary<string, Point3D> GetPlateTrainedPoints(string positionName, Model.SubstrateType substrateType)
        {
            if (string.IsNullOrEmpty(positionName))
                throw new ArgumentNullException(nameof(positionName));

            var trainedPoints = new Dictionary<string, Point3D>();

            try
            {
                InitializePlatePositions();

                if (platePositions_.TryGetValue(positionName, out var platePosition))
                {
                    // Extract trained points based on substrate type
                    switch (substrateType)
                    {
                        case Model.SubstrateType.Plate:
                            ExtractPlateTrainedPoints(platePosition, trainedPoints);
                            break;
                        case Model.SubstrateType.Vial:
                            ExtractVialTrainedPoints(platePosition, trainedPoints);
                            break;
                        case Model.SubstrateType.Rack:
                            ExtractRackTrainedPoints(platePosition, trainedPoints);
                            break;
                    }
                }

                return trainedPoints;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to get plate trained points: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Updates substrate positions with plates
        /// </summary>
        public static void UpdateSubstratePositionsWithPlates()
        {
            try
            {
                InitializePlatePositions();

                if (platePositions_ != null)
                {
                    foreach (var position in platePositions_)
                    {
                        if (position.Value != null)
                        {
                            TrainingHelper.UpdateSubstratePositions(position.Value, true, false);
                        }
                    }

                    var core = new FS.AS.Core.AutomationStudioCore();
                    core.Save(FS.AS.Core.itSaveType.stSubstrates);
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to update substrate positions: {ex.Message}", ex);
            }
        }

        #endregion

        #region Image Management

        /// <summary>
        /// Gets deck layout image
        /// </summary>
        /// <returns>Bitmap image of the deck layout</returns>
        public static BitmapImage GetDeckLayoutImage()
        {
            try
            {
                using (var bitMap = TrainingHelper.GetDeckImageWithReference())
                {
                    return ConvertBitmap(bitMap);
                }
            }
            catch (Exception)
            {
                throw new ApplicationException("Unable to load reference deck image.");
            }
        }

        /// <summary>
        /// Gets element reference image
        /// </summary>
        /// <returns>Bitmap image of the element reference</returns>
        public static BitmapImage GetElementReferenceImage()
        {
            try
            {
                using (var bitMap = TrainingHelper.GetSubElementImageWithReference())
                {
                    if (bitMap != null)
                    {
                        return ConvertBitmap(bitMap);
                    }

                    return null;
                }
            }
            catch (Exception)
            {
                throw new ApplicationException("Unable to load sub element reference deck image");
            }
        }

        /// <summary>
        /// Gets deck layout image for a specific position
        /// </summary>
        /// <param name="position">Position name</param>
        /// <returns>Bitmap image of the deck layout with position highlighted</returns>
        public static BitmapImage GetDeckLayoutImage(string position)
        {
            if (string.IsNullOrEmpty(position))
                throw new ArgumentNullException(nameof(position));

            try
            {
                using (var image = TrainingHelper.GetDeckImage(position))
                {
                    return image != null ? ConvertBitmap(image) : null;
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Unable to load deck image for position {position}.\n\n{ex.Message}", ex);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Initializes plate positions collection
        /// </summary>
        private static void InitializePlatePositions()
        {
            if (platePositions_ == null)
            {
                platePositions_ = new Dictionary<string, IPlatePosition>();
                var positions = TrainingHelper.GetDeckTrainingPositions();

                foreach (var position in positions)
                {
                    if (position is PlatePositionClass platePositionClass)
                    {
                        platePositions_.Add(platePositionClass.Name, position);
                    }
                }
            }
        }

        /// <summary>
        /// Extracts trained points for plate substrates
        /// </summary>
        private static void ExtractPlateTrainedPoints(IPlatePosition platePosition, Dictionary<string, Point3D> trainedPoints)
        {
            // Add standard plate positions
            trainedPoints[SubstrateLocation.PlateBackLeft] = new Point3D(platePosition.X - 10, platePosition.Y + 10, platePosition.Z);
            trainedPoints[SubstrateLocation.PlateFrontLeft] = new Point3D(platePosition.X - 10, platePosition.Y - 10, platePosition.Z);
            trainedPoints[SubstrateLocation.PlateFrontRight] = new Point3D(platePosition.X + 10, platePosition.Y - 10, platePosition.Z);
        }

        /// <summary>
        /// Extracts trained points for vial substrates
        /// </summary>
        private static void ExtractVialTrainedPoints(IPlatePosition platePosition, Dictionary<string, Point3D> trainedPoints)
        {
            // Add single point for vials
            trainedPoints[SubstrateLocation.SinglePoint] = new Point3D(platePosition.X, platePosition.Y, platePosition.Z);
        }

        /// <summary>
        /// Extracts trained points for rack substrates
        /// </summary>
        private static void ExtractRackTrainedPoints(IPlatePosition platePosition, Dictionary<string, Point3D> trainedPoints)
        {
            // Add front and back positions for racks
            trainedPoints[SubstrateLocation.CustomFront] = new Point3D(platePosition.X, platePosition.Y - 15, platePosition.Z);
            trainedPoints[SubstrateLocation.CustomBack] = new Point3D(platePosition.X, platePosition.Y + 15, platePosition.Z);
        }

        /// <summary>
        /// Converts a Bitmap to BitmapImage
        /// </summary>
        private static BitmapImage ConvertBitmap(Bitmap bitmap)
        {
            if (bitmap == null)
                return null;

            using (var memory = new MemoryStream())
            {
                bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Png);
                memory.Position = 0;

                var bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.StreamSource = memory;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();
                bitmapImage.Freeze();

                return bitmapImage;
            }
        }

        #endregion

        #region Arm Reference Points

        /// <summary>
        /// Gets arm reference points
        /// </summary>
        /// <param name="name">Arm name</param>
        /// <param name="x">X coordinate</param>
        /// <param name="y">Y coordinate</param>
        /// <param name="z">Z coordinate</param>
        public static void GetArmReferencePoints(string name, out double x, out double y, out double z)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentNullException(nameof(name));

            x = 0; y = 0; z = 0;

            try
            {
                var helper = new ResourceHelper();
                var iid = typeof(ITrainable).GUID.ToString("B");

                if (helper.GetResourceByName(iid, name) is ITrainable resource)
                {
                    resource.GetPoint(1, ref x, ref y, ref z);
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to get arm reference points for {name}: {ex.Message}", ex);
            }
        }

        #endregion

        #region Async Operations

        /// <summary>
        /// Asynchronously gets deck layout image
        /// </summary>
        /// <returns>Task returning BitmapImage</returns>
        public static async Task<BitmapImage> GetDeckLayoutImageAsync()
        {
            return await Task.Run(() => GetDeckLayoutImage());
        }

        /// <summary>
        /// Asynchronously gets deck layout image for a position
        /// </summary>
        /// <param name="position">Position name</param>
        /// <returns>Task returning BitmapImage</returns>
        public static async Task<BitmapImage> GetDeckLayoutImageAsync(string position)
        {
            if (string.IsNullOrEmpty(position))
                throw new ArgumentNullException(nameof(position));

            return await Task.Run(() => GetDeckLayoutImage(position));
        }

        /// <summary>
        /// Asynchronously gets element reference image
        /// </summary>
        /// <returns>Task returning BitmapImage</returns>
        public static async Task<BitmapImage> GetElementReferenceImageAsync()
        {
            return await Task.Run(() => GetElementReferenceImage());
        }

        /// <summary>
        /// Asynchronously updates substrate positions
        /// </summary>
        /// <returns>Task for async operation</returns>
        public static async Task UpdateSubstratePositionsWithPlatesAsync()
        {
            await Task.Run(() => UpdateSubstratePositionsWithPlates());
        }

        #endregion

        #region Validation and Utilities

        /// <summary>
        /// Validates position name format
        /// </summary>
        /// <param name="positionName">Position name to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidPositionName(string positionName)
        {
            if (string.IsNullOrEmpty(positionName))
                return false;

            try
            {
                var deck = GetDeck();
                return TrainingHelper.GetTrainingArmTypes(positionName).Any();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets all available position names
        /// </summary>
        /// <returns>Collection of position names</returns>
        public static ICollection<string> GetAvailablePositionNames()
        {
            var positionNames = new List<string>();

            try
            {
                InitializePlatePositions();

                if (platePositions_ != null)
                {
                    positionNames.AddRange(platePositions_.Keys);
                }
            }
            catch
            {
                // Return empty list on error
            }

            return positionNames;
        }

        /// <summary>
        /// Gets deck configuration summary
        /// </summary>
        /// <returns>Configuration summary</returns>
        public static (int ElementCount, int PositionCount, double Width, double Depth) GetDeckSummary()
        {
            var deck = GetDeck();
            var elementCount = deck.DeckElements.Count;
            var positionCount = deck.DeckElements.Sum(e => e.DeckSubElements.Count);

            return (elementCount, positionCount, deck.Width, deck.Depth);
        }

        /// <summary>
        /// Checks if BenchTop mode is active
        /// </summary>
        /// <returns>True if BenchTop mode, false otherwise</returns>
        public static bool IsBenchTopMode()
        {
            return TrainingHelper.IsBenchTopMode();
        }

        #endregion

        #region Cleanup and Resource Management

        /// <summary>
        /// Clears cached data and forces reload
        /// </summary>
        public static void ClearCache()
        {
            lock (_lockObject)
            {
                deck_ = null;
                platePositions_ = null;
            }

            TrainingHelper.RefreshDeck();
        }

        /// <summary>
        /// Performs cleanup of temporary resources
        /// </summary>
        public static void Cleanup()
        {
            ClearCache();
            TrainingHelper.Cleanup();
        }

        #endregion
    }

    #region Model Classes

    namespace Model
    {
        /// <summary>
        /// Gripper type enumeration
        /// </summary>
        public enum GripperType
        {
            VPG,
            DispenseHead,
            None
        }

        /// <summary>
        /// Tip type enumeration
        /// </summary>
        public enum TipType
        {
            None,
            pH,
            SixTip,
            FixedTip,
            Extendable
        }

        /// <summary>
        /// Substrate type enumeration
        /// </summary>
        public enum SubstrateType
        {
            Plate,
            Vial,
            Rack,
            Unknown
        }
    }

    #endregion

    #region Supporting Structures

    /// <summary>
    /// 3D Point structure
    /// </summary>
    public struct Point3D
    {
        public double X { get; set; }
        public double Y { get; set; }
        public double Z { get; set; }

        public Point3D(double x, double y, double z)
        {
            X = x;
            Y = y;
            Z = z;
        }

        public override string ToString()
        {
            return $"({X:F2}, {Y:F2}, {Z:F2})";
        }
    }

    #endregion
}
