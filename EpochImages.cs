using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// Implementation of the IEpochImages interface
    /// </summary>
    public class EpochImages : IEpochImages
    {
        #region Private Fields

        private readonly List<IEpochImage> _images;
        private int _currentIndex;
        private bool _disposed;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the EpochImages class
        /// </summary>
        public EpochImages()
        {
            _images = new List<IEpochImage>();
            _currentIndex = -1;
        }

        /// <summary>
        /// Initializes a new instance of the EpochImages class with the specified capacity
        /// </summary>
        /// <param name="capacity">The initial capacity of the collection</param>
        public EpochImages(int capacity)
        {
            _images = new List<IEpochImage>(capacity);
            _currentIndex = -1;
        }

        /// <summary>
        /// Initializes a new instance of the EpochImages class with the specified images
        /// </summary>
        /// <param name="images">The initial images to add to the collection</param>
        public EpochImages(IEnumerable<IEpochImage> images)
        {
            _images = new List<IEpochImage>(images ?? throw new ArgumentNullException(nameof(images)));
            _currentIndex = _images.Count > 0 ? 0 : -1;
            Sort(); // Ensure images are sorted by timestamp
        }

        #endregion

        #region Properties

        /// <summary>
        /// Gets the number of epoch images in the collection
        /// </summary>
        public int Count => _images.Count;

        /// <summary>
        /// Gets a value indicating whether the collection is read-only
        /// </summary>
        public bool IsReadOnly => false;

        /// <summary>
        /// Gets or sets the epoch image at the specified index
        /// </summary>
        /// <param name="index">The zero-based index of the epoch image</param>
        /// <returns>The epoch image at the specified index</returns>
        public IEpochImage this[int index]
        {
            get
            {
                if (index < 0 || index >= _images.Count)
                    throw new ArgumentOutOfRangeException(nameof(index));
                return _images[index];
            }
            set
            {
                if (index < 0 || index >= _images.Count)
                    throw new ArgumentOutOfRangeException(nameof(index));

                var oldImage = _images[index];
                _images[index] = value ?? throw new ArgumentNullException(nameof(value));

                OnImageRemoved(oldImage);
                OnImageAdded(value);
            }
        }

        /// <summary>
        /// Gets the start time of the epoch collection
        /// </summary>
        public DateTime StartTime
        {
            get
            {
                if (_images.Count == 0)
                    return DateTime.MinValue;
                return _images.Min(img => img.Timestamp);
            }
        }

        /// <summary>
        /// Gets the end time of the epoch collection
        /// </summary>
        public DateTime EndTime
        {
            get
            {
                if (_images.Count == 0)
                    return DateTime.MinValue;
                return _images.Max(img => img.Timestamp);
            }
        }

        /// <summary>
        /// Gets the total duration of the epoch collection
        /// </summary>
        public TimeSpan Duration
        {
            get
            {
                if (_images.Count == 0)
                    return TimeSpan.Zero;
                return EndTime - StartTime;
            }
        }

        /// <summary>
        /// Gets the current epoch image
        /// </summary>
        public IEpochImage CurrentImage
        {
            get
            {
                if (_currentIndex >= 0 && _currentIndex < _images.Count)
                    return _images[_currentIndex];
                return null;
            }
        }

        /// <summary>
        /// Gets the current epoch index
        /// </summary>
        public int CurrentIndex => _currentIndex;

        #endregion

        #region Events

        /// <summary>
        /// Occurs when an epoch image is added to the collection
        /// </summary>
        public event EventHandler<EpochImageEventArgs> ImageAdded;

        /// <summary>
        /// Occurs when an epoch image is removed from the collection
        /// </summary>
        public event EventHandler<EpochImageEventArgs> ImageRemoved;

        /// <summary>
        /// Occurs when the current epoch image changes
        /// </summary>
        public event EventHandler<CurrentImageChangedEventArgs> CurrentImageChanged;

        /// <summary>
        /// Occurs when the collection is cleared
        /// </summary>
        public event EventHandler CollectionCleared;

        #endregion

        #region Collection Methods

        /// <summary>
        /// Adds an epoch image to the collection
        /// </summary>
        /// <param name="epochImage">The epoch image to add</param>
        public void Add(IEpochImage epochImage)
        {
            if (epochImage == null)
                throw new ArgumentNullException(nameof(epochImage));

            _images.Add(epochImage);

            // Set as current if it's the first image
            if (_images.Count == 1)
                _currentIndex = 0;

            OnImageAdded(epochImage);
        }

        /// <summary>
        /// Adds an epoch image with the specified timestamp and image data
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        /// <param name="imageData">The image data</param>
        /// <returns>The created epoch image</returns>
        public IEpochImage Add(DateTime timestamp, byte[] imageData)
        {
            var epochImage = new EpochImage(timestamp, imageData);
            Add(epochImage);
            return epochImage;
        }

        /// <summary>
        /// Adds an epoch image with the specified timestamp and image
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        /// <param name="image">The image</param>
        /// <returns>The created epoch image</returns>
        public IEpochImage Add(DateTime timestamp, Image image)
        {
            var epochImage = new EpochImage(timestamp, image);
            Add(epochImage);
            return epochImage;
        }

        /// <summary>
        /// Inserts an epoch image at the specified index
        /// </summary>
        /// <param name="index">The zero-based index at which to insert the epoch image</param>
        /// <param name="epochImage">The epoch image to insert</param>
        public void Insert(int index, IEpochImage epochImage)
        {
            if (epochImage == null)
                throw new ArgumentNullException(nameof(epochImage));
            if (index < 0 || index > _images.Count)
                throw new ArgumentOutOfRangeException(nameof(index));

            _images.Insert(index, epochImage);

            // Adjust current index if necessary
            if (index <= _currentIndex)
                _currentIndex++;

            // Set as current if it's the first image
            if (_images.Count == 1)
                _currentIndex = 0;

            OnImageAdded(epochImage);
        }

        /// <summary>
        /// Removes the specified epoch image from the collection
        /// </summary>
        /// <param name="epochImage">The epoch image to remove</param>
        /// <returns>True if the epoch image was successfully removed; otherwise, false</returns>
        public bool Remove(IEpochImage epochImage)
        {
            if (epochImage == null)
                return false;

            int index = _images.IndexOf(epochImage);
            if (index >= 0)
            {
                RemoveAt(index);
                return true;
            }
            return false;
        }

        /// <summary>
        /// Removes the epoch image at the specified index
        /// </summary>
        /// <param name="index">The zero-based index of the epoch image to remove</param>
        public void RemoveAt(int index)
        {
            if (index < 0 || index >= _images.Count)
                throw new ArgumentOutOfRangeException(nameof(index));

            var removedImage = _images[index];
            _images.RemoveAt(index);

            // Adjust current index
            if (index < _currentIndex)
                _currentIndex--;
            else if (index == _currentIndex)
            {
                if (_currentIndex >= _images.Count)
                    _currentIndex = _images.Count - 1;

                OnCurrentImageChanged();
            }

            OnImageRemoved(removedImage);
        }

        /// <summary>
        /// Removes all epoch images from the collection
        /// </summary>
        public void Clear()
        {
            _images.Clear();
            _currentIndex = -1;
            OnCollectionCleared();
        }

        /// <summary>
        /// Determines whether the collection contains the specified epoch image
        /// </summary>
        /// <param name="epochImage">The epoch image to locate</param>
        /// <returns>True if the epoch image is found; otherwise, false</returns>
        public bool Contains(IEpochImage epochImage)
        {
            return _images.Contains(epochImage);
        }

        /// <summary>
        /// Determines the index of the specified epoch image
        /// </summary>
        /// <param name="epochImage">The epoch image to locate</param>
        /// <returns>The index of the epoch image if found; otherwise, -1</returns>
        public int IndexOf(IEpochImage epochImage)
        {
            return _images.IndexOf(epochImage);
        }

        /// <summary>
        /// Copies the epoch images to an array
        /// </summary>
        /// <param name="array">The array to copy to</param>
        /// <param name="arrayIndex">The starting index in the array</param>
        public void CopyTo(IEpochImage[] array, int arrayIndex)
        {
            _images.CopyTo(array, arrayIndex);
        }

        #endregion

        #region Query Methods

        /// <summary>
        /// Gets the epoch image at the specified timestamp
        /// </summary>
        /// <param name="timestamp">The timestamp to search for</param>
        /// <returns>The epoch image at the specified timestamp, or null if not found</returns>
        public IEpochImage GetImageAtTime(DateTime timestamp)
        {
            return _images.FirstOrDefault(img => img.Timestamp == timestamp);
        }

        /// <summary>
        /// Gets the epoch image closest to the specified timestamp
        /// </summary>
        /// <param name="timestamp">The timestamp to search for</param>
        /// <returns>The epoch image closest to the specified timestamp</returns>
        public IEpochImage GetClosestImage(DateTime timestamp)
        {
            if (_images.Count == 0)
                return null;

            return _images.OrderBy(img => Math.Abs((img.Timestamp - timestamp).Ticks)).First();
        }

        /// <summary>
        /// Gets epoch images within the specified time range
        /// </summary>
        /// <param name="startTime">The start time of the range</param>
        /// <param name="endTime">The end time of the range</param>
        /// <returns>A collection of epoch images within the specified range</returns>
        public IEnumerable<IEpochImage> GetImagesInRange(DateTime startTime, DateTime endTime)
        {
            return _images.Where(img => img.Timestamp >= startTime && img.Timestamp <= endTime);
        }

        #endregion

        #region Navigation Methods

        /// <summary>
        /// Sets the current epoch image by index
        /// </summary>
        /// <param name="index">The index of the epoch image to set as current</param>
        /// <returns>True if the current image was set successfully; otherwise, false</returns>
        public bool SetCurrentImage(int index)
        {
            if (index < 0 || index >= _images.Count)
                return false;

            var oldIndex = _currentIndex;
            _currentIndex = index;

            if (oldIndex != _currentIndex)
                OnCurrentImageChanged();

            return true;
        }

        /// <summary>
        /// Sets the current epoch image by timestamp
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch image to set as current</param>
        /// <returns>True if the current image was set successfully; otherwise, false</returns>
        public bool SetCurrentImage(DateTime timestamp)
        {
            var image = GetImageAtTime(timestamp);
            if (image != null)
            {
                int index = IndexOf(image);
                return SetCurrentImage(index);
            }
            return false;
        }

        /// <summary>
        /// Moves to the next epoch image
        /// </summary>
        /// <returns>True if moved to the next image; otherwise, false</returns>
        public bool MoveNext()
        {
            if (_currentIndex < _images.Count - 1)
            {
                _currentIndex++;
                OnCurrentImageChanged();
                return true;
            }
            return false;
        }

        /// <summary>
        /// Moves to the previous epoch image
        /// </summary>
        /// <returns>True if moved to the previous image; otherwise, false</returns>
        public bool MovePrevious()
        {
            if (_currentIndex > 0)
            {
                _currentIndex--;
                OnCurrentImageChanged();
                return true;
            }
            return false;
        }

        /// <summary>
        /// Moves to the first epoch image
        /// </summary>
        /// <returns>True if moved to the first image; otherwise, false</returns>
        public bool MoveFirst()
        {
            if (_images.Count > 0)
            {
                var oldIndex = _currentIndex;
                _currentIndex = 0;

                if (oldIndex != _currentIndex)
                    OnCurrentImageChanged();

                return true;
            }
            return false;
        }

        /// <summary>
        /// Moves to the last epoch image
        /// </summary>
        /// <returns>True if moved to the last image; otherwise, false</returns>
        public bool MoveLast()
        {
            if (_images.Count > 0)
            {
                var oldIndex = _currentIndex;
                _currentIndex = _images.Count - 1;

                if (oldIndex != _currentIndex)
                    OnCurrentImageChanged();

                return true;
            }
            return false;
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Sorts the epoch images by timestamp
        /// </summary>
        public void Sort()
        {
            _images.Sort((x, y) => x.Timestamp.CompareTo(y.Timestamp));
        }

        /// <summary>
        /// Sorts the epoch images using the specified comparer
        /// </summary>
        /// <param name="comparer">The comparer to use for sorting</param>
        public void Sort(IComparer<IEpochImage> comparer)
        {
            _images.Sort(comparer);
        }

        /// <summary>
        /// Creates a subset of epoch images based on the specified criteria
        /// </summary>
        /// <param name="predicate">The criteria for selecting images</param>
        /// <returns>A new epoch images collection containing the subset</returns>
        public IEpochImages CreateSubset(Func<IEpochImage, bool> predicate)
        {
            var filteredImages = _images.Where(predicate);
            return new EpochImages(filteredImages);
        }

        /// <summary>
        /// Merges another epoch images collection with this one
        /// </summary>
        /// <param name="other">The other epoch images collection to merge</param>
        public void Merge(IEpochImages other)
        {
            if (other == null)
                throw new ArgumentNullException(nameof(other));

            foreach (var image in other)
            {
                Add(image);
            }

            Sort(); // Sort after merging
        }

        /// <summary>
        /// Validates the integrity of the epoch images collection
        /// </summary>
        /// <returns>A validation result</returns>
        public EpochImageValidationResult Validate()
        {
            var result = new EpochImageValidationResult();

            foreach (var image in _images)
            {
                if (!image.Validate())
                {
                    result.InvalidImages.Add(image);
                }
            }

            // Check for duplicate timestamps
            var duplicateTimestamps = _images
                .GroupBy(img => img.Timestamp)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key);

            result.DuplicateTimestamps.AddRange(duplicateTimestamps);
            result.IsValid = result.InvalidImages.Count == 0 && result.DuplicateTimestamps.Count == 0;

            return result;
        }

        #endregion

        #region I/O Methods

        /// <summary>
        /// Loads epoch images from the specified directory
        /// </summary>
        /// <param name="directoryPath">The path to the directory containing epoch images</param>
        /// <returns>The number of images loaded</returns>
        public async Task<int> LoadFromDirectoryAsync(string directoryPath)
        {
            if (string.IsNullOrEmpty(directoryPath) || !Directory.Exists(directoryPath))
                return 0;

            int loadedCount = 0;
            var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".gif", ".tiff" };

            var files = Directory.GetFiles(directoryPath)
                .Where(f => imageExtensions.Contains(Path.GetExtension(f).ToLower()))
                .OrderBy(f => f);

            foreach (var file in files)
            {
                try
                {
                    var timestamp = File.GetCreationTime(file);
                    var epochImage = new EpochImage(timestamp, file);

                    if (await epochImage.LoadAsync())
                    {
                        Add(epochImage);
                        loadedCount++;
                    }
                }
                catch
                {
                    // Skip files that can't be loaded
                }
            }

            Sort(); // Sort by timestamp after loading
            return loadedCount;
        }

        /// <summary>
        /// Saves epoch images to the specified directory
        /// </summary>
        /// <param name="directoryPath">The path to the directory to save images</param>
        /// <returns>The number of images saved</returns>
        public async Task<int> SaveToDirectoryAsync(string directoryPath)
        {
            if (string.IsNullOrEmpty(directoryPath))
                return 0;

            if (!Directory.Exists(directoryPath))
                Directory.CreateDirectory(directoryPath);

            int savedCount = 0;

            foreach (var image in _images)
            {
                try
                {
                    var fileName = $"epoch_{image.Timestamp:yyyyMMdd_HHmmss}_{image.Id}.png";
                    var filePath = Path.Combine(directoryPath, fileName);

                    if (await image.SaveAsync(filePath))
                    {
                        savedCount++;
                    }
                }
                catch
                {
                    // Skip images that can't be saved
                }
            }

            return savedCount;
        }

        /// <summary>
        /// Exports epoch images to the specified format
        /// </summary>
        /// <param name="filePath">The path to the export file</param>
        /// <param name="format">The export format</param>
        /// <returns>True if export was successful; otherwise, false</returns>
        public async Task<bool> ExportAsync(string filePath, EpochImageExportFormat format)
        {
            try
            {
                switch (format)
                {
                    case EpochImageExportFormat.Xml:
                        return await ExportToXmlAsync(filePath);
                    case EpochImageExportFormat.Json:
                        return await ExportToJsonAsync(filePath);
                    case EpochImageExportFormat.Csv:
                        return await ExportToCsvAsync(filePath);
                    default:
                        return false;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Imports epoch images from the specified file
        /// </summary>
        /// <param name="filePath">The path to the import file</param>
        /// <returns>The number of images imported</returns>
        public async Task<int> ImportAsync(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return 0;

            try
            {
                var extension = Path.GetExtension(filePath).ToLower();

                switch (extension)
                {
                    case ".xml":
                        return await ImportFromXmlAsync(filePath);
                    case ".json":
                        return await ImportFromJsonAsync(filePath);
                    case ".csv":
                        return await ImportFromCsvAsync(filePath);
                    default:
                        return 0;
                }
            }
            catch
            {
                return 0;
            }
        }

        #endregion

        #region IEnumerable Implementation

        /// <summary>
        /// Returns an enumerator that iterates through the collection
        /// </summary>
        /// <returns>An enumerator for the collection</returns>
        public IEnumerator<IEpochImage> GetEnumerator()
        {
            return _images.GetEnumerator();
        }

        /// <summary>
        /// Returns an enumerator that iterates through the collection
        /// </summary>
        /// <returns>An enumerator for the collection</returns>
        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Raises the ImageAdded event
        /// </summary>
        /// <param name="image">The image that was added</param>
        protected virtual void OnImageAdded(IEpochImage image)
        {
            ImageAdded?.Invoke(this, new EpochImageEventArgs(image));
        }

        /// <summary>
        /// Raises the ImageRemoved event
        /// </summary>
        /// <param name="image">The image that was removed</param>
        protected virtual void OnImageRemoved(IEpochImage image)
        {
            ImageRemoved?.Invoke(this, new EpochImageEventArgs(image));
        }

        /// <summary>
        /// Raises the CurrentImageChanged event
        /// </summary>
        protected virtual void OnCurrentImageChanged()
        {
            var args = new CurrentImageChangedEventArgs(CurrentImage, _currentIndex);
            CurrentImageChanged?.Invoke(this, args);
        }

        /// <summary>
        /// Raises the CollectionCleared event
        /// </summary>
        protected virtual void OnCollectionCleared()
        {
            CollectionCleared?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region Private Helper Methods

        private async Task<bool> ExportToXmlAsync(string filePath)
        {
            // Implementation for XML export
            return await Task.FromResult(true);
        }

        private async Task<bool> ExportToJsonAsync(string filePath)
        {
            // Implementation for JSON export
            return await Task.FromResult(true);
        }

        private async Task<bool> ExportToCsvAsync(string filePath)
        {
            // Implementation for CSV export
            return await Task.FromResult(true);
        }

        private async Task<int> ImportFromXmlAsync(string filePath)
        {
            // Implementation for XML import
            return await Task.FromResult(0);
        }

        private async Task<int> ImportFromJsonAsync(string filePath)
        {
            // Implementation for JSON import
            return await Task.FromResult(0);
        }

        private async Task<int> ImportFromCsvAsync(string filePath)
        {
            // Implementation for CSV import
            return await Task.FromResult(0);
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the epoch images collection
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the epoch images collection
        /// </summary>
        /// <param name="disposing">True if disposing; otherwise, false</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                foreach (var image in _images)
                {
                    image?.Dispose();
                }

                _images.Clear();
                _disposed = true;
            }
        }

        #endregion
    }
}