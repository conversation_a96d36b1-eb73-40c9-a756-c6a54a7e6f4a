using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Threading.Tasks;

namespace FS.AS.Core.Imaging
{
    /// <summary>
    /// Interface for managing epoch-based images
    /// </summary>
    public interface IEpochImages : IEnumerable<IEpochImage>, IDisposable
    {
        #region Properties

        /// <summary>
        /// Gets the number of epoch images in the collection
        /// </summary>
        int Count { get; }

        /// <summary>
        /// Gets a value indicating whether the collection is read-only
        /// </summary>
        bool IsReadOnly { get; }

        /// <summary>
        /// Gets or sets the epoch image at the specified index
        /// </summary>
        /// <param name="index">The zero-based index of the epoch image</param>
        /// <returns>The epoch image at the specified index</returns>
        IEpochImage this[int index] { get; set; }

        /// <summary>
        /// Gets the start time of the epoch collection
        /// </summary>
        DateTime StartTime { get; }

        /// <summary>
        /// Gets the end time of the epoch collection
        /// </summary>
        DateTime EndTime { get; }

        /// <summary>
        /// Gets the total duration of the epoch collection
        /// </summary>
        TimeSpan Duration { get; }

        /// <summary>
        /// Gets the current epoch image
        /// </summary>
        IEpochImage CurrentImage { get; }

        /// <summary>
        /// Gets the current epoch index
        /// </summary>
        int CurrentIndex { get; }

        #endregion

        #region Collection Methods

        /// <summary>
        /// Adds an epoch image to the collection
        /// </summary>
        /// <param name="epochImage">The epoch image to add</param>
        void Add(IEpochImage epochImage);

        /// <summary>
        /// Adds an epoch image with the specified timestamp and image data
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        /// <param name="imageData">The image data</param>
        /// <returns>The created epoch image</returns>
        IEpochImage Add(DateTime timestamp, byte[] imageData);

        /// <summary>
        /// Adds an epoch image with the specified timestamp and image
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch</param>
        /// <param name="image">The image</param>
        /// <returns>The created epoch image</returns>
        IEpochImage Add(DateTime timestamp, Image image);

        /// <summary>
        /// Inserts an epoch image at the specified index
        /// </summary>
        /// <param name="index">The zero-based index at which to insert the epoch image</param>
        /// <param name="epochImage">The epoch image to insert</param>
        void Insert(int index, IEpochImage epochImage);

        /// <summary>
        /// Removes the specified epoch image from the collection
        /// </summary>
        /// <param name="epochImage">The epoch image to remove</param>
        /// <returns>True if the epoch image was successfully removed; otherwise, false</returns>
        bool Remove(IEpochImage epochImage);

        /// <summary>
        /// Removes the epoch image at the specified index
        /// </summary>
        /// <param name="index">The zero-based index of the epoch image to remove</param>
        void RemoveAt(int index);

        /// <summary>
        /// Removes all epoch images from the collection
        /// </summary>
        void Clear();

        /// <summary>
        /// Determines whether the collection contains the specified epoch image
        /// </summary>
        /// <param name="epochImage">The epoch image to locate</param>
        /// <returns>True if the epoch image is found; otherwise, false</returns>
        bool Contains(IEpochImage epochImage);

        /// <summary>
        /// Determines the index of the specified epoch image
        /// </summary>
        /// <param name="epochImage">The epoch image to locate</param>
        /// <returns>The index of the epoch image if found; otherwise, -1</returns>
        int IndexOf(IEpochImage epochImage);

        /// <summary>
        /// Copies the epoch images to an array
        /// </summary>
        /// <param name="array">The array to copy to</param>
        /// <param name="arrayIndex">The starting index in the array</param>
        void CopyTo(IEpochImage[] array, int arrayIndex);

        #endregion

        #region Query Methods

        /// <summary>
        /// Gets the epoch image at the specified timestamp
        /// </summary>
        /// <param name="timestamp">The timestamp to search for</param>
        /// <returns>The epoch image at the specified timestamp, or null if not found</returns>
        IEpochImage GetImageAtTime(DateTime timestamp);

        /// <summary>
        /// Gets the epoch image closest to the specified timestamp
        /// </summary>
        /// <param name="timestamp">The timestamp to search for</param>
        /// <returns>The epoch image closest to the specified timestamp</returns>
        IEpochImage GetClosestImage(DateTime timestamp);

        /// <summary>
        /// Gets epoch images within the specified time range
        /// </summary>
        /// <param name="startTime">The start time of the range</param>
        /// <param name="endTime">The end time of the range</param>
        /// <returns>A collection of epoch images within the specified range</returns>
        IEnumerable<IEpochImage> GetImagesInRange(DateTime startTime, DateTime endTime);

        #endregion

        #region Navigation Methods

        /// <summary>
        /// Sets the current epoch image by index
        /// </summary>
        /// <param name="index">The index of the epoch image to set as current</param>
        /// <returns>True if the current image was set successfully; otherwise, false</returns>
        bool SetCurrentImage(int index);

        /// <summary>
        /// Sets the current epoch image by timestamp
        /// </summary>
        /// <param name="timestamp">The timestamp of the epoch image to set as current</param>
        /// <returns>True if the current image was set successfully; otherwise, false</returns>
        bool SetCurrentImage(DateTime timestamp);

        /// <summary>
        /// Moves to the next epoch image
        /// </summary>
        /// <returns>True if moved to the next image; otherwise, false</returns>
        bool MoveNext();

        /// <summary>
        /// Moves to the previous epoch image
        /// </summary>
        /// <returns>True if moved to the previous image; otherwise, false</returns>
        bool MovePrevious();

        /// <summary>
        /// Moves to the first epoch image
        /// </summary>
        /// <returns>True if moved to the first image; otherwise, false</returns>
        bool MoveFirst();

        /// <summary>
        /// Moves to the last epoch image
        /// </summary>
        /// <returns>True if moved to the last image; otherwise, false</returns>
        bool MoveLast();

        #endregion

        #region Utility Methods

        /// <summary>
        /// Sorts the epoch images by timestamp
        /// </summary>
        void Sort();

        /// <summary>
        /// Sorts the epoch images using the specified comparer
        /// </summary>
        /// <param name="comparer">The comparer to use for sorting</param>
        void Sort(IComparer<IEpochImage> comparer);

        /// <summary>
        /// Creates a subset of epoch images based on the specified criteria
        /// </summary>
        /// <param name="predicate">The criteria for selecting images</param>
        /// <returns>A new epoch images collection containing the subset</returns>
        IEpochImages CreateSubset(Func<IEpochImage, bool> predicate);

        /// <summary>
        /// Merges another epoch images collection with this one
        /// </summary>
        /// <param name="other">The other epoch images collection to merge</param>
        void Merge(IEpochImages other);

        /// <summary>
        /// Validates the integrity of the epoch images collection
        /// </summary>
        /// <returns>A validation result</returns>
        EpochImageValidationResult Validate();

        #endregion

        #region I/O Methods

        /// <summary>
        /// Loads epoch images from the specified directory
        /// </summary>
        /// <param name="directoryPath">The path to the directory containing epoch images</param>
        /// <returns>The number of images loaded</returns>
        Task<int> LoadFromDirectoryAsync(string directoryPath);

        /// <summary>
        /// Saves epoch images to the specified directory
        /// </summary>
        /// <param name="directoryPath">The path to the directory to save images</param>
        /// <returns>The number of images saved</returns>
        Task<int> SaveToDirectoryAsync(string directoryPath);

        /// <summary>
        /// Exports epoch images to the specified format
        /// </summary>
        /// <param name="filePath">The path to the export file</param>
        /// <param name="format">The export format</param>
        /// <returns>True if export was successful; otherwise, false</returns>
        Task<bool> ExportAsync(string filePath, EpochImageExportFormat format);

        /// <summary>
        /// Imports epoch images from the specified file
        /// </summary>
        /// <param name="filePath">The path to the import file</param>
        /// <returns>The number of images imported</returns>
        Task<int> ImportAsync(string filePath);

        #endregion

        #region Events

        /// <summary>
        /// Occurs when an epoch image is added to the collection
        /// </summary>
        event EventHandler<EpochImageEventArgs> ImageAdded;

        /// <summary>
        /// Occurs when an epoch image is removed from the collection
        /// </summary>
        event EventHandler<EpochImageEventArgs> ImageRemoved;

        /// <summary>
        /// Occurs when the current epoch image changes
        /// </summary>
        event EventHandler<CurrentImageChangedEventArgs> CurrentImageChanged;

        /// <summary>
        /// Occurs when the collection is cleared
        /// </summary>
        event EventHandler CollectionCleared;

        #endregion
    }
}
