using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing.Design;
using System.Globalization;
using System.IO;
using System.Xml;

namespace FS.AS.Core.RobotDeck
{
    /// <summary>
    /// Complete .NET implementation of XCMElement class
    /// Represents a deck element that can be placed on a robot deck
    /// Uses C# 7.3 features
    /// </summary>
    [Serializable]
    public class XCMElement : IDeckElement
    {
        #region Private Fields

        private string name_ = string.Empty;
        private int width_ = 1;
        private int startUnit_ = 1;
        private string imageFilename_ = string.Empty;
        private Collection<DeckSubElement> deckSubElements_;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the XCMElement class
        /// </summary>
        public XCMElement()
        {
            deckSubElements_ = new Collection<DeckSubElement>();
        }

        #endregion

        #region IDeckElement Implementation

        /// <summary>
        /// The name of the deck element
        /// </summary>
        [DisplayName("Name")]
        [Description("The name of the deck element")]
        [Category("Element Properties")]
        public string Name
        {
            get => name_;
            set => name_ = value ?? string.Empty;
        }

        /// <summary>
        /// The width of the deck element in deck units
        /// </summary>
        [DisplayName("Width (units)")]
        [Description("The width of the deck element in deck units")]
        [Category("Element Properties")]
        public int Width
        {
            get => width_;
            set
            {
                if (value < 1)
                    width_ = 1;
                else if (value > 50)
                    width_ = 50;
                else
                    width_ = value;
            }
        }

        /// <summary>
        /// The starting unit position of the deck element (1-based)
        /// </summary>
        [DisplayName("Start Unit")]
        [Description("The starting unit position of the deck element (1-based)")]
        [Category("Element Properties")]
        public int StartUnit
        {
            get => startUnit_;
            set
            {
                if (value < 1)
                    startUnit_ = 1;
                else
                    startUnit_ = value;
            }
        }

        /// <summary>
        /// The filename of the image associated with this deck element
        /// </summary>
        [DisplayName("Image Filename")]
        [Description("The filename of the image associated with this deck element")]
        [Category("Element Properties")]
        public string ImageFilename
        {
            get => imageFilename_;
            set => imageFilename_ = value ?? string.Empty;
        }

        /// <summary>
        /// The collection of sub-elements associated with this deck element
        /// </summary>
        [DisplayName("Sub-Elements")]
        [Description("The collection of sub-elements associated with this deck element")]
        [Category("Sub-Elements")]
        [Editor(typeof(CollectionEditor), typeof(UITypeEditor))]
        public Collection<DeckSubElement> DeckSubElements => deckSubElements_;

        #endregion

        #region XML Serialization

        /// <summary>
        /// Gets the XML representation of the deck element
        /// </summary>
        /// <returns>XML string representing the deck element</returns>
        public string GetXML()
        {
            using (var sw = new StringWriter())
            {
                using (var xmlWriter = new XmlTextWriter(sw))
                {
                    xmlWriter.Formatting = Formatting.None;
                    xmlWriter.WriteStartDocument();
                    WriteXML(xmlWriter);
                    xmlWriter.WriteEndDocument();
                }
                return sw.ToString();
            }
        }

        /// <summary>
        /// Sets the deck element configuration from XML
        /// </summary>
        /// <param name="xml">XML string containing element configuration</param>
        public void SetXML(string xml)
        {
            if (string.IsNullOrEmpty(xml))
                throw new ArgumentException("XML cannot be null or empty", nameof(xml));

            try
            {
                using (var sr = new StringReader(xml))
                {
                    using (var xmlReader = new XmlTextReader(sr))
                    {
                        ReadXML(xmlReader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to parse XML: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Writes the deck element configuration to an XML writer
        /// </summary>
        /// <param name="xmlWriter">XML writer to write to</param>
        public void WriteXML(XmlWriter xmlWriter)
        {
            if (xmlWriter == null)
                throw new ArgumentNullException(nameof(xmlWriter));

            xmlWriter.WriteStartElement("Symyx.AutomationStudio.RobotDeck.XCMElement_RobotDeck");

            // Write element properties
            xmlWriter.WriteElementString("n", Name);
            xmlWriter.WriteElementString("Width", Width.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("StartUnit", StartUnit.ToString(CultureInfo.InvariantCulture));
            xmlWriter.WriteElementString("ImageFilename", ImageFilename);

            // Write sub-elements
            if (DeckSubElements.Count > 0)
            {
                xmlWriter.WriteStartElement("Symyx.AutomationStudio.RobotDeck.DeckSubElements");
                foreach (var subElement in DeckSubElements)
                {
                    subElement.WriteXML(xmlWriter);
                }
                xmlWriter.WriteEndElement();
            }

            xmlWriter.WriteEndElement();
        }

        /// <summary>
        /// Reads the deck element configuration from an XML reader
        /// </summary>
        /// <param name="xmlRd">XML reader to read from</param>
        public void ReadXML(XmlReader xmlRd)
        {
            if (xmlRd == null)
                throw new ArgumentNullException(nameof(xmlRd));

            try
            {
                // Clear existing sub-elements
                DeckSubElements.Clear();

                while (xmlRd.Read())
                {
                    if (xmlRd.IsStartElement())
                    {
                        switch (xmlRd.Name)
                        {
                            case "n":
                                Name = xmlRd.ReadElementContentAsString();
                                break;

                            case "Width":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int width))
                                    Width = width;
                                break;

                            case "StartUnit":
                                if (int.TryParse(xmlRd.ReadElementContentAsString(), NumberStyles.Integer, CultureInfo.InvariantCulture, out int startUnit))
                                    StartUnit = startUnit;
                                break;

                            case "ImageFilename":
                                ImageFilename = xmlRd.ReadElementContentAsString();
                                break;

                            case "Symyx.AutomationStudio.RobotDeck.DeckSubElements":
                                ReadDeckSubElements(xmlRd);
                                break;
                        }
                    }
                    else if (xmlRd.NodeType == XmlNodeType.EndElement && xmlRd.Name == "Symyx.AutomationStudio.RobotDeck.XCMElement_RobotDeck")
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to read XML: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Reads deck sub-elements from XML
        /// </summary>
        /// <param name="xmlRd">XML reader positioned at DeckSubElements</param>
        private void ReadDeckSubElements(XmlReader xmlRd)
        {
            while (xmlRd.Read() && xmlRd.Name != "Symyx.AutomationStudio.RobotDeck.DeckSubElements")
            {
                if (xmlRd.IsStartElement())
                {
                    var subElement = new DeckSubElement();
                    subElement.ReadXML(xmlRd);
                    DeckSubElements.Add(subElement);
                }
            }
        }

        #endregion

        #region Validation and Utilities

        /// <summary>
        /// Validates the deck element configuration
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            try
            {
                // Basic validation
                if (Width <= 0 || StartUnit <= 0)
                    return false;

                if (string.IsNullOrEmpty(Name))
                    return false;

                // Validate sub-elements
                foreach (var subElement in DeckSubElements)
                {
                    if (subElement.Position <= 0)
                        return false;

                    if (string.IsNullOrEmpty(subElement.Name))
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the end unit position of this element
        /// </summary>
        /// <returns>The last unit position occupied by this element</returns>
        public int GetEndUnit()
        {
            return StartUnit + Width - 1;
        }

        /// <summary>
        /// Checks if this element occupies the specified unit
        /// </summary>
        /// <param name="unitNumber">Unit number to check</param>
        /// <returns>True if the element occupies the unit, false otherwise</returns>
        public bool OccupiesUnit(int unitNumber)
        {
            return unitNumber >= StartUnit && unitNumber <= GetEndUnit();
        }

        /// <summary>
        /// Creates a deep copy of the element
        /// </summary>
        /// <returns>Deep copy of the element</returns>
        public XCMElement Clone()
        {
            var xml = GetXML();
            var clone = new XCMElement();
            clone.SetXML(xml);
            return clone;
        }

        #endregion

        #region Object Overrides

        /// <summary>
        /// Returns a string representation of the element
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"XCMElement: {Name} (Units {StartUnit}-{GetEndUnit()}, {DeckSubElements.Count} sub-elements)";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">Object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object obj)
        {
            if (obj is XCMElement other)
            {
                return Name == other.Name &&
                       Width == other.Width &&
                       StartUnit == other.StartUnit &&
                       ImageFilename == other.ImageFilename;
            }
            return false;
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>Hash code for the current object</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(Name, Width, StartUnit, ImageFilename);
        }

        #endregion
    }
}
