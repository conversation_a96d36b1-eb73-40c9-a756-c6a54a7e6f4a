using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using FS.AS.Core.Imaging;
using FS.AS.Core.LSDesignHelper.Net.HelperClasses;

namespace FS.AS.Core.LSDesignHelper.Net.Examples
{
    /// <summary>
    /// Example usage of the rewritten DeckHelper SaveImage methods
    /// </summary>
    public class DeckHelperUsageExample
    {
        /// <summary>
        /// Demonstrates various ways to use the enhanced DeckHelper
        /// </summary>
        public static async Task RunExamples()
        {
            Console.WriteLine("=== DeckHelper SaveImage Examples ===");
            Console.WriteLine();

            // Example 1: Basic deck image saving
            await Example1_BasicDeckImageSaving();

            // Example 2: Deck layout saving
            await Example2_DeckLayoutSaving();

            // Example 3: Advanced image saving with custom properties
            await Example3_AdvancedImageSaving();

            // Example 4: Error handling scenarios
            await Example4_ErrorHandling();

            // Example 5: Batch operations
            await Example5_BatchOperations();
        }

        private static async Task Example1_BasicDeckImageSaving()
        {
            Console.WriteLine("1. Basic Deck Image Saving");
            Console.WriteLine("--------------------------");

            try
            {
                // Save a deck image file (async version)
                var imageId1 = await DeckHelper.SaveDeckImageAsync("deck_layout_001.jpg");
                Console.WriteLine($"   Async: Saved deck image with ID: {imageId1}");

                // Save a deck image file (sync version for backward compatibility)
                var imageId2 = DeckHelper.SaveDeckImage("deck_layout_002.jpg");
                Console.WriteLine($"   Sync:  Saved deck image with ID: {imageId2}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static async Task Example2_DeckLayoutSaving()
        {
            Console.WriteLine("2. Deck Layout Saving");
            Console.WriteLine("--------------------");

            try
            {
                // Save current deck layout (async version)
                var layoutId1 = await DeckHelper.SaveDeckLayoutAsync();
                Console.WriteLine($"   Async: Saved deck layout with ID: {layoutId1}");

                // Save current deck layout (sync version)
                var layoutId2 = DeckHelper.SaveDeckLayout();
                Console.WriteLine($"   Sync:  Saved deck layout with ID: {layoutId2}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static async Task Example3_AdvancedImageSaving()
        {
            Console.WriteLine("3. Advanced Image Saving with Custom Properties");
            Console.WriteLine("-----------------------------------------------");

            try
            {
                // Create a custom deck image
                using (var bitmap = CreateSampleDeckBitmap())
                {
                    // Create EpochImage with custom properties
                    var epochImage = new EpochImage(DateTime.Now, bitmap);
                    epochImage.Name = "Custom Deck Layout";
                    epochImage.Description = "Programmatically generated deck layout";
                    epochImage.Quality = 95; // High quality
                    epochImage.CompressionType = "JPEG";

                    // Add custom metadata
                    epochImage.Metadata["CreatedBy"] = "DeckHelper Example";
                    epochImage.Metadata["Version"] = "1.0";
                    epochImage.Metadata["DeckType"] = "Standard";
                    epochImage.Metadata["Resolution"] = "High";

                    // Add custom tags
                    epochImage.Tags.Add("custom");
                    epochImage.Tags.Add("programmatic");
                    epochImage.Tags.Add("high-quality");

                    // Save using the enhanced method (this would need the private method to be made internal/public)
                    // var imageId = await DeckHelper.SaveImageAsync(epochImage);
                    // Console.WriteLine($"   Saved custom deck image with ID: {imageId}");

                    Console.WriteLine("   Custom EpochImage created with:");
                    Console.WriteLine($"     Name: {epochImage.Name}");
                    Console.WriteLine($"     Description: {epochImage.Description}");
                    Console.WriteLine($"     Quality: {epochImage.Quality}");
                    Console.WriteLine($"     Size: {epochImage.Width}x{epochImage.Height}");
                    Console.WriteLine($"     Metadata count: {epochImage.Metadata.Count}");
                    Console.WriteLine($"     Tags: {string.Join(", ", epochImage.Tags)}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static async Task Example4_ErrorHandling()
        {
            Console.WriteLine("4. Error Handling Scenarios");
            Console.WriteLine("---------------------------");

            // Test various error conditions
            var errorScenarios = new[]
            {
                ("Null filename", async () => await DeckHelper.SaveDeckImageAsync(null)),
                ("Empty filename", async () => await DeckHelper.SaveDeckImageAsync("")),
                ("Non-existent file", async () => await DeckHelper.SaveDeckImageAsync("non_existent_file.jpg")),
                ("Invalid path characters", async () => await DeckHelper.SaveDeckImageAsync("invalid<>file.jpg"))
            };

            foreach (var (scenarioName, action) in errorScenarios)
            {
                try
                {
                    await action();
                    Console.WriteLine($"   {scenarioName}: Unexpected success");
                }
                catch (ArgumentException ex)
                {
                    Console.WriteLine($"   {scenarioName}: ✓ Caught ArgumentException - {ex.Message}");
                }
                catch (FileNotFoundException ex)
                {
                    Console.WriteLine($"   {scenarioName}: ✓ Caught FileNotFoundException - {ex.Message}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   {scenarioName}: ✓ Caught Exception - {ex.GetType().Name}: {ex.Message}");
                }
            }

            Console.WriteLine();
        }

        private static async Task Example5_BatchOperations()
        {
            Console.WriteLine("5. Batch Operations");
            Console.WriteLine("------------------");

            try
            {
                // Simulate batch saving of multiple deck images
                var imageFiles = new[]
                {
                    "deck_position_A1.jpg",
                    "deck_position_B2.jpg",
                    "deck_position_C3.jpg"
                };

                Console.WriteLine("   Batch saving deck images:");
                var savedIds = new List<int>();

                foreach (var imageFile in imageFiles)
                {
                    try
                    {
                        // In a real scenario, these files would exist
                        // For demo purposes, we'll just show the pattern
                        Console.WriteLine($"     Would save: {imageFile}");
                        
                        // Simulate the save operation
                        await Task.Delay(50); // Simulate processing time
                        var mockId = new Random().Next(1000, 9999);
                        savedIds.Add(mockId);
                        
                        Console.WriteLine($"     Saved with ID: {mockId}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"     Failed to save {imageFile}: {ex.Message}");
                    }
                }

                Console.WriteLine($"   Batch operation completed. Saved {savedIds.Count} images.");
                Console.WriteLine($"   Image IDs: {string.Join(", ", savedIds)}");

                // Demonstrate parallel processing
                Console.WriteLine("\n   Parallel batch processing:");
                var parallelTasks = imageFiles.Select(async file =>
                {
                    await Task.Delay(new Random().Next(10, 100)); // Simulate variable processing time
                    var mockId = new Random().Next(1000, 9999);
                    Console.WriteLine($"     Parallel saved {file} with ID: {mockId}");
                    return mockId;
                });

                var parallelResults = await Task.WhenAll(parallelTasks);
                Console.WriteLine($"   Parallel operation completed. Results: {string.Join(", ", parallelResults)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Batch operation error: {ex.Message}");
            }

            Console.WriteLine();
        }

        #region Helper Methods

        /// <summary>
        /// Creates a sample bitmap for demonstration purposes
        /// </summary>
        /// <returns>A sample deck layout bitmap</returns>
        private static Bitmap CreateSampleDeckBitmap()
        {
            var bitmap = new Bitmap(400, 300);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                // Create a simple deck layout representation
                graphics.Clear(Color.LightGray);

                // Draw deck outline
                using (var pen = new Pen(Color.Black, 2))
                {
                    graphics.DrawRectangle(pen, 10, 10, 380, 280);
                }

                // Draw some deck positions
                using (var brush = new SolidBrush(Color.LightBlue))
                using (var pen = new Pen(Color.DarkBlue, 1))
                {
                    for (int row = 0; row < 3; row++)
                    {
                        for (int col = 0; col < 4; col++)
                        {
                            var x = 30 + col * 90;
                            var y = 30 + row * 80;
                            var rect = new Rectangle(x, y, 70, 60);
                            
                            graphics.FillRectangle(brush, rect);
                            graphics.DrawRectangle(pen, rect);
                            
                            // Add position label
                            var label = $"{(char)('A' + row)}{col + 1}";
                            using (var font = new Font("Arial", 8))
                            using (var textBrush = new SolidBrush(Color.Black))
                            {
                                var textSize = graphics.MeasureString(label, font);
                                var textX = x + (70 - textSize.Width) / 2;
                                var textY = y + (60 - textSize.Height) / 2;
                                graphics.DrawString(label, font, textBrush, textX, textY);
                            }
                        }
                    }
                }

                // Add title
                using (var font = new Font("Arial", 12, FontStyle.Bold))
                using (var brush = new SolidBrush(Color.Black))
                {
                    graphics.DrawString("Sample Deck Layout", font, brush, 150, 5);
                }
            }

            return bitmap;
        }

        /// <summary>
        /// Demonstrates integration with the modern EpochImage system
        /// </summary>
        public static async Task DemonstrateEpochImageIntegration()
        {
            Console.WriteLine("=== EpochImage Integration Demo ===");
            Console.WriteLine();

            try
            {
                // Create an EpochImage
                using (var bitmap = CreateSampleDeckBitmap())
                {
                    var epochImage = new EpochImage(DateTime.Now, bitmap);
                    
                    // Set comprehensive properties
                    epochImage.Name = "Integration Demo Deck";
                    epochImage.Description = "Demonstrates EpochImage integration with DeckHelper";
                    epochImage.Quality = 90;
                    
                    // Add rich metadata
                    epochImage.Metadata["Application"] = "DeckHelper";
                    epochImage.Metadata["Module"] = "LSDesignHelper.Net";
                    epochImage.Metadata["CreatedBy"] = Environment.UserName;
                    epochImage.Metadata["MachineName"] = Environment.MachineName;
                    epochImage.Metadata["ProcessId"] = Environment.ProcessId;
                    epochImage.Metadata["CLRVersion"] = Environment.Version.ToString();
                    
                    // Add comprehensive tags
                    epochImage.Tags.Add("deck");
                    epochImage.Tags.Add("integration");
                    epochImage.Tags.Add("demo");
                    epochImage.Tags.Add("automated");
                    
                    Console.WriteLine("Created EpochImage with properties:");
                    Console.WriteLine($"  ID: {epochImage.Id}");
                    Console.WriteLine($"  Name: {epochImage.Name}");
                    Console.WriteLine($"  Timestamp: {epochImage.Timestamp:yyyy-MM-dd HH:mm:ss}");
                    Console.WriteLine($"  Size: {epochImage.Width}x{epochImage.Height}");
                    Console.WriteLine($"  Format: {epochImage.Format}");
                    Console.WriteLine($"  Quality: {epochImage.Quality}");
                    Console.WriteLine($"  Is Loaded: {epochImage.IsLoaded}");
                    Console.WriteLine($"  Metadata Count: {epochImage.Metadata.Count}");
                    Console.WriteLine($"  Tags: {string.Join(", ", epochImage.Tags)}");
                    
                    // Export to different formats
                    Console.WriteLine("\nExporting to different formats:");
                    var jpegData = epochImage.Export(ImageFormat.Jpeg);
                    var pngData = epochImage.Export(ImageFormat.Png);
                    var bmpData = epochImage.Export(ImageFormat.Bmp);
                    
                    Console.WriteLine($"  JPEG: {jpegData?.Length ?? 0:N0} bytes");
                    Console.WriteLine($"  PNG:  {pngData?.Length ?? 0:N0} bytes");
                    Console.WriteLine($"  BMP:  {bmpData?.Length ?? 0:N0} bytes");
                    
                    // Get image statistics
                    var stats = epochImage.GetStatistics();
                    if (stats != null)
                    {
                        Console.WriteLine("\nImage Statistics:");
                        Console.WriteLine($"  Mean Color: R={stats.Mean.R}, G={stats.Mean.G}, B={stats.Mean.B}");
                        Console.WriteLine($"  Brightness: {stats.BrightnessAverage:F1}");
                        Console.WriteLine($"  Pixel Count: {stats.PixelCount:N0}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Integration demo error: {ex.Message}");
            }
        }

        #endregion
    }
}
